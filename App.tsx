import React, { useState } from 'react';
import { StatusBar, View } from 'expo-status-bar';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import { EnhancedStateProvider } from './src/context/EnhancedStateContext';
import { GlobalStateProvider } from './src/context/ApiIntegratedGlobalStateContext';
import { ThemeProvider } from './src/context/ThemeContext';
import AppNavigator from './src/navigation/AppNavigator';
import DevTools from './src/context/devtools/DevTools';

// Feature flag to enable the new enhanced state management
const USE_ENHANCED_STATE = __DEV__ && false; // Set to true to enable enhanced state management

export default function App() {
  const [showDevTools, setShowDevTools] = useState(false);
  
  const StateProvider = USE_ENHANCED_STATE ? EnhancedStateProvider : GlobalStateProvider;
  
  // In development, you can shake the device or use a debug menu to show dev tools
  React.useEffect(() => {
    if (__DEV__) {
      // You could add a shake gesture detector here or other trigger
      // For now, we'll use a timeout to demonstrate
      const timer = setTimeout(() => {
        if (USE_ENHANCED_STATE) {
          console.log('🛠️ Dev Tools available - Enhanced State Management is active');
          console.log('To show DevTools, call: setShowDevTools(true)');
        }
      }, 2000);
      
      return () => clearTimeout(timer);
    }
  }, []);
  
  return (
    <SafeAreaProvider>
      <ThemeProvider>
        <StateProvider>
          <AppNavigator />
          <StatusBar style="auto" />
          {USE_ENHANCED_STATE && (
            <DevTools 
              visible={showDevTools} 
              onClose={() => setShowDevTools(false)} 
            />
          )}
        </StateProvider>
      </ThemeProvider>
    </SafeAreaProvider>
  );
}
