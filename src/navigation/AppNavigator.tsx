import React, { useState } from 'react';
import { NavigationContainer } from '@react-navigation/native';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import { View, StyleSheet } from 'react-native';
import { useAuth, useApp } from '../context/ApiIntegratedGlobalStateContext';
import { navigationRef, executePendingNavigation } from '../services/AppNavigationService';

// Screens
import LoginScreen from '../screens/LoginScreen';
import DashboardScreen from '../screens/DashboardScreen';
import MapScreen from '../screens/MapScreen';
import BookingListScreen from '../screens/BookingListScreen';
import EarningsScreen from '../screens/EarningsScreen';
import ProfileScreen from '../screens/ProfileScreen';
import BookingsHubScreen from '../screens/BookingsHubScreen';
import ActionsScreen from '../screens/ActionsScreen';
import TripHistoryScreen from '../screens/TripHistoryScreen';

// Components
import AppLayout from '../components/AppLayout';
import SimpleSidebar from '../components/SimpleSidebar';
import IslandBottomNavigation from '../components/IslandBottomNavigation';

const Stack = createNativeStackNavigator<any>();

// Main screen component that switches content based on current screen
const MainNavigator = () => {
  const [activeScreen, setActiveScreen] = useState('dashboard');
  const [isSidebarVisible, setIsSidebarVisible] = useState(false);
  const { state } = useApp();

  // Get screen title based on active screen
  const getScreenTitle = () => {
    switch (activeScreen) {
      case 'bookings':
        return 'Bookings';
      case 'earnings':
        return 'Earnings';
      case 'profile':
        return 'Profile';
      case 'settings':
        return 'Settings';
      case 'help':
        return 'Help & Support';
      default:
        return 'Dashboard';
    }
  };

  // Get screen subtitle based on active screen
  const getScreenSubtitle = () => {
    switch (activeScreen) {
      case 'bookings':
        return `${state.pendingBookings?.length || 0} pending`;
      case 'earnings':
        return 'Today\'s summary';
      default:
        return undefined;
    }
  };

  // Handle navigation from sidebar
  const handleSidebarNavigation = (screen: string) => {
    setActiveScreen(screen);
    setIsSidebarVisible(false);
  };

  // Handle navigation from bottom tabs (only for main 3 screens)
  const handleBottomNavigation = (screen: string) => {
    setActiveScreen(screen);
  };

  // Determine which tab should be active for bottom navigation
  const getActiveBottomTab = () => {
    if (['dashboard'].includes(activeScreen)) return 'dashboard';
    if (['bookings'].includes(activeScreen)) return 'bookings';
    if (['earnings'].includes(activeScreen)) return 'earnings';
    return 'dashboard'; // default
  };

  const screenContent = () => {
    switch (activeScreen) {
      case 'bookings':
        return <BookingsHubScreen />;
      case 'earnings':
        return <EarningsScreen />;
      case 'profile':
        return <ProfileScreen />;
      case 'settings':
        return <ActionsScreen onNavigateToMain={() => setActiveScreen('dashboard')} />;
      case 'help':
        return <ActionsScreen onNavigateToMain={() => setActiveScreen('dashboard')} />;
      default:
        return <DashboardScreen />;
    }
  };

  return (
    <View style={styles.container}>
      <AppLayout
        title={getScreenTitle()}
        subtitle={getScreenSubtitle()}
        onMenuPress={() => setIsSidebarVisible(true)}
        showThemeToggle={true}
      >
        {screenContent()}
      </AppLayout>

      {/* Island Bottom Navigation for main 3 screens */}
      <IslandBottomNavigation
        activeTab={getActiveBottomTab()}
        onTabPress={handleBottomNavigation}
        visible={true}
      />

      <SimpleSidebar
        visible={isSidebarVisible}
        onClose={() => setIsSidebarVisible(false)}
        onNavigate={handleSidebarNavigation}
        currentScreen={activeScreen}
      />
    </View>
  );
};

const AppNavigator = () => {
  const { state: authState } = useAuth();
  const { setNavigationVisibility } = useApp();

  // Hide navigation on login screen
  React.useEffect(() => {
    if (!authState.isAuthenticated) {
      setNavigationVisibility(false);
    } else {
      setNavigationVisibility(true);
    }
  }, [authState.isAuthenticated]);

  // Wrap LoginScreen with AppLayout
  const WrappedLoginScreen = () => (
    <AppLayout title="Login">
      <LoginScreen />
    </AppLayout>
  );

  // Execute any pending navigation actions when the navigator is ready
  const onNavigationReady = () => {
    executePendingNavigation();
  };

  return (
    <NavigationContainer ref={navigationRef} onReady={onNavigationReady}>
      <Stack.Navigator id={undefined} screenOptions={{ headerShown: false }}>
        {authState.isAuthenticated ? (
          <Stack.Screen
            name="Main"
            component={MainNavigator}
          />
        ) : (
          <Stack.Screen
            name="Login"
            component={WrappedLoginScreen}
          />
        )}
      </Stack.Navigator>
    </NavigationContainer>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
  },
});

export default AppNavigator;