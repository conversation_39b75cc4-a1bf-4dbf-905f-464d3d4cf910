import { 
  Driver, 
  Ride, 
  Trip, 
  Location, 
  DriverStats,
  RideLifecycleStatus,
  BookingPriorityStatus,
  RidePriority,
  PaymentMethod,
  VehicleType,
  TransferType,
  FlightStatus
} from '../types';

// Mock Driver Data
export const generateMockDriver = (driverId?: string): Driver => {
  return {
    id: driverId || 'driver-123',
    name: '<PERSON>',
    email: '<EMAIL>',
    phone: '******-0199',
    isOnline: false,
    currentLocation: {
      latitude: 37.7749,
      longitude: -122.4194,
      address: '123 Main St, San Francisco, CA 94102',
    },
    vehicleType: 'sedan',
    licensePlate: 'ABC-123',
    profilePhoto: '',
    acceptanceRate: 94.2,
  };
};

// Mock Available Rides Data
export const generateMockAvailableRides = (): Ride[] => {
  const mockLocations = [
    {
      pickup: { latitude: 37.6213, longitude: -122.3790, address: 'San Francisco International Airport (SFO), Terminal 2' },
      dropoff: { latitude: 37.7749, longitude: -122.4194, address: '123 Market St, San Francisco, CA' },
    },
    {
      pickup: { latitude: 37.7849, longitude: -122.4094, address: '456 Union Square, San Francisco, CA' },
      dropoff: { latitude: 37.6213, longitude: -122.3790, address: 'San Francisco International Airport (SFO), Terminal 1' },
    },
    {
      pickup: { latitude: 37.8044, longitude: -122.2712, address: '789 Broadway, Oakland, CA' },
      dropoff: { latitude: 37.7749, longitude: -122.4194, address: 'Golden Gate Bridge, San Francisco, CA' },
    },
  ];

  const passengerNames = ['Emma Johnson', 'Michael Chen', 'Sarah Williams', 'David Rodriguez', 'Lisa Thompson'];
  const flightNumbers = ['UA123', 'AA456', 'DL789', 'SW101', 'BA202'];
  
  return mockLocations.map((location, index) => ({
    id: `ride-${Date.now()}-${index}`,
    passengerId: `passenger-${index + 1}`,
    passengerName: passengerNames[index % passengerNames.length],
    passengerPhone: `******-01${index}${index}`,
    pickupLocation: location.pickup,
    dropoffLocation: location.dropoff,
    status: 'pending' as RideLifecycleStatus,
    bookingStatus: (index === 0 ? 'premium' : index === 1 ? 'urgent' : 'new') as BookingPriorityStatus,
    fare: Math.round((25 + Math.random() * 75) * 100) / 100, // $25-$100
    distance: Math.round((5 + Math.random() * 25) * 10) / 10, // 5-30 km
    duration: Math.round(15 + Math.random() * 45), // 15-60 minutes
    paymentMethod: (['card', 'cash', 'wallet'][index % 3]) as PaymentMethod,
    createdAt: new Date(),
    priority: (index === 0 ? 'high' : index === 1 ? 'medium' : 'low') as RidePriority,
    transferType: (index === 0 ? 'arrival' : 'departure') as TransferType,
    flightNumber: index === 0 ? flightNumbers[index] : undefined,
    flightTime: index === 0 ? new Date(Date.now() + 45 * 60 * 1000) : undefined,
    scheduledTime: new Date(Date.now() + Math.random() * 30 * 60 * 1000),
    specialRequests: index === 1 ? 'Wheelchair accessible vehicle needed' : undefined,
    vehicleType: (index === 0 ? 'executive' : index === 1 ? 'saloon' : 'estate') as VehicleType,
    passengerCount: Math.floor(Math.random() * 3) + 1,
    hasMeetAndGreet: index === 0,
    airport: index === 0 || index === 1 ? 'San Francisco International (SFO)' : 'None',
  }));
};

// Mock Trip History Data
export const generateMockTrips = (): Trip[] => {
  const trips: Trip[] = [];
  const now = new Date();
  
  for (let i = 0; i < 20; i++) {
    const daysAgo = Math.floor(Math.random() * 30);
    const tripDate = new Date(now.getTime() - daysAgo * 24 * 60 * 60 * 1000);
    
    trips.push({
      id: `trip-${Date.now()}-${i}`,
      rideId: `ride-${Date.now()}-${i}`,
      earnings: Math.round((15 + Math.random() * 35) * 100) / 100, // $15-$50
      distance: Math.round((3 + Math.random() * 15) * 10) / 10, // 3-18 km
      duration: Math.round(20 + Math.random() * 40), // 20-60 minutes
      completedAt: tripDate,
    });
  }
  
  return trips.sort((a, b) => b.completedAt.getTime() - a.completedAt.getTime());
};

// Mock Driver Stats
export const generateMockDriverStats = (): DriverStats => {
  return {
    totalTrips: 1847,
    totalEarnings: 42350.75,
    acceptanceRate: 94.2,
    cancellationRate: 1.8,
    onlineHours: 2240,
  };
};

// Mock Earnings Summary
export const generateMockEarningsSummary = () => {
  return {
    today: 187.50,
    week: 1240.25,
    month: 4850.75,
    year: 42350.75,
    todayTrips: 8,
    weekTrips: 52,
    monthTrips: 198,
    yearTrips: 1847,
    todayHours: 9.5,
    weekHours: 48.2,
    monthHours: 187.5,
    yearHours: 2240,
  };
};

// Mock Real-time Ride Generator
export const generateRandomRideRequest = (): Ride => {
  const airportLocations = [
    { 
      latitude: 37.6213, 
      longitude: -122.3790, 
      address: 'San Francisco International Airport (SFO), Terminal 1' 
    },
    { 
      latitude: 37.6213, 
      longitude: -122.3790, 
      address: 'San Francisco International Airport (SFO), Terminal 3' 
    },
  ];
  
  const cityLocations = [
    { latitude: 37.7749, longitude: -122.4194, address: '123 Market St, San Francisco, CA' },
    { latitude: 37.7849, longitude: -122.4094, address: '456 Union Square, San Francisco, CA' },
    { latitude: 37.8044, longitude: -122.2712, address: '789 Broadway, Oakland, CA' },
    { latitude: 37.7849, longitude: -122.4594, address: '321 Lombard St, San Francisco, CA' },
    { latitude: 37.7649, longitude: -122.4394, address: '654 Mission St, San Francisco, CA' },
  ];
  
  const passengerNames = [
    'Emma Thompson', 'James Wilson', 'Sophia Martinez', 'Liam Anderson', 
    'Olivia Garcia', 'Noah Brown', 'Isabella Davis', 'Lucas Miller'
  ];
  
  const isAirport = Math.random() < 0.3; // 30% chance of airport transfer
  const pickup = isAirport 
    ? airportLocations[Math.floor(Math.random() * airportLocations.length)]
    : cityLocations[Math.floor(Math.random() * cityLocations.length)];
  
  const dropoff = isAirport
    ? cityLocations[Math.floor(Math.random() * cityLocations.length)]
    : Math.random() < 0.2 
      ? airportLocations[Math.floor(Math.random() * airportLocations.length)]
      : cityLocations[Math.floor(Math.random() * cityLocations.length)];

  return {
    id: `ride-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
    passengerId: `passenger-${Math.floor(Math.random() * 1000)}`,
    passengerName: passengerNames[Math.floor(Math.random() * passengerNames.length)],
    passengerPhone: `******-${String(Math.floor(Math.random() * 10000)).padStart(4, '0')}`,
    pickupLocation: pickup,
    dropoffLocation: dropoff,
    status: 'pending' as RideLifecycleStatus,
    bookingStatus: (['new', 'urgent', 'premium'][Math.floor(Math.random() * 3)]) as BookingPriorityStatus,
    fare: Math.round((20 + Math.random() * 80) * 100) / 100,
    distance: Math.round((2 + Math.random() * 25) * 10) / 10,
    duration: Math.round(10 + Math.random() * 50),
    paymentMethod: (['card', 'cash', 'wallet'][Math.floor(Math.random() * 3)]) as PaymentMethod,
    createdAt: new Date(),
    priority: (['low', 'medium', 'high'][Math.floor(Math.random() * 3)]) as RidePriority,
    transferType: (isAirport ? (Math.random() < 0.5 ? 'arrival' : 'departure') : 'arrival') as TransferType,
    flightNumber: isAirport ? `${['UA', 'AA', 'DL', 'SW', 'BA'][Math.floor(Math.random() * 5)]}${Math.floor(Math.random() * 900) + 100}` : undefined,
    flightTime: isAirport ? new Date(Date.now() + (30 + Math.random() * 120) * 60 * 1000) : undefined,
    scheduledTime: new Date(Date.now() + Math.random() * 15 * 60 * 1000),
    vehicleType: (['saloon', 'estate', 'executive', 'people_carrier'][Math.floor(Math.random() * 4)]) as VehicleType,
    passengerCount: Math.floor(Math.random() * 3) + 1,
    hasMeetAndGreet: isAirport && Math.random() < 0.7,
    airport: isAirport ? 'San Francisco International (SFO)' : 'None',
  };
};

// Mock Location Generator
export const generateMockLocation = (): Location => {
  // San Francisco Bay Area coordinates
  const baseLatitude = 37.7749;
  const baseLongitude = -122.4194;
  
  return {
    latitude: baseLatitude + (Math.random() - 0.5) * 0.2, // ±0.1 degrees
    longitude: baseLongitude + (Math.random() - 0.5) * 0.2,
    address: `${Math.floor(Math.random() * 9999) + 1} ${['Main', 'Market', 'Mission', 'Castro', 'Union'][Math.floor(Math.random() * 5)]} St, San Francisco, CA`,
  };
};

// Mock Ride Status Progression
export const getRideStatusProgression = () => [
  'heading_to_pickup',
  'arrived_at_pickup', 
  'passenger_onboard',
  'arrived_at_destination'
];

// Status filtering and validation helpers
export const statusFilters = {
  // Filter rides by lifecycle status
  filterByLifecycleStatus: (rides: Ride[], status: RideLifecycleStatus): Ride[] => {
    return rides.filter(ride => ride.status === status);
  },
  
  // Filter rides by booking priority status
  filterByBookingStatus: (rides: Ride[], status: BookingPriorityStatus): Ride[] => {
    return rides.filter(ride => ride.bookingStatus === status);
  },
  
  // Filter active/available rides (pending status)
  getAvailableRides: (rides: Ride[]): Ride[] => {
    return rides.filter(ride => ride.status === 'pending');
  },
  
  // Filter rejected rides
  getRejectedRides: (rides: Ride[]): Ride[] => {
    return rides.filter(ride => ride.status === 'rejected');
  },
  
  // Filter completed rides
  getCompletedRides: (rides: Ride[]): Ride[] => {
    return rides.filter(ride => ride.status === 'completed');
  },
  
  // Filter rides by priority
  filterByPriority: (rides: Ride[], priority: RidePriority): Ride[] => {
    return rides.filter(ride => ride.priority === priority);
  },
  
  // Get urgent bookings (high priority + urgent booking status)
  getUrgentBookings: (rides: Ride[]): Ride[] => {
    return rides.filter(ride => 
      ride.status === 'pending' && 
      (ride.bookingStatus === 'urgent' || ride.priority === 'high')
    );
  },
  
  // Get scheduled bookings
  getScheduledBookings: (rides: Ride[]): Ride[] => {
    return rides.filter(ride => ride.bookingStatus === 'scheduled');
  }
};

// Status validation helpers
export const statusValidation = {
  isValidLifecycleStatus: (status: string): status is RideLifecycleStatus => {
    return ['pending', 'accepted', 'rejected', 'ongoing', 'completed', 'cancelled'].includes(status);
  },
  
  isValidBookingStatus: (status: string): status is BookingPriorityStatus => {
    return ['new', 'urgent', 'scheduled', 'popular', 'premium'].includes(status);
  },
  
  isValidPriority: (priority: string): priority is RidePriority => {
    return ['low', 'medium', 'high'].includes(priority);
  }
};

// Export all mock generators
export const mockData = {
  generateDriver: generateMockDriver,
  generateAvailableRides: generateMockAvailableRides,
  generateTrips: generateMockTrips,
  generateDriverStats: generateMockDriverStats,
  generateEarningsSummary: generateMockEarningsSummary,
  generateRandomRideRequest: generateRandomRideRequest,
  generateLocation: generateMockLocation,
  getRideStatusProgression,
  statusFilters,
  statusValidation,
};

export default mockData;