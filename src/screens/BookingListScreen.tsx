import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, FlatList, TouchableOpacity, Alert, Modal, Linking } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { MaterialIcons } from '@expo/vector-icons';
import { useApp } from '../context/ApiIntegratedGlobalStateContext';
import Button from '../components/Button';
import Card from '../components/Card';
import BottomSpacer from '../components/BottomSpacer';
import TimePreferenceModal from '../components/TimePreferenceModal';
import ExpandableCustomerDetails from '../components/ExpandableCustomerDetails';
import { Ride } from '../types';
import { colors, shadows } from '../theme/colors';
import { statusFilters } from '../data/mockData';

const BookingListScreen: React.FC = () => {
  const navigation = useNavigation();
  const { 
    state: appState, 
    setCurrentRide, 
    removeAvailableRide, 
    generateMockRides,
    addQueuedBooking,
    setRideStatus,
    setQueueStatus,
    canAcceptNewBooking,
    getMaxQueueSize
  } = useApp();
  const [selectedBooking, setSelectedBooking] = useState<Ride | null>(null);
  const [showDetailsModal, setShowDetailsModal] = useState(false);
  const [availableDrivers, setAvailableDrivers] = useState<any[]>([]);
  const [showDriversList, setShowDriversList] = useState(false);
  const [showTimePreference, setShowTimePreference] = useState(false);
  const [selectedBookingForTime, setSelectedBookingForTime] = useState<Ride | null>(null);
  const [expandedCustomerId, setExpandedCustomerId] = useState<string | null>(null);
  const [expandedBookingId, setExpandedBookingId] = useState<string | null>(null);
  const [isSearching, setIsSearching] = useState(false);

  useEffect(() => {
    // Initialize centralized mock data on mount
    if (!appState.mockDataGenerated) {
      setIsSearching(true);
      
      // Simulate loading
      const loadingTimeout = setTimeout(() => {
        setIsSearching(false);
        generateMockRides();
      }, 1500);

      return () => {
        clearTimeout(loadingTimeout);
      };
    }
  }, [appState.mockDataGenerated, generateMockRides]);

  useEffect(() => {
    // Mock available drivers data
    const mockDrivers = [
      { id: '1', name: 'Sarah Johnson', distance: 0.5, acceptanceRate: 92, completedTrips: 1250 },
      { id: '2', name: 'Mike Wilson', distance: 1.2, acceptanceRate: 88, completedTrips: 980 },
      { id: '3', name: 'David Brown', distance: 2.1, acceptanceRate: 95, completedTrips: 1800 },
      { id: '4', name: 'Lisa Miller', distance: 1.8, acceptanceRate: 90, completedTrips: 1100 },
      { id: '5', name: 'James Davis', distance: 2.8, acceptanceRate: 85, completedTrips: 750 },
      { id: '6', name: 'Emma Wilson', distance: 1.5, acceptanceRate: 93, completedTrips: 1400 },
    ];
    setAvailableDrivers(mockDrivers);
  }, []);


  const getStatusColor = (status: string) => {
    switch (status) {
      case 'new': return colors.primary;
      case 'urgent': return colors.error;
      case 'scheduled': return colors.warning;
      case 'popular': return '#AF52DE';
      case 'premium': return '#FFD700';
      default: return colors.success;
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'new': return '🆕';
      case 'urgent': return '🚨';
      case 'scheduled': return '📅';
      case 'popular': return '🔥';
      case 'premium': return '⭐';
      default: return '📋';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return colors.error;
      case 'medium': return colors.warning;
      case 'low': return colors.success;
      default: return colors.textSecondary;
    }
  };

  const handleAcceptBooking = (booking: Ride) => {
    if (!canAcceptNewBooking()) {
      if (appState.rideStatus === 'passenger_onboard' || appState.rideStatus === 'arrived_at_destination') {
        Alert.alert('Cannot Accept', 'You are currently serving a passenger. Complete your trip to accept new bookings.');
      } else if (appState.queuedBookings.length >= getMaxQueueSize()) {
        Alert.alert('Queue Full', `You already have ${appState.queuedBookings.length} bookings queued. Complete a trip to add more.`);
      } else {
        Alert.alert('Cannot Accept', 'Unable to accept booking at this time.');
      }
      return;
    }

    if (booking.scheduledTime || booking.preferredTime) {
      setSelectedBookingForTime(booking);
      setShowTimePreference(true);
      setShowDetailsModal(false);
    } else {
      Alert.alert(
        'Accept Booking',
        `Accept ride request from ${booking.passengerName}?`,
        [
          { text: 'Cancel', style: 'cancel' },
          {
            text: 'Accept',
            onPress: () => {
              if (appState.currentRide) {
                // Queue the booking
                const currentQueueLength = appState.queuedBookings.length;
                addQueuedBooking(booking);
                removeAvailableRide(booking.id);
                const queuePosition = currentQueueLength + 1;
                Alert.alert(
                  'Booking Queued', 
                  `${booking.passengerName}'s trip has been added to your queue (#${queuePosition}/${getMaxQueueSize()}).`
                );
              } else {
                // Accept as current ride
                setCurrentRide(booking);
                setRideStatus('heading_to_pickup');
                setQueueStatus('limited_queue');
                removeAvailableRide(booking.id);
              }
              setShowDetailsModal(false);
            }
          }
        ]
      );
    }
  };

  const handleTimePreferenceAccept = (booking: Ride) => {
    if (appState.currentRide) {
      addQueuedBooking(booking);
      removeAvailableRide(booking.id);
    } else {
      setCurrentRide(booking);
      setRideStatus('heading_to_pickup');
      setQueueStatus('limited_queue');
      removeAvailableRide(booking.id);
    }
    setShowTimePreference(false);
    setSelectedBookingForTime(null);
  };

  const handleDeclineBooking = (bookingId: string) => {
    Alert.alert(
      'Decline Booking',
      'Are you sure you want to decline this ride request?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Decline',
          style: 'destructive',
          onPress: () => {
            removeAvailableRide(bookingId);
            setShowDetailsModal(false);
            
            // Close expanded details when declining
            if (expandedBookingId === bookingId) {
              setExpandedBookingId(null);
            }
          }
        }
      ]
    );
  };

  const handleCallPassenger = (phoneNumber: string, passengerName: string) => {
    Alert.alert(
      'Call Passenger',
      `Call ${passengerName} at ${phoneNumber}?`,
      [
        { text: 'Cancel', style: 'cancel' },
        { 
          text: 'Call', 
          onPress: () => {
            const phoneUrl = `tel:${phoneNumber}`;
            Linking.canOpenURL(phoneUrl)
              .then(supported => {
                if (supported) {
                  return Linking.openURL(phoneUrl);
                } else {
                  Alert.alert('Error', 'Phone calls are not supported on this device');
                }
              })
              .catch(err => {
                console.error('Error opening phone app:', err);
                Alert.alert('Error', 'Failed to open phone app');
              });
          }
        }
      ]
    );
  };

  const handleMessagePassenger = (phoneNumber: string, passengerName: string) => {
    Alert.alert(
      'Message Passenger',
      `Send SMS to ${passengerName}?`,
      [
        { text: 'Cancel', style: 'cancel' },
        { 
          text: 'Send SMS', 
          onPress: () => {
            const smsUrl = `sms:${phoneNumber}`;
            Linking.canOpenURL(smsUrl)
              .then(supported => {
                if (supported) {
                  return Linking.openURL(smsUrl);
                } else {
                  Alert.alert('Error', 'SMS is not supported on this device');
                }
              })
              .catch(err => {
                console.error('Error opening SMS app:', err);
                Alert.alert('Error', 'Failed to open SMS app');
              });
          }
        }
      ]
    );
  };

  const toggleBookingDetails = (bookingId: string) => {
    setExpandedBookingId(expandedBookingId === bookingId ? null : bookingId);
  };

  const showBookingDetails = (booking: Ride) => {
    setSelectedBooking(booking);
    setShowDetailsModal(true);
  };

  const toggleCustomerDetails = (rideId: string) => {
    setExpandedCustomerId(expandedCustomerId === rideId ? null : rideId);
  };

  const renderBookingItem = ({ item }: { item: Ride }) => (
    <Card style={[styles.bookingCard, item.status === 'rejected' && styles.rejectedBookingCard]}>
      {/* Transfer Request Header */}
      <View style={styles.transferRequestHeader}>
        <Text style={styles.transferRequestTitle}>Airport Transfer</Text>
        <View style={styles.transferRequestHeaderRight}>
          <Text style={[
            styles.transferTypeBadgeCompact,
            item.transferType === 'arrival' ? styles.arrivalBadge : styles.departureBadge
          ]}>
            {item.transferType === 'arrival' ? 'ARRIVAL' : 'DEPARTURE'}
          </Text>
          {item.status === 'rejected' && (
            <View style={styles.rejectedStatusBadge}>
              <MaterialIcons name="cancel" size={14} color={colors.error} />
              <Text style={styles.rejectedStatusText}>REJECTED</Text>
            </View>
          )}
          <TouchableOpacity 
            onPress={() => toggleBookingDetails(item.id)}
            style={styles.expandButton}
            activeOpacity={0.7}
          >
            <MaterialIcons 
              name={expandedBookingId === item.id ? "expand-less" : "expand-more"} 
              size={24} 
              color={colors.textSecondary} 
            />
          </TouchableOpacity>
        </View>
      </View>
      
      {/* Main Booking Info - Dashboard Style */}
      <View style={styles.bookingMainInfo}>
        <View style={styles.passengerMainRow}>
          <View style={styles.passengerMainLeft}>
            <Text style={styles.passengerNameMain}>{item.passengerName}</Text>
            <Text style={styles.flightInfoMain}>{item.flightNumber} • {item.airport}</Text>
          </View>
          <View style={styles.passengerMainRight}>
            <Text style={styles.fareAmountMain}>£{item.fare.toFixed(0)}</Text>
            <Text style={styles.fareDetailsMain}>{item.distance}km • {item.duration}min</Text>
          </View>
        </View>
        
        <View style={styles.locationMainInfo}>
          <View style={styles.locationMainRow}>
            <MaterialIcons name="location-on" size={12} color={colors.success} />
            <Text style={styles.locationMainText} numberOfLines={2}>{item.pickupLocation.address}</Text>
          </View>
          <View style={styles.locationMainRow}>
            <MaterialIcons name="place" size={12} color={colors.error} />
            <Text style={styles.locationMainText} numberOfLines={2}>{item.dropoffLocation.address}</Text>
          </View>
        </View>
      </View>

      {/* Expanded Details */}
      {expandedBookingId === item.id && (
        <View style={styles.rideDetailsExpanded}>
          <View style={styles.expandedRow}>
            <View style={styles.expandedLeft}>
              <Text style={styles.expandedLabel}>Contact</Text>
              <Text style={styles.expandedValue}>{item.passengerPhone}</Text>
              <Text style={styles.expandedValue}>{item.passengerCount > 1 ? `${item.passengerCount} passengers` : '1 passenger'}</Text>
            </View>
            <View style={styles.expandedRight}>
              <Text style={styles.expandedLabel}>Vehicle</Text>
              <Text style={styles.expandedValue}>{item.vehicleType.replace('_', ' ')}</Text>
              <Text style={styles.expandedValue}>{item.paymentMethod.toUpperCase()}</Text>
            </View>
          </View>
          
          {item.specialRequests && (
            <View style={styles.expandedSpecialRequests}>
              <Text style={styles.expandedLabel}>Special Requests</Text>
              <Text style={styles.expandedValue}>{item.specialRequests}</Text>
            </View>
          )}
        </View>
      )}
      
      {/* Communication Actions - Hidden for rejected bookings and when queue is full */}
      {item.status !== 'rejected' && canAcceptNewBooking() && (
        <View style={styles.communicationActions}>
          <TouchableOpacity
            style={styles.callButton}
            onPress={() => handleCallPassenger(item.passengerPhone, item.passengerName)}
            activeOpacity={0.7}
          >
            <MaterialIcons name="phone" size={16} color={colors.textLight} />
            <Text style={styles.actionText}>Call</Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={styles.messageButton}
            onPress={() => handleMessagePassenger(item.passengerPhone, item.passengerName)}
            activeOpacity={0.7}
          >
            <MaterialIcons name="message" size={16} color={colors.textLight} />
            <Text style={styles.actionText}>Message</Text>
          </TouchableOpacity>
        </View>
      )}

      {/* Limited Actions Message - Shown when queue is full but not rejected */}
      {item.status !== 'rejected' && !canAcceptNewBooking() && (
        <View style={styles.limitedActionsContainer}>
          <View style={styles.limitedActionsMessage}>
            <MaterialIcons name="pause-circle-filled" size={16} color={colors.warning} />
            <Text style={styles.limitedActionsText}>
              {appState.rideStatus === 'passenger_onboard' || appState.rideStatus === 'arrived_at_destination' 
                ? 'Complete your current trip to interact with new bookings'
                : `Queue full (${appState.queuedBookings.length}/${getMaxQueueSize()}). Complete a trip to add more.`
              }
            </Text>
          </View>
        </View>
      )}

      {/* Action Buttons */}
      {item.status === 'rejected' ? (
        <View style={styles.rejectedActionsContainer}>
          <View style={styles.rejectedStatusContainer}>
            <MaterialIcons name="info" size={16} color={colors.error} />
            <Text style={styles.rejectedMessage}>This booking was declined and is no longer available</Text>
          </View>
        </View>
      ) : (
        <View style={styles.rideActionsCompact}>
          <Button
            title="Decline"
            onPress={() => handleDeclineBooking(item.id)}
            variant="secondary"
            style={styles.actionButtonCompact}
          />
          <Button
            title={
              !appState.currentRide ? "Accept" :
              canAcceptNewBooking() ? `Queue (${appState.queuedBookings.length + 1}/${getMaxQueueSize()})` :
              "Queue Full"
            }
            onPress={() => handleAcceptBooking(item)}
            variant={canAcceptNewBooking() ? "success" : "secondary"}
            style={styles.actionButtonCompact}
            disabled={!canAcceptNewBooking()}
          />
        </View>
      )}
    </Card>
  );

  // Navigation is now handled by AppNavigator

  return (
    <View style={styles.container}>

      <View style={styles.content}>
        {/* Booking Summary - Hidden when driver has active ride or in service mode */}
        {(() => {
          const availableRides = statusFilters.getAvailableRides(appState.availableRides);
          return !appState.currentRide && (appState.rideStatus !== 'passenger_onboard' && appState.rideStatus !== 'arrived_at_destination') && availableRides.length > 0 && (
            <Card style={styles.summaryCard}>
              <View style={styles.summaryHeader}>
                <MaterialIcons name="pending-actions" size={20} color={colors.primary} />
                <Text style={styles.summaryTitle}>Available Requests</Text>
                <View style={styles.summaryBadge}>
                  <Text style={styles.summaryCount}>{availableRides.length}</Text>
                </View>
              </View>
              <Text style={styles.summarySubtext}>Tap any request to view details and accept</Text>
            </Card>
          );
        })()}

        {/* Service Mode Message - Shown when driver is in service */}
        {appState.currentRide && (appState.rideStatus === 'passenger_onboard' || appState.rideStatus === 'arrived_at_destination') && (
          <Card style={styles.serviceModeCard}>
            <View style={styles.serviceModeContainer}>
              <Text style={styles.serviceModeIcon}>🚗</Text>
              <Text style={styles.serviceModeTitle}>Service Mode Active</Text>
              <Text style={styles.serviceModeText}>
                You are currently serving a passenger. New booking requests are hidden 
                to help you focus on your current trip.
              </Text>
              {appState.queuedBookings.length > 0 && (
                <Text style={styles.serviceModeNote}>
                  {appState.queuedBookings.length} booking{appState.queuedBookings.length > 1 ? 's' : ''} queued for after this trip.
                </Text>
              )}
            </View>
          </Card>
        )}

        {(() => {
          const availableRides = statusFilters.getAvailableRides(appState.availableRides);
          return availableRides.length === 0 ? (
            <Card style={styles.emptyCard}>
              {isSearching ? (
                <View style={styles.loadingContainer}>
                  <Text style={styles.loadingText}>🔍 Searching for airport transfers...</Text>
                  <Text style={styles.loadingSubtext}>Looking for nearby flights and bookings</Text>
                </View>
              ) : (
                <View style={styles.waitingContainer}>
                  <Text style={styles.waitingText}>✈️ No transfer requests available</Text>
                  <Text style={styles.waitingSubtext}>New airport transfers will appear here</Text>
                </View>
              )}
            </Card>
          ) : (
            <FlatList
              data={availableRides}
              renderItem={renderBookingItem}
              keyExtractor={(item) => item.id}
              showsVerticalScrollIndicator={false}
              contentContainerStyle={styles.listContainer}
              ItemSeparatorComponent={() => <View style={{ height: 8 }} />}
              ListFooterComponent={() => <BottomSpacer />}
            />
          );
        })()}
      </View>

      <Modal
        visible={showDetailsModal}
        animationType="slide"
        presentationStyle="pageSheet"
        onRequestClose={() => setShowDetailsModal(false)}
      >
        {selectedBooking && (
          <View style={styles.modalContainer}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Booking Details</Text>
              <TouchableOpacity onPress={() => setShowDetailsModal(false)}>
                <Text style={styles.closeButton}>✕</Text>
              </TouchableOpacity>
            </View>
            
            <View style={styles.modalContent}>
              <View style={styles.passengerDetails}>
                <Text style={styles.modalPassengerName}>{selectedBooking.passengerName}</Text>
                <Text style={styles.modalPassengerInfo}>📞 {selectedBooking.passengerPhone}</Text>
              </View>
              
              <Card style={styles.modalCard}>
                <Text style={styles.modalSectionTitle}>Trip Details</Text>
                <Text style={styles.modalDetail}>📍 From: {selectedBooking.pickupLocation.address}</Text>
                <Text style={styles.modalDetail}>🏁 To: {selectedBooking.dropoffLocation.address}</Text>
                <Text style={styles.modalDetail}>🚗 Distance: {selectedBooking.distance}km</Text>
                <Text style={styles.modalDetail}>⏱️ Duration: {selectedBooking.duration}min</Text>
                <Text style={styles.modalDetail}>💰 Fare: £{selectedBooking.fare.toFixed(2)}</Text>
                <Text style={styles.modalDetail}>💳 Payment: {selectedBooking.paymentMethod.toUpperCase()}</Text>
              </Card>

              {/* Customer Details in Modal */}
              <ExpandableCustomerDetails 
                ride={selectedBooking}
                isExpanded={true}
                onToggle={() => {}}
                showActions={true}
              />
              
              {selectedBooking.specialRequests && (
                <Card style={styles.modalCard}>
                  <Text style={styles.modalSectionTitle}>Special Requests</Text>
                  <Text style={styles.modalDetail}>{selectedBooking.specialRequests}</Text>
                </Card>
              )}
            </View>
            
            <View style={styles.modalActions}>
              <Button
                title="Decline"
                onPress={() => handleDeclineBooking(selectedBooking.id)}
                variant="secondary"
                style={styles.modalActionButton}
              />
              <Button
                title={
                  !appState.currentRide ? "Accept Ride" :
                  canAcceptNewBooking() ? `Queue (${appState.queuedBookings.length + 1}/${getMaxQueueSize()})` :
                  "Queue Full"
                }
                onPress={() => handleAcceptBooking(selectedBooking)}
                variant={canAcceptNewBooking() ? "success" : "secondary"}
                style={styles.modalActionButton}
                disabled={!canAcceptNewBooking()}
              />
            </View>
          </View>
        )}
      </Modal>

      <Modal
        visible={showDriversList}
        animationType="slide"
        presentationStyle="pageSheet"
        onRequestClose={() => setShowDriversList(false)}
      >
        <View style={styles.modalContainer}>
          <View style={styles.modalHeader}>
            <Text style={styles.modalTitle}>Available Drivers</Text>
            <TouchableOpacity onPress={() => setShowDriversList(false)}>
              <Text style={styles.closeButton}>✕</Text>
            </TouchableOpacity>
          </View>
          
          <View style={styles.driversListContainer}>
            <Text style={styles.driversListTitle}>Drivers in your area ({availableDrivers.length})</Text>
            <FlatList
              data={availableDrivers}
              keyExtractor={(item) => item.id}
              renderItem={({ item }) => (
                <View style={styles.driverItem}>
                  <View style={styles.driverInfo}>
                    <Text style={styles.driverName}>{item.name}</Text>
                    <View style={styles.driverStats}>
                      <Text style={styles.driverStat}>📍 {item.distance}km</Text>
                      <Text style={styles.driverStat}>✅ {item.acceptanceRate}%</Text>
                      <Text style={styles.driverStat}>🚗 {item.completedTrips} trips</Text>
                    </View>
                  </View>
                  <View style={styles.driverStatus}>
                    <View style={[styles.statusDot, { backgroundColor: colors.success }]} />
                    <Text style={styles.driverStatusText}>Online</Text>
                  </View>
                </View>
              )}
              showsVerticalScrollIndicator={false}
            />
          </View>
        </View>
      </Modal>

      <TimePreferenceModal
        visible={showTimePreference}
        onClose={() => {
          setShowTimePreference(false);
          setSelectedBookingForTime(null);
        }}
        booking={selectedBookingForTime}
        onAccept={handleTimePreferenceAccept}
      />
      
      {/* Sidebar removed - now using AppNavigator */}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.backgroundTertiary,
  },
  content: {
    flex: 1,
    paddingHorizontal: 12,
    paddingTop: 16,
  },
  driversButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    gap: 4,
  },
  driversButtonText: {
    color: colors.textLight,
    fontSize: 12,
    fontWeight: '600',
  },
  listContainer: {
    paddingHorizontal: 12,
    paddingBottom: 120,
  },
  bookingCard: {
    marginBottom: 16,
  },
  bookingHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  passengerInfo: {
    flex: 1,
  },
  passengerNameRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  passengerMeta: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  statusBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.backgroundTertiary,
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 8,
    marginLeft: 8,
  },
  statusIcon: {
    fontSize: 10,
    marginRight: 4,
  },
  statusText: {
    fontSize: 10,
    fontWeight: '600',
  },
  priorityBadge: {
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 8,
    backgroundColor: colors.backgroundTertiary,
  },
  priorityText: {
    fontSize: 10,
    fontWeight: '600',
  },
  passengerName: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.text,
    marginBottom: 4,
  },
  fareAmount: {
    fontSize: 22,
    fontWeight: 'bold',
    color: colors.success,
  },
  locationInfo: {
    marginBottom: 8,
  },
  locationLabel: {
    fontSize: 14,
    fontWeight: '600',
    color: colors.text,
    marginBottom: 2,
  },
  locationAddress: {
    fontSize: 14,
    color: colors.textSecondary,
    marginLeft: 20,
  },
  bookingMetrics: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginVertical: 12,
  },
  metricText: {
    fontSize: 12,
    color: colors.textSecondary,
    backgroundColor: colors.border,
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  specialRequests: {
    fontSize: 14,
    color: colors.warning,
    fontStyle: 'italic',
    marginTop: 8,
    backgroundColor: colors.warningLight,
    padding: 8,
    borderRadius: 8,
  },
  actionButtons: {
    flexDirection: 'row',
    marginTop: 16,
    gap: 12,
  },
  actionButton: {
    flex: 1,
  },
  emptyCard: {
    alignItems: 'center',
    padding: 40,
    marginHorizontal: 4,
  },
  emptyText: {
    fontSize: 18,
    color: colors.textSecondary,
    textAlign: 'center',
    marginBottom: 8,
  },
  emptySubtext: {
    fontSize: 14,
    color: colors.textTertiary,
    textAlign: 'center',
  },
  modalContainer: {
    flex: 1,
    backgroundColor: colors.backgroundTertiary,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    paddingTop: 60,
    backgroundColor: colors.background,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: colors.text,
  },
  closeButton: {
    fontSize: 18,
    color: colors.primary,
    fontWeight: '600',
  },
  modalContent: {
    flex: 1,
    padding: 20,
  },
  passengerDetails: {
    alignItems: 'center',
    marginBottom: 20,
  },
  modalPassengerName: {
    fontSize: 24,
    fontWeight: 'bold',
    color: colors.text,
    marginBottom: 8,
  },
  modalPassengerInfo: {
    fontSize: 16,
    color: colors.textSecondary,
    marginBottom: 4,
  },
  modalCard: {
    marginBottom: 16,
  },
  modalSectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.text,
    marginBottom: 12,
  },
  modalDetail: {
    fontSize: 16,
    color: colors.textSecondary,
    marginBottom: 8,
  },
  modalActions: {
    flexDirection: 'row',
    padding: 20,
    backgroundColor: colors.background,
    borderTopWidth: 1,
    borderTopColor: colors.border,
    gap: 12,
  },
  modalActionButton: {
    flex: 1,
  },
  driversListContainer: {
    flex: 1,
    padding: 20,
  },
  driversListTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.text,
    marginBottom: 16,
  },
  driverItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: colors.background,
    padding: 16,
    borderRadius: 12,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  driverInfo: {
    flex: 1,
  },
  driverName: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.text,
    marginBottom: 8,
  },
  driverStats: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  driverStat: {
    fontSize: 12,
    color: colors.textSecondary,
    backgroundColor: colors.backgroundTertiary,
    paddingHorizontal: 6,
    paddingVertical: 3,
    borderRadius: 6,
  },
  driverStatus: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statusDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginRight: 6,
  },
  driverStatusText: {
    fontSize: 12,
    color: colors.textSecondary,
    fontWeight: '500',
  },
  // Dashboard-style improvements
  summaryCard: {
    marginBottom: 12,
    marginHorizontal: 4,
  },
  summaryHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    marginBottom: 8,
  },
  summaryTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.text,
    flex: 1,
  },
  summaryBadge: {
    backgroundColor: colors.primary,
    borderRadius: 12,
    paddingHorizontal: 8,
    paddingVertical: 4,
    minWidth: 24,
    alignItems: 'center',
  },
  summaryCount: {
    fontSize: 14,
    fontWeight: '700',
    color: colors.textLight,
  },
  summarySubtext: {
    fontSize: 14,
    color: colors.textSecondary,
    fontStyle: 'italic',
  },
  loadingContainer: {
    alignItems: 'center',
    paddingVertical: 20,
  },
  loadingText: {
    fontSize: 16,
    color: colors.primary,
    textAlign: 'center',
    fontWeight: '600',
    marginBottom: 8,
  },
  loadingSubtext: {
    fontSize: 14,
    color: colors.textSecondary,
    textAlign: 'center',
    fontStyle: 'italic',
  },
  waitingContainer: {
    alignItems: 'center',
    paddingVertical: 20,
  },
  waitingText: {
    fontSize: 16,
    color: colors.textSecondary,
    textAlign: 'center',
    fontWeight: '600',
    marginBottom: 8,
  },
  waitingSubtext: {
    fontSize: 14,
    color: colors.textTertiary,
    textAlign: 'center',
    fontStyle: 'italic',
  },
  // Enhanced booking card styles from dashboard
  transferRequestHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  transferRequestHeaderRight: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  expandButton: {
    padding: 4,
  },
  transferRequestTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.text,
  },
  transferTypeBadgeCompact: {
    fontSize: 10,
    fontWeight: '700',
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 8,
    textAlign: 'center',
  },
  arrivalBadge: {
    backgroundColor: colors.successLight,
    color: colors.success,
  },
  departureBadge: {
    backgroundColor: colors.backgroundTertiary,
    color: colors.primary,
  },
  rejectedStatusBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.errorLight,
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    gap: 4,
    borderWidth: 1,
    borderColor: colors.error,
  },
  rejectedStatusText: {
    fontSize: 10,
    fontWeight: '700',
    color: colors.error,
  },
  rejectedBookingCard: {
    opacity: 0.7,
    borderWidth: 2,
    borderColor: colors.error,
    backgroundColor: colors.errorLight,
  },
  bookingMainInfo: {
    backgroundColor: colors.backgroundTertiary,
    borderRadius: 12,
    padding: 14,
    marginBottom: 12,
  },
  passengerMainRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 12,
  },
  passengerMainLeft: {
    flex: 2,
  },
  passengerMainRight: {
    flex: 1,
    alignItems: 'flex-end',
  },
  passengerNameMain: {
    fontSize: 16,
    fontWeight: '700',
    color: colors.text,
    marginBottom: 4,
  },
  flightInfoMain: {
    fontSize: 14,
    color: colors.primary,
    fontWeight: '600',
  },
  fareAmountMain: {
    fontSize: 18,
    fontWeight: '700',
    color: colors.success,
    marginBottom: 2,
  },
  fareDetailsMain: {
    fontSize: 12,
    color: colors.textSecondary,
    marginBottom: 4,
  },
  locationMainInfo: {
    gap: 6,
  },
  locationMainRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
  },
  locationMainText: {
    fontSize: 12,
    color: colors.textSecondary,
    flex: 1,
  },
  rideDetailsExpanded: {
    backgroundColor: colors.backgroundTertiary,
    borderRadius: 12,
    padding: 16,
    marginTop: 12,
    marginBottom: 12,
  },
  expandedRow: {
    flexDirection: 'row',
    gap: 16,
    marginBottom: 12,
  },
  expandedLeft: {
    flex: 1,
  },
  expandedRight: {
    flex: 1,
  },
  expandedLabel: {
    fontSize: 12,
    fontWeight: '600',
    color: colors.textSecondary,
    marginBottom: 4,
  },
  expandedValue: {
    fontSize: 14,
    color: colors.text,
    marginBottom: 2,
  },
  expandedSpecialRequests: {
    marginBottom: 12,
    padding: 10,
    backgroundColor: colors.warningLight,
    borderRadius: 8,
  },
  communicationActions: {
    flexDirection: 'row',
    gap: 8,
    marginTop: 12,
    marginBottom: 8,
  },
  callButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: colors.success,
    paddingVertical: 10,
    borderRadius: 8,
    gap: 6,
  },
  messageButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: colors.primary,
    paddingVertical: 10,
    borderRadius: 8,
    gap: 6,
  },
  actionText: {
    fontSize: 14,
    fontWeight: '600',
    color: colors.textLight,
  },
  rideActionsCompact: {
    flexDirection: 'row',
    gap: 8,
  },
  actionButtonCompact: {
    flex: 1,
    paddingVertical: 8,
  },
  rejectedActionsContainer: {
    marginTop: 12,
    marginBottom: 8,
  },
  rejectedStatusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.errorLight,
    padding: 12,
    borderRadius: 8,
    gap: 8,
    borderLeftWidth: 4,
    borderLeftColor: colors.error,
  },
  rejectedMessage: {
    flex: 1,
    fontSize: 14,
    color: colors.error,
    fontWeight: '500',
  },
  // Service mode styles
  serviceModeCard: {
    marginBottom: 12,
    marginHorizontal: 4,
  },
  serviceModeContainer: {
    alignItems: 'center',
    paddingVertical: 20,
  },
  serviceModeIcon: {
    fontSize: 32,
    marginBottom: 12,
  },
  serviceModeTitle: {
    fontSize: 18,
    fontWeight: '700',
    color: colors.text,
    marginBottom: 8,
    textAlign: 'center',
  },
  serviceModeText: {
    fontSize: 14,
    color: colors.textSecondary,
    textAlign: 'center',
    lineHeight: 20,
    marginBottom: 8,
  },
  serviceModeNote: {
    fontSize: 12,
    color: colors.primary,
    fontWeight: '600',
    textAlign: 'center',
    marginTop: 4,
  },
  // Limited actions styles
  limitedActionsContainer: {
    marginTop: 12,
    marginBottom: 8,
  },
  limitedActionsMessage: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.warningLight,
    padding: 12,
    borderRadius: 8,
    gap: 8,
    borderLeftWidth: 4,
    borderLeftColor: colors.warning,
  },
  limitedActionsText: {
    flex: 1,
    fontSize: 14,
    color: colors.text,
    fontWeight: '500',
  },
});

export default BookingListScreen;