import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Modal, Alert, Linking, Platform, ScrollView } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { MaterialIcons } from '@expo/vector-icons';
import NavigationCard from '../components/NavigationCard';
import GoogleMapsNavigation from '../components/GoogleMapsNavigation';
import { useApp } from '../context/ApiIntegratedGlobalStateContext';
import { AirportLocation } from '../types';
import { colors, shadows } from '../theme/colors';

interface AirportTransferData {
  id: string;
  name: string;
  phone: string;
  photo: string;
  location: {
    latitude: number;
    longitude: number;
    address: string;
  };
  destinationLocation: {
    latitude: number;
    longitude: number;
    address: string;
  };
  destination: string;
  fare: number;
  distance: number;
  status: 'waiting' | 'in_transit' | 'completed';
  transferType: 'arrival' | 'departure';
  flightNumber?: string;
  airline?: string;
  terminal?: string;
  flightTime?: string;
  flightStatus?: 'on_time' | 'delayed' | 'landed' | 'cancelled';
  vehicleType: 'saloon' | 'estate' | 'executive' | 'people_carrier' | 'executive_people_carrier' | 'minibus_8';
  passengerCount: number;
  hasMeetAndGreet: boolean;
  airport: string;
  scheduledTime?: string;
}

// Airport locations
const AIRPORTS: AirportLocation[] = [
  {
    code: 'LHR',
    name: 'Heathrow Airport',
    coordinates: { latitude: 51.4700, longitude: -0.4543 }
  },
  {
    code: 'LGW',
    name: 'Gatwick Airport',
    coordinates: { latitude: 51.1537, longitude: -0.1821 }
  },
  {
    code: 'STN',
    name: 'Stansted Airport',
    coordinates: { latitude: 51.8860, longitude: 0.2389 }
  }
];

const mapStyle = [
  {
    "featureType": "poi",
    "elementType": "all",
    "stylers": [
      {
        "visibility": "off"
      }
    ]
  },
  {
    "featureType": "poi.business",
    "elementType": "all",
    "stylers": [
      {
        "visibility": "off"
      }
    ]
  },
  {
    "featureType": "poi.attraction",
    "elementType": "all",
    "stylers": [
      {
        "visibility": "off"
      }
    ]
  },
  {
    "featureType": "poi.park",
    "elementType": "all",
    "stylers": [
      {
        "visibility": "off"
      }
    ]
  },
  {
    "featureType": "poi.school",
    "elementType": "all",
    "stylers": [
      {
        "visibility": "off"
      }
    ]
  },
  {
    "featureType": "poi.government",
    "elementType": "all",
    "stylers": [
      {
        "visibility": "off"
      }
    ]
  },
  {
    "featureType": "poi.medical",
    "elementType": "all",
    "stylers": [
      {
        "visibility": "off"
      }
    ]
  },
  {
    "featureType": "poi.place_of_worship",
    "elementType": "all",
    "stylers": [
      {
        "visibility": "off"
      }
    ]
  },
  {
    "featureType": "poi.sports_complex",
    "elementType": "all",
    "stylers": [
      {
        "visibility": "off"
      }
    ]
  },
  {
    "featureType": "transit",
    "elementType": "all",
    "stylers": [
      {
        "visibility": "off"
      }
    ]
  }
];

const MapScreen: React.FC = () => {
  const navigation = useNavigation();
  const { setNavigationVisibility } = useApp();
  const [passengers, setPassengers] = useState<AirportTransferData[]>([]);
  const [selectedPassenger, setSelectedPassenger] = useState<AirportTransferData | null>(null);
  const [isDetailsVisible, setIsDetailsVisible] = useState(false);
  const [isCardExpanded, setIsCardExpanded] = useState(false);
  const [routeCoordinates, setRouteCoordinates] = useState<Array<{latitude: number, longitude: number}>>([]);
  const [allRoutes, setAllRoutes] = useState<Array<{passengerId: string, coordinates: Array<{latitude: number, longitude: number}>, color: string}>>([]);
  const [showAllRoutes, setShowAllRoutes] = useState(false);
  const [driverLocation] = useState({ latitude: 51.4700, longitude: -0.4543 });

  useEffect(() => {
    const mockPassengers: AirportTransferData[] = [
      {
        id: '1',
        name: 'John Smith',
        phone: '+44 7700 123456',
        photo: 'https://i.pravatar.cc/150?img=1',
        location: {
          latitude: 51.4700,
          longitude: -0.4543,
          address: 'Heathrow Airport Terminal 5, London'
        },
        destinationLocation: {
          latitude: 51.5074,
          longitude: -0.1278,
          address: 'Central London'
        },
        destination: 'Central London',
        fare: 65.00,
        distance: 24.5,
        status: 'waiting',
        transferType: 'arrival',
        flightNumber: 'BA123',
        airline: 'British Airways',
        terminal: 'Terminal 5',
        flightTime: '14:30',
        flightStatus: 'landed',
        vehicleType: 'executive',
        passengerCount: 2,
        hasMeetAndGreet: true,
        airport: 'Heathrow (LHR)'
      },
      {
        id: '2',
        name: 'Sarah Johnson',
        phone: '+44 7700 987654',
        photo: 'https://i.pravatar.cc/150?img=2',
        location: {
          latitude: 51.8860,
          longitude: 0.2389,
          address: 'Stansted Airport Terminal, London'
        },
        destinationLocation: {
          latitude: 52.2053,
          longitude: 0.1218,
          address: 'Cambridge'
        },
        destination: 'Cambridge',
        fare: 85.00,
        distance: 48.3,
        status: 'waiting',
        transferType: 'departure',
        flightNumber: 'FR456',
        airline: 'Ryanair',
        terminal: 'Main Terminal',
        flightTime: '18:15',
        flightStatus: 'on_time',
        vehicleType: 'people_carrier',
        passengerCount: 5,
        hasMeetAndGreet: false,
        airport: 'Stansted (STN)',
        scheduledTime: 'Today 15:45'
      },
      {
        id: '3',
        name: 'Mike Wilson',
        phone: '+44 7700 456789',
        photo: 'https://i.pravatar.cc/150?img=3',
        location: {
          latitude: 51.1537,
          longitude: -0.1821,
          address: 'Gatwick Airport South Terminal, London'
        },
        destinationLocation: {
          latitude: 50.8429,
          longitude: -0.1313,
          address: 'Brighton'
        },
        destination: 'Brighton',
        fare: 45.00,
        distance: 32.1,
        status: 'waiting',
        transferType: 'arrival',
        flightNumber: 'EZ789',
        airline: 'easyJet',
        terminal: 'South Terminal',
        flightTime: '16:20',
        flightStatus: 'delayed',
        vehicleType: 'saloon',
        passengerCount: 1,
        hasMeetAndGreet: true,
        airport: 'Gatwick (LGW)'
      }
    ];
    setPassengers(mockPassengers);
    
    // Generate routes for all passengers
    const routes = mockPassengers.map((passenger, index) => {
      const routeColors = [colors.primary, colors.success, colors.warning, colors.error, '#5856D6'];
      const route = [
        passenger.location, // Airport pickup
        {
          latitude: (passenger.location.latitude + passenger.destinationLocation.latitude) / 2,
          longitude: (passenger.location.longitude + passenger.destinationLocation.longitude) / 2,
        },
        passenger.destinationLocation // Final destination
      ];
      return {
        passengerId: passenger.id,
        coordinates: route,
        color: routeColors[index % routeColors.length]
      };
    });
    setAllRoutes(routes);
  }, []);

  const handleMarkerPress = (passenger: AirportTransferData, isDestination: boolean = false) => {
    setSelectedPassenger(passenger);
    setIsDetailsVisible(true);
    setIsCardExpanded(false);
    
    // Generate route from airport to destination
    const route = [
      passenger.location, // Airport pickup
      {
        latitude: (passenger.location.latitude + passenger.destinationLocation.latitude) / 2,
        longitude: (passenger.location.longitude + passenger.destinationLocation.longitude) / 2,
      },
      passenger.destinationLocation // Final destination
    ];
    setRouteCoordinates(route);
  };

  const toggleAllRoutes = () => {
    setShowAllRoutes(!showAllRoutes);
    if (!showAllRoutes) {
      setRouteCoordinates([]); // Clear individual route when showing all
    }
  };

  const handleCardPress = () => {
    setIsCardExpanded(true);
  };

  const closeDetails = () => {
    setIsDetailsVisible(false);
    setSelectedPassenger(null);
    setIsCardExpanded(false);
    setRouteCoordinates([]);
  };

  const handleStartNavigation = (passenger: AirportTransferData) => {
    const destination = passenger.location; // Airport pickup location
    const destinationLabel = `${passenger.airport} - ${passenger.terminal}`;

    Alert.alert(
      'Open Navigation',
      `Navigate to ${destinationLabel}?`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Google Maps',
          onPress: () => {
            const url = Platform.select({
              ios: `comgooglemaps://?daddr=${destination.latitude},${destination.longitude}&directionsmode=driving`,
              android: `google.navigation:q=${destination.latitude},${destination.longitude}&mode=d`
            });
            
            Linking.canOpenURL(url!)
              .then(supported => {
                if (supported) {
                  return Linking.openURL(url!);
                } else {
                  // Fallback to web Google Maps
                  const webUrl = `https://maps.google.com/maps?daddr=${destination.latitude},${destination.longitude}&mode=driving`;
                  return Linking.openURL(webUrl);
                }
              })
              .catch(err => {
                console.error('Error opening maps:', err);
                Alert.alert('Error', 'Failed to open navigation app');
              });
          }
        },
        {
          text: Platform.OS === 'ios' ? 'Apple Maps' : 'Maps',
          onPress: () => {
            const url = Platform.select({
              ios: `maps://?daddr=${destination.latitude},${destination.longitude}&dirflg=d`,
              android: `geo:${destination.latitude},${destination.longitude}?q=${destination.latitude},${destination.longitude}`
            });
            
            Linking.canOpenURL(url!)
              .then(supported => {
                if (supported) {
                  return Linking.openURL(url!);
                } else {
                  Alert.alert('Error', 'Navigation app not available');
                }
              })
              .catch(err => {
                console.error('Error opening maps:', err);
                Alert.alert('Error', 'Failed to open navigation app');
              });
          }
        }
      ]
    );
  };

  // Navigation is now handled by AppNavigator

  return (
    <View style={styles.container}>
      <View style={styles.mapContainer}>
      <GoogleMapsNavigation
        origin={driverLocation}
        destination={selectedPassenger?.location || { latitude: 51.4700, longitude: -0.4543 }}
        onNavigationProgress={(progress) => console.log('Navigation progress:', progress)}
      />
      
      {/* Floating control buttons */}
      <View style={styles.mapControls}>
        <TouchableOpacity 
          style={[styles.controlButton, showAllRoutes && styles.controlButtonActive]}
          onPress={toggleAllRoutes}
        >
          <MaterialIcons 
            name={showAllRoutes ? "visibility-off" : "visibility"} 
            size={20} 
            color={showAllRoutes ? colors.textLight : colors.primary} 
          />
          <Text style={[styles.controlButtonText, showAllRoutes && styles.controlButtonTextActive]}>
            {showAllRoutes ? "Hide Routes" : "Show All Routes"}
          </Text>
        </TouchableOpacity>
      </View>
      
      {/* Map Legend */}
      <View style={styles.mapLegend}>
        <Text style={styles.legendTitle}>Map Legend</Text>
        <View style={styles.legendItem}>
          <View style={[styles.legendDot, styles.arrivalDot]} />
          <Text style={styles.legendText}>Airport Pickup (Arrival)</Text>
        </View>
        <View style={styles.legendItem}>
          <View style={[styles.legendDot, styles.departureDot]} />
          <Text style={styles.legendText}>Airport Pickup (Departure)</Text>
        </View>
        <View style={styles.legendItem}>
          <MaterialIcons name="place" size={16} color={colors.error} />
          <Text style={styles.legendText}>Customer Destination</Text>
        </View>
      </View>
      </View>

      {/* Small Island Card */}
      <Modal
        visible={isDetailsVisible && !isCardExpanded}
        transparent={true}
        animationType="fade"
        onRequestClose={closeDetails}
      >
        <TouchableOpacity 
          style={styles.cardOverlay}
          activeOpacity={1}
          onPress={closeDetails}
        >
          <View style={styles.cardContainer}>
            {selectedPassenger && (
              <TouchableOpacity 
                style={styles.islandCard}
                onPress={handleCardPress}
                activeOpacity={0.8}
              >
                <View style={styles.cardHeader}>
                  <View style={styles.cardUserInfo}>
                    <MaterialIcons name="person" size={16} color={colors.primary} />
                    <Text style={styles.cardName}>{selectedPassenger.name}</Text>
                  </View>
                  <View style={[styles.statusBadge, selectedPassenger.transferType === 'arrival' ? styles.arrivalTransfer : styles.departureTransfer]}>
                    <Text style={[styles.statusText, selectedPassenger.transferType === 'arrival' ? styles.arrivalTransferText : styles.departureTransferText]}>
                      {selectedPassenger.transferType === 'arrival' ? 'ARRIVAL' : 'DEPARTURE'}
                    </Text>
                  </View>
                </View>
                
                <View style={styles.flightInfo}>
                  <View style={styles.flightRow}>
                    <MaterialIcons name="flight" size={14} color={colors.primary} />
                    <Text style={styles.flightText}>{selectedPassenger.flightNumber} • {selectedPassenger.airport}</Text>
                  </View>
                  <View style={styles.vehicleRow}>
                    <MaterialIcons name="directions-car" size={14} color={colors.warning} />
                    <Text style={styles.vehicleText}>{selectedPassenger.vehicleType.replace('_', ' ').toUpperCase()} • {selectedPassenger.passengerCount} pax</Text>
                  </View>
                </View>
                
                <View style={styles.cardRoute}>
                  <MaterialIcons name="my-location" size={12} color={colors.success} />
                  <Text style={styles.cardRouteText}>→ {selectedPassenger.destination}</Text>
                </View>
                
                <View style={styles.cardFooter}>
                  <View style={styles.flightStatusContainer}>
                    <Text style={[styles.flightStatus, 
                      selectedPassenger.flightStatus === 'on_time' ? styles.onTimeStatus :
                      selectedPassenger.flightStatus === 'delayed' ? styles.delayedStatus :
                      selectedPassenger.flightStatus === 'landed' ? styles.landedStatus : styles.cancelledStatus
                    ]}>
                      {selectedPassenger.flightStatus?.toUpperCase().replace('_', ' ')}
                    </Text>
                    {selectedPassenger.hasMeetAndGreet && (
                      <Text style={styles.meetGreetBadge}>MEET & GREET</Text>
                    )}
                  </View>
                  <Text style={styles.cardFare}>£{selectedPassenger.fare.toFixed(2)}</Text>
                  <Text style={styles.cardTapHint}>Tap to expand</Text>
                </View>
              </TouchableOpacity>
            )}
          </View>
        </TouchableOpacity>
      </Modal>

      {/* Expanded Details Modal */}
      <Modal
        visible={isCardExpanded}
        transparent={true}
        animationType="slide"
        onRequestClose={closeDetails}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContainer}>
            {selectedPassenger && (
              <>
                <View style={styles.modalHeader}>
                  <Text style={styles.modalTitle}>Trip Details</Text>
                  <TouchableOpacity onPress={closeDetails} style={styles.closeButton}>
                    <MaterialIcons name="close" size={24} color={colors.textSecondary} />
                  </TouchableOpacity>
                </View>
                
                <ScrollView 
                  style={styles.modalContent}
                  showsVerticalScrollIndicator={false}
                  bounces={true}
                >
                  {/* Passenger Info Header */}
                  <View style={styles.passengerHeader}>
                    <View style={styles.passengerAvatar}>
                      <MaterialIcons name="person" size={32} color={colors.primary} />
                    </View>
                    <View style={styles.passengerDetails}>
                      <Text style={styles.passengerName}>{selectedPassenger.name}</Text>
                      <Text style={styles.passengerPhone}>{selectedPassenger.phone}</Text>
                      <View style={[styles.statusIndicator, selectedPassenger.transferType === 'arrival' ? styles.arrivalTransfer : styles.departureTransfer]}>
                        <Text style={[styles.statusText, selectedPassenger.transferType === 'arrival' ? styles.arrivalTransferText : styles.departureTransferText]}>
                          {selectedPassenger.transferType === 'arrival' ? 'AIRPORT ARRIVAL' : 'AIRPORT DEPARTURE'}
                        </Text>
                      </View>
                    </View>
                  </View>

                  {/* Flight Information */}
                  <View style={styles.flightSection}>
                    <Text style={styles.sectionTitle}>Flight Information</Text>
                    <View style={styles.flightCard}>
                      <View style={styles.flightHeaderRow}>
                        <View style={styles.flightMainInfo}>
                          <Text style={styles.flightNumber}>{selectedPassenger.flightNumber}</Text>
                          <Text style={styles.airlineName}>{selectedPassenger.airline}</Text>
                        </View>
                        <View style={styles.flightStatusBadge}>
                          <Text style={[styles.flightStatusText, 
                            selectedPassenger.flightStatus === 'on_time' ? styles.onTimeText :
                            selectedPassenger.flightStatus === 'delayed' ? styles.delayedText :
                            selectedPassenger.flightStatus === 'landed' ? styles.landedText : styles.cancelledText
                          ]}>
                            {selectedPassenger.flightStatus?.toUpperCase().replace('_', ' ')}
                          </Text>
                        </View>
                      </View>
                      <View style={styles.flightDetailsRow}>
                        <View style={styles.flightDetail}>
                          <MaterialIcons name="flight" size={16} color={colors.primary} />
                          <Text style={styles.flightDetailLabel}>Time</Text>
                          <Text style={styles.flightDetailValue}>{selectedPassenger.flightTime}</Text>
                        </View>
                        <View style={styles.flightDetail}>
                          <MaterialIcons name="location-on" size={16} color={colors.warning} />
                          <Text style={styles.flightDetailLabel}>Terminal</Text>
                          <Text style={styles.flightDetailValue}>{selectedPassenger.terminal}</Text>
                        </View>
                        <View style={styles.flightDetail}>
                          <MaterialIcons name="local-airport" size={16} color={colors.success} />
                          <Text style={styles.flightDetailLabel}>Airport</Text>
                          <Text style={styles.flightDetailValue}>{selectedPassenger.airport}</Text>
                        </View>
                      </View>
                    </View>
                  </View>

                  {/* Vehicle Information */}
                  <View style={styles.vehicleSection}>
                    <Text style={styles.sectionTitle}>Vehicle & Service</Text>
                    <View style={styles.vehicleCard}>
                      <View style={styles.vehicleInfo}>
                        <View style={styles.vehicleDetail}>
                          <MaterialIcons name="directions-car" size={20} color={colors.primary} />
                          <Text style={styles.vehicleDetailLabel}>Vehicle Type</Text>
                          <Text style={styles.vehicleDetailValue}>{selectedPassenger.vehicleType.replace('_', ' ').toUpperCase()}</Text>
                        </View>
                        <View style={styles.vehicleDetail}>
                          <MaterialIcons name="people" size={20} color={colors.warning} />
                          <Text style={styles.vehicleDetailLabel}>Passengers</Text>
                          <Text style={styles.vehicleDetailValue}>{selectedPassenger.passengerCount}</Text>
                        </View>
                        {selectedPassenger.hasMeetAndGreet && (
                          <View style={styles.vehicleDetail}>
                            <MaterialIcons name="person-pin" size={20} color={colors.success} />
                            <Text style={styles.vehicleDetailLabel}>Service</Text>
                            <Text style={styles.meetGreetService}>MEET & GREET</Text>
                          </View>
                        )}
                      </View>
                    </View>
                  </View>

                  {/* Route Information */}
                  <View style={styles.routeSection}>
                    <Text style={styles.sectionTitle}>Route Information</Text>
                    <View style={styles.routeContainer}>
                      <View style={styles.routeItem}>
                        <MaterialIcons name="my-location" size={20} color={colors.success} />
                        <View style={styles.routeText}>
                          <Text style={styles.routeLabel}>Pickup</Text>
                          <Text style={styles.routeAddress}>{selectedPassenger.location.address}</Text>
                        </View>
                      </View>
                      
                      <View style={styles.routeLine}>
                        <MaterialIcons name="more-vert" size={16} color={colors.border} />
                      </View>
                      
                      <View style={styles.routeItem}>
                        <MaterialIcons name="place" size={20} color={colors.error} />
                        <View style={styles.routeText}>
                          <Text style={styles.routeLabel}>Destination</Text>
                          <Text style={styles.routeAddress}>{selectedPassenger.destination}</Text>
                        </View>
                      </View>
                    </View>
                  </View>

                  {/* Transfer Status */}
                  <View style={styles.transferStatusSection}>
                    <Text style={styles.sectionTitle}>Transfer Status</Text>
                    <View style={styles.transferStatusCard}>
                      <View style={styles.transferStatusItem}>
                        <MaterialIcons name="schedule" size={20} color={colors.primary} />
                        <Text style={styles.transferStatusLabel}>
                          {selectedPassenger.transferType === 'arrival' ? 'Flight Status' : 'Departure Time'}
                        </Text>
                        <Text style={styles.transferStatusValue}>
                          {selectedPassenger.transferType === 'arrival' 
                            ? selectedPassenger.flightStatus?.toUpperCase().replace('_', ' ') 
                            : selectedPassenger.flightTime}
                        </Text>
                      </View>
                      <View style={styles.transferStatusItem}>
                        <MaterialIcons name="directions-car" size={20} color={colors.warning} />
                        <Text style={styles.transferStatusLabel}>
                          {selectedPassenger.transferType === 'arrival' ? 'Time to Pickup' : 'Journey Time'}
                        </Text>
                        <Text style={styles.transferStatusValue}>
                          {selectedPassenger.transferType === 'arrival' ? '~15 min' : '~45 min'}
                        </Text>
                      </View>
                    </View>
                  </View>

                  {/* Navigation Section */}
                  <NavigationCard
                    destination={selectedPassenger.transferType === 'arrival' ? selectedPassenger.location : selectedPassenger.destinationLocation}
                    destinationLabel={selectedPassenger.transferType === 'arrival' ? 'Airport Pickup' : 'Drop-off Location'}
                    estimatedTime="15-18 min"
                    distance={`${selectedPassenger.distance} km`}
                    onNavigationStart={() => console.log('Navigation started from map')}
                  />

                  {/* Trip Details */}
                  <View style={styles.tripDetailsSection}>
                    <Text style={styles.sectionTitle}>Trip Details</Text>
                    <View style={styles.tripMetrics}>
                      <View style={styles.metricItem}>
                        <MaterialIcons name="straighten" size={20} color={colors.primary} />
                        <Text style={styles.metricValue}>{selectedPassenger.distance} km</Text>
                        <Text style={styles.metricLabel}>Distance</Text>
                      </View>
                      <View style={styles.metricItem}>
                        <MaterialIcons name="attach-money" size={20} color={colors.success} />
                        <Text style={styles.metricValue}>£{selectedPassenger.fare.toFixed(2)}</Text>
                        <Text style={styles.metricLabel}>Fare</Text>
                      </View>
                      <View style={styles.metricItem}>
                        <MaterialIcons name="timer" size={20} color={colors.textTertiary} />
                        <Text style={styles.metricValue}>45 min</Text>
                        <Text style={styles.metricLabel}>Until Pickup</Text>
                      </View>
                    </View>
                  </View>

                  {/* Action Buttons */}
                  <View style={styles.actionButtons}>
                    <View style={styles.secondaryButtonsRow}>
                      <TouchableOpacity style={styles.secondaryButton}>
                        <MaterialIcons name="phone" size={20} color={colors.primary} />
                        <Text style={styles.secondaryButtonText}>Call</Text>
                      </TouchableOpacity>
                      <TouchableOpacity style={styles.secondaryButton}>
                        <MaterialIcons name="message" size={20} color={colors.primary} />
                        <Text style={styles.secondaryButtonText}>Message</Text>
                      </TouchableOpacity>
                    </View>
                    
                    {/* Airport Transfer Options */}
                    {selectedPassenger.transferType === 'arrival' && (
                      <View style={styles.flightTrackingButtonRow}>
                        <TouchableOpacity style={styles.flightTrackingButton}>
                          <MaterialIcons name="flight" size={20} color={colors.primary} />
                          <Text style={styles.flightTrackingButtonText}>Track Flight Updates</Text>
                        </TouchableOpacity>
                      </View>
                    )}
                    
                    {selectedPassenger.hasMeetAndGreet && (
                      <View style={styles.meetGreetButtonRow}>
                        <TouchableOpacity style={styles.meetGreetButton}>
                          <MaterialIcons name="person-pin" size={20} color={colors.success} />
                          <Text style={styles.meetGreetButtonText}>Activate Meet & Greet Mode</Text>
                        </TouchableOpacity>
                      </View>
                    )}
                    
                    <View style={styles.mainButtonsRow}>
                      <TouchableOpacity style={styles.declineButton}>
                        <Text style={styles.declineButtonText}>Decline</Text>
                      </TouchableOpacity>
                      <TouchableOpacity style={selectedPassenger.transferType === 'arrival' ? styles.acceptArrivalButton : styles.acceptDepartureButton}>
                        <Text style={selectedPassenger.transferType === 'arrival' ? styles.acceptArrivalButtonText : styles.acceptDepartureButtonText}>
                          {selectedPassenger.transferType === 'arrival' ? 'Accept Arrival Transfer' : 'Accept Departure Transfer'}
                        </Text>
                      </TouchableOpacity>
                    </View>
                  </View>
                </ScrollView>
              </>
            )}
          </View>
        </View>
      </Modal>

      {/* Sidebar removed - now using AppNavigator */}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  profileButton: {
    padding: 8,
  },
  mapContainer: {
    flex: 1,
  },
  map: {
    flex: 1,
    width: '100%',
    height: '100%',
  },
  cardOverlay: {
    flex: 1,
    backgroundColor: 'transparent',
    justifyContent: 'flex-end',
    alignItems: 'center',
  },
  cardContainer: {
    alignItems: 'center',
    paddingBottom: 50,
  },
  islandCard: {
    backgroundColor: colors.background,
    borderRadius: 16,
    padding: 16,
    minWidth: 280,
    maxWidth: 320,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 12,
    elevation: 8,
    borderWidth: 1,
    borderColor: colors.border,
  },
  cardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  cardUserInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  cardName: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.text,
    marginLeft: 6,
    marginRight: 8,
  },
  statusBadge: {
    backgroundColor: colors.successLight,
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 8,
  },
  statusText: {
    fontSize: 10,
    fontWeight: '600',
    color: colors.success,
  },
  cardRoute: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
    paddingHorizontal: 8,
    paddingVertical: 6,
    backgroundColor: colors.backgroundTertiary,
    borderRadius: 8,
  },
  cardRouteText: {
    fontSize: 14,
    color: colors.text,
    marginLeft: 6,
    fontWeight: '500',
  },
  cardFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  cardDistance: {
    fontSize: 12,
    color: colors.textSecondary,
    backgroundColor: colors.backgroundTertiary,
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 6,
  },
  cardSchedule: {
    fontSize: 12,
    color: colors.primary,
    backgroundColor: colors.backgroundTertiary,
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 6,
    fontWeight: '600',
  },
  cardWaiting: {
    fontSize: 12,
    color: colors.error,
    backgroundColor: colors.errorLight,
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 6,
    fontWeight: '600',
  },
  cardFare: {
    fontSize: 16,
    fontWeight: '700',
    color: colors.success,
  },
  cardTapHint: {
    fontSize: 11,
    color: colors.textTertiary,
    fontStyle: 'italic',
  },
  passengerMarker: {
    width: 30,
    height: 30,
    alignItems: 'center',
    justifyContent: 'center',
  },
  passengerDot: {
    width: 20,
    height: 20,
    borderRadius: 10,
    borderWidth: 3,
    borderColor: colors.background,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 3,
    elevation: 5,
  },
  arrivalDot: {
    backgroundColor: colors.success,
  },
  departureDot: {
    backgroundColor: colors.primary,
  },
  calloutContainer: {
    backgroundColor: colors.background,
    padding: 10,
    borderRadius: 8,
    minWidth: 120,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 3,
  },
  calloutName: {
    fontSize: 14,
    fontWeight: '600',
    color: colors.text,
    marginBottom: 4,
  },
  calloutInfo: {
    fontSize: 12,
    color: colors.textSecondary,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  modalContainer: {
    backgroundColor: colors.background,
    borderTopLeftRadius: 24,
    borderTopRightRadius: 24,
    padding: 0,
    maxHeight: '90%',
    flex: 1,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    paddingBottom: 16,
    borderBottomWidth: 1,
    borderBottomColor: colors.backgroundTertiary,
  },
  modalTitle: {
    fontSize: 22,
    fontWeight: '700',
    color: colors.text,
  },
  closeButton: {
    padding: 8,
    borderRadius: 20,
    backgroundColor: colors.backgroundTertiary,
  },
  modalContent: {
    flex: 1,
    paddingHorizontal: 20,
    paddingTop: 20,
    paddingBottom: 40,
  },
  passengerHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 24,
    padding: 16,
    backgroundColor: colors.backgroundTertiary,
    borderRadius: 16,
  },
  passengerAvatar: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: colors.backgroundTertiary,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 16,
  },
  passengerDetails: {
    flex: 1,
  },
  passengerName: {
    fontSize: 20,
    fontWeight: '700',
    color: colors.text,
    marginBottom: 4,
  },
  passengerPhone: {
    fontSize: 16,
    color: colors.primary,
    marginBottom: 8,
  },
  statusIndicator: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  arrivalTransfer: {
    backgroundColor: colors.successLight,
  },
  departureTransfer: {
    backgroundColor: colors.backgroundTertiary,
  },
  arrivalTransferText: {
    color: colors.success,
  },
  departureTransferText: {
    color: colors.primary,
  },
  flightInfo: {
    marginBottom: 12,
    backgroundColor: colors.backgroundTertiary,
    borderRadius: 8,
    padding: 8,
  },
  flightRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  vehicleRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  flightText: {
    fontSize: 12,
    color: colors.text,
    marginLeft: 6,
    fontWeight: '600',
  },
  vehicleText: {
    fontSize: 12,
    color: colors.text,
    marginLeft: 6,
    fontWeight: '500',
  },
  flightStatusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
  },
  flightStatus: {
    fontSize: 10,
    fontWeight: '600',
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 4,
  },
  onTimeStatus: {
    backgroundColor: colors.successLight,
    color: colors.success,
  },
  delayedStatus: {
    backgroundColor: colors.errorLight,
    color: colors.error,
  },
  landedStatus: {
    backgroundColor: colors.backgroundTertiary,
    color: colors.primary,
  },
  cancelledStatus: {
    backgroundColor: colors.backgroundTertiary,
    color: colors.textTertiary,
  },
  meetGreetBadge: {
    fontSize: 9,
    fontWeight: '700',
    backgroundColor: colors.warningLight,
    color: colors.warning,
    paddingHorizontal: 4,
    paddingVertical: 2,
    borderRadius: 4,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.text,
    marginBottom: 16,
  },
  flightSection: {
    marginBottom: 24,
  },
  flightCard: {
    backgroundColor: colors.backgroundTertiary,
    borderRadius: 16,
    padding: 16,
    borderWidth: 1,
    borderColor: colors.border,
  },
  flightHeaderRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  flightMainInfo: {
    flex: 1,
  },
  flightNumber: {
    fontSize: 20,
    fontWeight: '700',
    color: colors.text,
    marginBottom: 4,
  },
  airlineName: {
    fontSize: 14,
    color: colors.textSecondary,
    fontWeight: '500',
  },
  flightStatusBadge: {
    backgroundColor: colors.backgroundTertiary,
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 12,
  },
  flightStatusText: {
    fontSize: 12,
    fontWeight: '700',
  },
  onTimeText: {
    color: colors.success,
  },
  delayedText: {
    color: colors.error,
  },
  landedText: {
    color: colors.primary,
  },
  cancelledText: {
    color: colors.textTertiary,
  },
  flightDetailsRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  flightDetail: {
    alignItems: 'center',
    flex: 1,
  },
  flightDetailLabel: {
    fontSize: 12,
    color: colors.textSecondary,
    marginTop: 4,
    marginBottom: 2,
  },
  flightDetailValue: {
    fontSize: 14,
    fontWeight: '600',
    color: colors.text,
  },
  vehicleSection: {
    marginBottom: 24,
  },
  vehicleCard: {
    backgroundColor: colors.backgroundTertiary,
    borderRadius: 16,
    padding: 16,
    borderWidth: 1,
    borderColor: colors.border,
  },
  vehicleInfo: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  vehicleDetail: {
    alignItems: 'center',
    flex: 1,
  },
  vehicleDetailLabel: {
    fontSize: 12,
    color: colors.textSecondary,
    marginTop: 4,
    marginBottom: 2,
  },
  vehicleDetailValue: {
    fontSize: 14,
    fontWeight: '600',
    color: colors.text,
  },
  meetGreetService: {
    fontSize: 12,
    fontWeight: '700',
    color: colors.warning,
    backgroundColor: colors.warningLight,
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 8,
  },
  transferStatusSection: {
    marginBottom: 24,
  },
  transferStatusCard: {
    backgroundColor: colors.backgroundTertiary,
    borderRadius: 16,
    padding: 16,
    borderWidth: 1,
    borderColor: colors.border,
  },
  transferStatusItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  transferStatusLabel: {
    fontSize: 14,
    color: colors.textSecondary,
    marginLeft: 12,
    flex: 1,
  },
  transferStatusValue: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.text,
  },
  flightTrackingButtonRow: {
    marginBottom: 12,
  },
  flightTrackingButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: colors.backgroundTertiary,
    borderWidth: 1,
    borderColor: colors.primary,
    borderRadius: 12,
    paddingVertical: 12,
    paddingHorizontal: 16,
  },
  flightTrackingButtonText: {
    fontSize: 14,
    fontWeight: '600',
    color: colors.primary,
    marginLeft: 6,
  },
  meetGreetButtonRow: {
    marginBottom: 12,
  },
  meetGreetButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: colors.successLight,
    borderWidth: 1,
    borderColor: colors.success,
    borderRadius: 12,
    paddingVertical: 12,
    paddingHorizontal: 16,
  },
  meetGreetButtonText: {
    fontSize: 14,
    fontWeight: '600',
    color: colors.success,
    marginLeft: 6,
  },
  acceptArrivalButton: {
    backgroundColor: colors.success,
    borderRadius: 12,
    paddingVertical: 14,
    paddingHorizontal: 24,
    flex: 2,
    alignItems: 'center',
    justifyContent: 'center',
  },
  acceptDepartureButton: {
    backgroundColor: colors.primary,
    borderRadius: 12,
    paddingVertical: 14,
    paddingHorizontal: 24,
    flex: 2,
    alignItems: 'center',
    justifyContent: 'center',
  },
  acceptArrivalButtonText: {
    fontSize: 16,
    fontWeight: '700',
    color: '#FFFFFF',
  },
  acceptDepartureButtonText: {
    fontSize: 16,
    fontWeight: '700',
    color: '#FFFFFF',
  },
  schedulingSection: {
    marginBottom: 24,
  },
  schedulingCard: {
    backgroundColor: colors.backgroundTertiary,
    borderRadius: 16,
    padding: 16,
    borderWidth: 1,
    borderColor: colors.primary,
  },
  schedulingItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  schedulingLabel: {
    fontSize: 14,
    color: colors.textSecondary,
    marginLeft: 12,
    flex: 1,
  },
  schedulingValue: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.text,
  },
  immediateSection: {
    marginBottom: 24,
  },
  immediateCard: {
    backgroundColor: colors.errorLight,
    borderRadius: 16,
    padding: 16,
    borderWidth: 1,
    borderColor: colors.error,
  },
  immediateItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  immediateLabel: {
    fontSize: 14,
    color: colors.textSecondary,
    marginLeft: 12,
    flex: 1,
  },
  immediateValue: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.text,
  },
  routeSection: {
    marginBottom: 24,
  },
  routeContainer: {
    backgroundColor: colors.backgroundTertiary,
    borderRadius: 16,
    padding: 16,
  },
  routeItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
  },
  routeLine: {
    alignItems: 'center',
    paddingVertical: 4,
  },
  routeText: {
    marginLeft: 12,
    flex: 1,
  },
  routeLabel: {
    fontSize: 14,
    fontWeight: '600',
    color: colors.textSecondary,
    marginBottom: 4,
  },
  routeAddress: {
    fontSize: 16,
    color: colors.text,
    lineHeight: 20,
  },
  tripDetailsSection: {
    marginBottom: 24,
  },
  tripMetrics: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  metricItem: {
    alignItems: 'center',
    flex: 1,
    padding: 16,
    backgroundColor: colors.backgroundTertiary,
    borderRadius: 16,
    marginHorizontal: 4,
  },
  metricValue: {
    fontSize: 18,
    fontWeight: '700',
    color: colors.text,
    marginTop: 8,
    marginBottom: 4,
  },
  metricLabel: {
    fontSize: 12,
    color: colors.textSecondary,
    fontWeight: '500',
  },
  actionButtons: {
    marginTop: 16,
    gap: 12,
  },
  secondaryButtonsRow: {
    flexDirection: 'row',
    gap: 8,
    marginBottom: 12,
  },
  secondaryButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: colors.backgroundTertiary,
    borderWidth: 1,
    borderColor: colors.primary,
    borderRadius: 12,
    paddingVertical: 12,
    paddingHorizontal: 12,
    flex: 1,
  },
  reminderButtonRow: {
    marginBottom: 12,
  },
  reminderButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: colors.warningLight,
    borderWidth: 1,
    borderColor: '#FF9500',
    borderRadius: 12,
    paddingVertical: 12,
    paddingHorizontal: 16,
  },
  reminderButtonText: {
    fontSize: 14,
    fontWeight: '600',
    color: colors.warning,
    marginLeft: 6,
  },
  onMyWayButtonRow: {
    marginBottom: 12,
  },
  onMyWayButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: colors.successLight,
    borderWidth: 1,
    borderColor: colors.success,
    borderRadius: 12,
    paddingVertical: 12,
    paddingHorizontal: 16,
  },
  onMyWayButtonText: {
    fontSize: 14,
    fontWeight: '600',
    color: colors.success,
    marginLeft: 6,
  },
  secondaryButtonText: {
    fontSize: 14,
    fontWeight: '600',
    color: colors.primary,
    marginLeft: 6,
  },
  mainButtonsRow: {
    flexDirection: 'row',
    gap: 12,
  },
  declineButton: {
    backgroundColor: colors.errorLight,
    borderWidth: 1,
    borderColor: colors.error,
    borderRadius: 12,
    paddingVertical: 14,
    paddingHorizontal: 24,
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  declineButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.error,
  },
  acceptButton: {
    backgroundColor: colors.success,
    borderRadius: 12,
    paddingVertical: 14,
    paddingHorizontal: 24,
    flex: 2,
    alignItems: 'center',
    justifyContent: 'center',
  },
  scheduleButton: {
    backgroundColor: colors.primary,
    borderRadius: 12,
    paddingVertical: 14,
    paddingHorizontal: 24,
    flex: 2,
    alignItems: 'center',
    justifyContent: 'center',
  },
  scheduleButtonText: {
    fontSize: 16,
    fontWeight: '700',
    color: '#FFFFFF',
  },
  driverMarkerContainer: {
    backgroundColor: colors.success,
    padding: 8,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
    width: 40,
    height: 40,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 4,
    elevation: 5,
    borderWidth: 2,
    borderColor: colors.background,
  },
  acceptButtonText: {
    fontSize: 16,
    fontWeight: '700',
    color: '#FFFFFF',
  },
  destinationMarker: {
    width: 30,
    height: 30,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: colors.background,
    borderRadius: 15,
    borderWidth: 2,
    borderColor: colors.error,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 3,
    elevation: 5,
  },
  mapControls: {
    position: 'absolute',
    top: 20,
    right: 16,
    zIndex: 1000,
  },
  controlButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.background,
    borderRadius: 20,
    paddingHorizontal: 12,
    paddingVertical: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 4,
    borderWidth: 1,
    borderColor: colors.primary,
    marginBottom: 8,
  },
  controlButtonActive: {
    backgroundColor: colors.primary,
    borderColor: colors.primary,
  },
  controlButtonText: {
    fontSize: 12,
    fontWeight: '600',
    color: colors.primary,
    marginLeft: 4,
  },
  controlButtonTextActive: {
    color: '#FFFFFF',
  },
  mapLegend: {
    position: 'absolute',
    bottom: 120,
    left: 16,
    backgroundColor: colors.background,
    borderRadius: 12,
    padding: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 4,
    borderWidth: 1,
    borderColor: colors.border,
  },
  legendTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: colors.text,
    marginBottom: 8,
  },
  legendItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 6,
  },
  legendDot: {
    width: 12,
    height: 12,
    borderRadius: 6,
    borderWidth: 2,
    borderColor: colors.background,
    marginRight: 8,
  },
  legendText: {
    fontSize: 12,
    color: colors.textSecondary,
  },
});

export default MapScreen;