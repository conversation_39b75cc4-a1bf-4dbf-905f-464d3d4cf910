import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, ScrollView, Alert, TouchableOpacity, Linking, Modal } from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import BottomSpacer from '../components/BottomSpacer';
import { useAuth, useApp } from '../context/ApiIntegratedGlobalStateContext';
import Button from '../components/Button';
import Card from '../components/Card';
// Removed Header import
import ExpandableCustomerDetails from '../components/ExpandableCustomerDetails';
import TransferNavigationModal from '../components/TransferNavigationModal';
import { Ride } from '../types';
import { shadows } from '../theme/colors';
import { useTheme } from '../context/ThemeContext';
import { NavigationService, NavigationStep } from '../services/NavigationService';
import GoogleMapsNavigation from '../components/GoogleMapsNavigation';
import WazeNavigator from '../components/WazeNavigator';
import EnhancedWazeNavigator from '../components/EnhancedWazeNavigator';
import { statusFilters } from '../data/mockData';

interface DashboardScreenProps {}

const DashboardScreen: React.FC<DashboardScreenProps> = () => {
  const { colors } = useTheme();
  const { state: authState } = useAuth();
  const { 
    state: appState, 
    setCurrentRide, 
    addQueuedBooking, 
    setRideStatus, 
    setQueueStatus, 
    shouldShowBookings, 
    canAcceptNewBooking,
    getMaxQueueSize,
    removeAvailableRide,
    loadAvailableRides,
    // Unified booking management
    acceptBooking,
    rejectBooking,
    getAllBookings,
    getActiveBookings,
    // Data loading methods
    refreshAllData
  } = useApp();
  const [isSearching, setIsSearching] = useState(false);
  const [expandedRideId, setExpandedRideId] = useState<string | null>(null);
  const [expandedCustomerId, setExpandedCustomerId] = useState<string | null>(null);
  const [showAllRequests, setShowAllRequests] = useState(false);
  const [showRejectedRequests, setShowRejectedRequests] = useState(false);
  const [showNavigationModal, setShowNavigationModal] = useState(false);
  const [isTestingNavigation, setIsTestingNavigation] = useState(false);
  const [showTestNavigationModal, setShowTestNavigationModal] = useState(false);
  const [showWazeNavigator, setShowWazeNavigator] = useState(false);
  const [currentNavigationStep, setCurrentNavigationStep] = useState<NavigationStep | null>(null);
  const [navigationStepIndex, setNavigationStepIndex] = useState(0);
  const [navigationRoute, setNavigationRoute] = useState<any>(null);

  useEffect(() => {
    // Load available rides when authenticated and have location
    if (authState.isAuthenticated && appState.location && !appState.loading.rides) {
      setIsSearching(true);
      
      loadAvailableRides().finally(() => {
        setIsSearching(false);
      });
    }
  }, [authState.isAuthenticated, appState.location, loadAvailableRides]);

  // Refresh data periodically when online
  useEffect(() => {
    if (!authState.isAuthenticated || !appState.isOnline) return;

    const interval = setInterval(() => {
      loadAvailableRides().catch(error => {
        console.warn('Failed to refresh available rides:', error);
      });
    }, 30000); // Refresh every 30 seconds

    return () => clearInterval(interval);
  }, [authState.isAuthenticated, appState.isOnline, loadAvailableRides]);

  // Optional: Generate additional rides periodically (disabled for now to use centralized data)
  // useEffect(() => {
  //   const interval = setInterval(() => {
  //     // Future: Add new rides to centralized state if needed
  //   }, 20000);
  //   return () => clearInterval(interval);
  // }, [shouldShowBookings, appState.availableRides.length]);

  const handleAcceptRide = async (ride: Ride) => {
    if (!canAcceptNewBooking()) {
      // Show specific reason why can't accept
      if (appState.rideStatus === 'passenger_onboard' || appState.rideStatus === 'arrived_at_destination') {
        Alert.alert('Cannot Accept', 'You are currently serving a passenger. Complete your trip to accept new bookings.');
      } else if (appState.queuedBookings.length >= getMaxQueueSize()) {
        Alert.alert('Queue Full', `You already have ${appState.queuedBookings.length} bookings queued. Complete a trip to add more.`);
      } else {
        Alert.alert('Cannot Accept', 'Unable to accept booking at this time.');
      }
      return;
    }
    
    try {
      const success = await acceptBooking(ride.id);
      if (success) {
        if (appState.currentRide) {
          const queuePosition = appState.queuedBookings.length + 1;
          Alert.alert(
            'Booking Queued', 
            `${ride.passengerName}'s trip has been added to your queue (#${queuePosition}/${getMaxQueueSize()}).`
          );
        } else {
          Alert.alert(
            'Booking Accepted', 
            `Trip to ${ride.dropoffLocation.address} accepted! Navigate to pickup location.`
          );
        }
        
        // Close expanded details after accepting
        if (expandedRideId === ride.id) {
          setExpandedRideId(null);
        }
      } else {
        Alert.alert('Error', 'Failed to accept booking. Please try again.');
      }
    } catch (error: any) {
      Alert.alert('Error', error.message || 'Failed to accept booking. Please try again.');
    }
  };

  const handleDeclineRide = async (rideId: string) => {
    try {
      await rejectBooking(rideId);
      
      // Also close expanded details when rejecting
      if (expandedRideId === rideId) {
        setExpandedRideId(null);
      }
    } catch (error: any) {
      Alert.alert('Error', error.message || 'Failed to reject booking. Please try again.');
    }
  };

  const handleCancelCurrentRide = () => {
    if (!appState.currentRide) return;
    
    Alert.alert(
      'Cancel Current Ride',
      `Are you sure you want to cancel the ride with ${appState.currentRide.passengerName}?`,
      [
        { text: 'No', style: 'cancel' },
        { 
          text: 'Yes, Cancel', 
          style: 'destructive',
          onPress: async () => {
            try {
              await rejectBooking(appState.currentRide!.id);
              setCurrentRide(null);
              setRideStatus(null);
              // Start next queued ride if available
              const nextRide = appState.queuedBookings[0];
              if (nextRide) {
                setCurrentRide(nextRide);
                removeQueuedBooking(nextRide.id);
                setRideStatus('heading_to_pickup');
              }
              Alert.alert('Ride Cancelled', 'The current ride has been cancelled.');
            } catch (error: any) {
              Alert.alert('Error', error.message || 'Failed to cancel ride. Please try again.');
            }
          }
        }
      ]
    );
  };

  const toggleRideDetails = (rideId: string) => {
    setExpandedRideId(expandedRideId === rideId ? null : rideId);
  };

  const toggleCustomerDetails = (rideId: string) => {
    setExpandedCustomerId(expandedCustomerId === rideId ? null : rideId);
  };

  const handleCallPassenger = (phoneNumber: string, passengerName: string) => {
    Alert.alert(
      'Call Passenger',
      `Call ${passengerName} at ${phoneNumber}?`,
      [
        { text: 'Cancel', style: 'cancel' },
        { 
          text: 'Call', 
          onPress: () => {
            const phoneUrl = `tel:${phoneNumber}`;
            Linking.canOpenURL(phoneUrl)
              .then(supported => {
                if (supported) {
                  return Linking.openURL(phoneUrl);
                } else {
                  Alert.alert('Error', 'Phone calls are not supported on this device');
                }
              })
              .catch(err => {
                console.error('Error opening phone app:', err);
                Alert.alert('Error', 'Failed to open phone app');
              });
          }
        }
      ]
    );
  };

  const handleMessagePassenger = (phoneNumber: string, passengerName: string) => {
    Alert.alert(
      'Message Passenger',
      `Send SMS to ${passengerName}?`,
      [
        { text: 'Cancel', style: 'cancel' },
        { 
          text: 'Send SMS', 
          onPress: () => {
            const smsUrl = `sms:${phoneNumber}`;
            Linking.canOpenURL(smsUrl)
              .then(supported => {
                if (supported) {
                  return Linking.openURL(smsUrl);
                } else {
                  Alert.alert('Error', 'SMS is not supported on this device');
                }
              })
              .catch(err => {
                console.error('Error opening SMS app:', err);
                Alert.alert('Error', 'Failed to open SMS app');
              });
          }
        }
      ]
    );
  };

  const handleTestNavigation = async () => {
    setIsTestingNavigation(true);
    try {
      // Coordinates for Alicia, Bohol to Tagbilaran
      const aliciaCoords = { latitude: 9.9167, longitude: 124.4167 }; // Alicia, Bohol
      const tagbilaranCoords = { latitude: 9.6496, longitude: 123.8547 }; // Tagbilaran City
      
      // Get the route first to show on the map
      const navigationService = NavigationService.getInstance();
      const route = await navigationService.getDetailedDirections(aliciaCoords, tagbilaranCoords);
      
      if (route) {
        setNavigationRoute(route);
        setCurrentNavigationStep(route.steps[0]);
        setNavigationStepIndex(0);
        setShowTestNavigationModal(true);
        setIsTestingNavigation(false);
      } else {
        Alert.alert('Error', 'Could not get directions for test route');
        setIsTestingNavigation(false);
      }
    } catch (error) {
      console.error('Navigation test error:', error);
      Alert.alert('Error', 'Failed to start test navigation');
      setIsTestingNavigation(false);
    }
  };

  const handleStartTurnByTurnNavigation = async () => {
    if (!navigationRoute) return;

    setIsTestingNavigation(true);
    try {
      const aliciaCoords = { latitude: 9.9167, longitude: 124.4167 };
      const tagbilaranCoords = { latitude: 9.6496, longitude: 123.8547 };
      
      const navigationService = NavigationService.getInstance();
      const success = await navigationService.startTurnByTurnNavigationInternal(
        aliciaCoords,
        tagbilaranCoords,
        (step, stepIndex) => {
          setCurrentNavigationStep(step);
          setNavigationStepIndex(stepIndex);
          console.log(`Step ${stepIndex + 1}: ${step.instruction}`);
        },
        () => {
          Alert.alert('Navigation Complete', 'You have arrived at Tagbilaran City!');
          setIsTestingNavigation(false);
          setShowTestNavigationModal(false);
        }
      );
      
      if (success) {
        Alert.alert('Navigation Started', 'Turn-by-turn navigation with voice guidance has started!');
      } else {
        Alert.alert('Navigation Failed', 'Could not start navigation. Please check your location permissions.');
        setIsTestingNavigation(false);
      }
    } catch (error) {
      console.error('Navigation start error:', error);
      Alert.alert('Error', 'Failed to start turn-by-turn navigation');
      setIsTestingNavigation(false);
    }
  };

  const handleStopNavigation = () => {
    const navigationService = NavigationService.getInstance();
    navigationService.stopNavigation();
    setIsTestingNavigation(false);
    setCurrentNavigationStep(null);
    setNavigationStepIndex(0);
    setShowTestNavigationModal(false);
  };

  const styles = createStyles(colors);

  return (
    <View style={styles.screenContainer}>
      <ScrollView style={styles.container}>

      {/* Current Transfer - TOP PRIORITY when active */}
      {appState.currentRide && (
        <Card>
          <View style={styles.currentTransferHeader}>
            <Text style={styles.cardTitle}>Current Transfer</Text>
            <TouchableOpacity 
              style={styles.navigateToCustomerButton}
              onPress={() => toggleCustomerDetails(appState.currentRide?.id || '')}
              activeOpacity={0.7}
            >
              <MaterialIcons name="person" size={20} color={colors.primary} />
              <Text style={styles.navigateToCustomerText}>Customer</Text>
            </TouchableOpacity>
          </View>
          
          <View style={styles.transferTypeContainer}>
            <Text style={[styles.transferTypeBadge, 
              appState.currentRide.transferType === 'arrival' ? styles.arrivalBadge : styles.departureBadge]}>
              {appState.currentRide.transferType === 'arrival' ? 'ARRIVAL' : 'DEPARTURE'}
            </Text>
          </View>
          
          <View style={styles.currentTransferMainInfo}>
            <View style={styles.passengerSection}>
              <Text style={styles.passengerName}>{appState.currentRide.passengerName}</Text>
              <View style={styles.flightInfoContainer}>
                <Text style={styles.flightInfo}>{appState.currentRide.flightNumber} • {appState.currentRide.airport}</Text>
                {appState.currentRide.flightStatus && (
                  <View style={styles.flightStatusBadge}>
                    <Text style={styles.flightStatusText}>
                      {appState.currentRide.flightStatus.replace('_', ' ').toUpperCase()}
                    </Text>
                  </View>
                )}
              </View>
            </View>
            
            <View style={styles.fareSection}>
              <Text style={styles.fareAmount}>£{appState.currentRide.fare.toFixed(2)}</Text>
            </View>
          </View>
          
          <View style={styles.routeInformation}>
            <View style={styles.routePoint}>
              <MaterialIcons name="radio-button-checked" size={12} color={colors.success} />
              <Text style={styles.routeAddress} numberOfLines={2}>
                {appState.currentRide.pickupLocation.address}
              </Text>
            </View>
            <View style={styles.routeLine} />
            <View style={styles.routePoint}>
              <MaterialIcons name="place" size={12} color={colors.error} />
              <Text style={styles.routeAddress} numberOfLines={2}>
                {appState.currentRide.dropoffLocation.address}
              </Text>
            </View>
          </View>

          <View style={styles.currentTransferActions}>
            <TouchableOpacity
              style={styles.cancelButton}
              onPress={handleCancelCurrentRide}
              activeOpacity={0.7}
            >
              <MaterialIcons name="cancel" size={20} color={colors.textLight} />
              <Text style={styles.cancelButtonText}>Cancel</Text>
            </TouchableOpacity>
            
            <TouchableOpacity
              style={styles.navigationButton}
              onPress={() => setShowNavigationModal(true)}
              activeOpacity={0.7}
            >
              <MaterialIcons name="navigation" size={20} color={colors.textLight} />
              <Text style={styles.navigationButtonText}>Navigate</Text>
            </TouchableOpacity>
          </View>
          
          {/* Current Ride Customer Details */}
          <ExpandableCustomerDetails 
            ride={appState.currentRide}
            isExpanded={expandedCustomerId === appState.currentRide?.id}
            onToggle={() => toggleCustomerDetails(appState.currentRide?.id || '')}
            showActions={true}
          />
        </Card>
      )}


      {/* Pending Requests Summary */}
      {(() => {
        const pendingRides = statusFilters.getAvailableRides(appState.availableRides);
        return shouldShowBookings() && !appState.currentRide && pendingRides.length > 0 && (
          <Card>
            <View style={styles.pendingRequestsHeaderCompact}>
              <Text style={styles.cardTitle}>New Requests</Text>
              <View style={styles.pendingRequestsBadge}>
                <Text style={styles.pendingRequestsCount}>{pendingRides.length}</Text>
              </View>
            </View>
            <View style={styles.pendingRequestsCompact}>
              {pendingRides.slice(0, 4).map(request => (
                <View key={request.id} style={styles.pendingRequestCompactItem}>
                  <View style={styles.pendingRequestCompactLeft}>
                    <Text style={styles.pendingRequestCompactFlight}>{request.flightNumber}</Text>
                    <Text style={styles.pendingRequestCompactAirport}>{request.airport}</Text>
                  </View>
                  <Text style={styles.pendingRequestCompactFare}>£{request.fare.toFixed(0)}</Text>
                </View>
              ))}
              {pendingRides.length > 4 && (
                <Text style={styles.moreRequestsText}>+{pendingRides.length - 4} more</Text>
              )}
            </View>
          </Card>
        );
      })()}

      {/* Recently Rejected Requests */}
      {(() => {
        const rejectedRides = statusFilters.getRejectedRides(appState.availableRides);
        return !appState.currentRide && rejectedRides.length > 0 && (
          <Card>
            <View style={styles.rejectedRequestsHeader}>
              <Text style={styles.cardTitle}>Recently Declined</Text>
              <View style={styles.rejectedRequestsBadge}>
                <Text style={styles.rejectedRequestsCount}>{rejectedRides.length}</Text>
              </View>
            </View>
            <View style={styles.rejectedRequestsCompact}>
              {rejectedRides.slice(0, 5).map(request => (
                <View key={request.id} style={styles.rejectedRequestCompactItem}>
                  <View style={styles.rejectedRequestCompactLeft}>
                    <Text style={styles.rejectedRequestCompactFlight}>{request.flightNumber}</Text>
                    <Text style={styles.rejectedRequestCompactAirport}>{request.airport}</Text>
                  </View>
                  <View style={styles.rejectedRequestCompactRight}>
                    <Text style={styles.rejectedRequestCompactFare}>£{request.fare.toFixed(0)}</Text>
                    <Text style={styles.rejectedStatusText}>DECLINED</Text>
                  </View>
                </View>
              ))}
              {rejectedRides.length > 5 && (
                <Text style={styles.moreRejectedText}>+{rejectedRides.length - 5} more declined</Text>
              )}
            </View>
          </Card>
        );
      })()}


      {(() => {
        const allVisibleRides = [...statusFilters.getAvailableRides(appState.availableRides), ...statusFilters.getRejectedRides(appState.availableRides)];
        return shouldShowBookings() && !appState.currentRide && allVisibleRides.map(ride => (
        <Card key={ride.id} style={ride.status === 'rejected' ? styles.rejectedBookingCard : undefined}>
          <View style={styles.transferRequestHeader}>
            <Text style={styles.transferRequestTitle}>Airport Transfer</Text>
            <View style={styles.transferRequestHeaderRight}>
              <Text style={[styles.transferTypeBadgeCompact, 
                ride.transferType === 'arrival' ? styles.arrivalBadge : styles.departureBadge]}>
                {ride.transferType === 'arrival' ? 'ARRIVAL' : 'DEPARTURE'}
              </Text>
              {ride.status === 'rejected' && (
                <View style={styles.rejectedStatusBadge}>
                  <MaterialIcons name="cancel" size={14} color={colors.error} />
                  <Text style={styles.rejectedStatusText}>REJECTED</Text>
                </View>
              )}
              <TouchableOpacity 
                onPress={() => toggleRideDetails(ride.id)}
                style={styles.expandButton}
                activeOpacity={0.7}
              >
                <MaterialIcons 
                  name={expandedRideId === ride.id ? "expand-less" : "expand-more"} 
                  size={24} 
                  color={colors.textSecondary} 
                />
              </TouchableOpacity>
            </View>
          </View>
          
          {/* Main Booking Info - Simplified */}
          <View style={styles.bookingMainInfo}>
            <View style={styles.passengerMainRow}>
              <View style={styles.passengerMainLeft}>
                <Text style={styles.passengerNameMain}>{ride.passengerName}</Text>
                <Text style={styles.flightInfoMain}>{ride.flightNumber} • {ride.airport}</Text>
              </View>
              <View style={styles.passengerMainRight}>
                <Text style={styles.fareAmountMain}>£{ride.fare.toFixed(0)}</Text>
                <Text style={styles.fareDetailsMain}>{ride.distance}km • {ride.duration}min</Text>
              </View>
            </View>
            
            <View style={styles.locationMainInfo}>
              <View style={styles.locationMainRow}>
                <MaterialIcons name="location-on" size={12} color={colors.success} />
                <Text style={styles.locationMainText} numberOfLines={2}>{ride.pickupLocation.address}</Text>
              </View>
              <View style={styles.locationMainRow}>
                <MaterialIcons name="place" size={12} color={colors.error} />
                <Text style={styles.locationMainText} numberOfLines={2}>{ride.dropoffLocation.address}</Text>
              </View>
            </View>
          </View>

          {expandedRideId === ride.id && (
            <View style={styles.rideDetailsExpanded}>
              <View style={styles.expandedRow}>
                <View style={styles.expandedLeft}>
                  <Text style={styles.expandedLabel}>Contact</Text>
                  <Text style={styles.expandedValue}>{ride.passengerPhone}</Text>
                  <Text style={styles.expandedValue}>{ride.passengerCount > 1 ? `${ride.passengerCount} passengers` : '1 passenger'}</Text>
                </View>
                <View style={styles.expandedRight}>
                  <Text style={styles.expandedLabel}>Vehicle</Text>
                  <Text style={styles.expandedValue}>{ride.vehicleType.replace('_', ' ')}</Text>
                  <Text style={styles.expandedValue}>{ride.paymentMethod.toUpperCase()}</Text>
                </View>
              </View>
              
              {ride.specialRequests && (
                <View style={styles.expandedSpecialRequests}>
                  <Text style={styles.expandedLabel}>Special Requests</Text>
                  <Text style={styles.expandedValue}>{ride.specialRequests}</Text>
                </View>
              )}
              
            </View>
          )}

          {/* Show customer details */}
          {/* <ExpandableCustomerDetails 
            ride={ride}
            isExpanded={expandedCustomerId === ride.id}
            onToggle={() => toggleCustomerDetails(ride.id)}
            showActions={false}
          />
           */}
          {/* Single set of communication buttons */}
          <View style={styles.communicationActions}>
            <TouchableOpacity
              style={styles.callButton}
              onPress={() => handleCallPassenger(ride.passengerPhone, ride.passengerName)}
              activeOpacity={0.7}
            >
              <MaterialIcons name="phone" size={16} color={colors.textLight} />
              <Text style={styles.actionText}>Call</Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={styles.messageButton}
              onPress={() => handleMessagePassenger(ride.passengerPhone, ride.passengerName)}
              activeOpacity={0.7}
            >
              <MaterialIcons name="message" size={16} color={colors.textLight} />
              <Text style={styles.actionText}>Message</Text>
            </TouchableOpacity>
          </View>

          {ride.status === 'rejected' ? (
            <View style={styles.rejectedActionsContainer}>
              <View style={styles.rejectedStatusContainer}>
                <MaterialIcons name="info" size={16} color={colors.error} />
                <Text style={styles.rejectedMessage}>This booking was declined and is no longer available</Text>
              </View>
            </View>
          ) : (
            <View style={styles.rideActionsCompact}>
              <Button
                title="Decline"
                onPress={() => handleDeclineRide(ride.id)}
                variant="secondary"
                style={styles.actionButtonCompact}
              />
              <Button
                title={
                  !appState.currentRide ? "Accept" :
                  canAcceptNewBooking() ? `Queue (${appState.queuedBookings.length + 1}/${getMaxQueueSize()})` :
                  "Queue Full"
                }
                onPress={() => handleAcceptRide(ride)}
                variant={canAcceptNewBooking() ? "success" : "secondary"}
                style={styles.actionButtonCompact}
                disabled={!canAcceptNewBooking()}
              />
            </View>
          )}
        </Card>
        ));
      })()}


      {!shouldShowBookings() && appState.currentRide && (
        <Card>
          <View style={styles.serviceMessageContainer}>
            <Text style={styles.serviceMessageIcon}>🚗</Text>
            <Text style={styles.serviceMessageTitle}>Focus Mode - In Service</Text>
            <Text style={styles.serviceMessageText}>
              New bookings are hidden while you're serving your passenger. 
              Complete your current trip to see new requests.
            </Text>
            {appState.queuedBookings.length > 0 && (
              <Text style={styles.serviceMessageNote}>
                You have {appState.queuedBookings.length} booking{appState.queuedBookings.length > 1 ? 's' : ''} queued for after this trip.
              </Text>
            )}
          </View>
        </Card>
      )}

      {statusFilters.getAvailableRides(appState.availableRides).length === 0 && !appState.currentRide && (
        <Card>
          {isSearching ? (
            <View style={styles.loadingContainer}>
              <Text style={styles.loadingText}>🔍 Searching for airport transfers...</Text>
              <Text style={styles.loadingSubtext}>Looking for nearby flights and bookings</Text>
            </View>
          ) : (
            <View style={styles.waitingContainer}>
              <Text style={styles.waitingText}>✈️ No transfer requests available</Text>
              <Text style={styles.waitingSubtext}>New airport transfers will appear here</Text>
            </View>
          )}
        </Card>
      )}

      {/* Queued Bookings - MOVED TO BOTTOM */}
      {appState.queuedBookings.length > 0 && (
        <Card>
          <View style={styles.queuedBookingHeader}>
            <Text style={styles.cardTitle}>Queued Bookings</Text>
            <View style={styles.queuedBadge}>
              <Text style={styles.queuedBadgeText}>{appState.queuedBookings.length}/{getMaxQueueSize()}</Text>
            </View>
          </View>
          {appState.queuedBookings.map((booking, index) => (
            <View key={booking.id} style={[styles.bookingMainInfo, index > 0 && styles.queuedBookingSpacing]}>
              <View style={styles.queuedBookingPosition}>
                <Text style={styles.queuePositionText}>#{index + 1}</Text>
              </View>
              <View style={styles.passengerMainRow}>
                <View style={styles.passengerMainLeft}>
                  <Text style={styles.passengerNameMain}>{booking.passengerName}</Text>
                  <Text style={styles.flightInfoMain}>{booking.flightNumber} • {booking.airport}</Text>
                </View>
                <View style={styles.passengerMainRight}>
                  <Text style={styles.fareAmountMain}>£{booking.fare.toFixed(0)}</Text>
                  <Text style={styles.fareDetailsMain}>{booking.distance}km • {booking.duration}min</Text>
                </View>
              </View>
              <Text style={styles.queueNote}>
                {index === 0 ? 'Next after current trip' : `Queue position #${index + 1}`}
              </Text>
            </View>
          ))}
        </Card>
      )}

      
      {/* Add bottom spacer for all pages except map and auth */}
      {!isTestingNavigation && <BottomSpacer />}
    </ScrollView>

    {/* Transfer Navigation Modal */}
    <TransferNavigationModal
      visible={showNavigationModal}
      ride={appState.currentRide}
      rideStatus={appState.rideStatus}
      onClose={() => setShowNavigationModal(false)}
      onNavigationStart={() => {
        setShowNavigationModal(false);
        Alert.alert('Navigation Started', 'Opening navigation app...');
      }}
    />

    {/* Test Navigation Modal */}
    <Modal
      visible={showTestNavigationModal}
      animationType="slide"
      presentationStyle="fullScreen"
      onRequestClose={() => setShowTestNavigationModal(false)}
    >
      <View style={styles.navigationModalContainer}>
        <View style={styles.navigationHeader}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => setShowTestNavigationModal(false)}
            activeOpacity={0.7}
          >
            <MaterialIcons name="arrow-back" size={24} color={colors.textLight} />
          </TouchableOpacity>
          <Text style={styles.navigationTitle}>Test Navigation</Text>
          <TouchableOpacity
            style={styles.stopButton}
            onPress={handleStopNavigation}
            activeOpacity={0.7}
          >
            <MaterialIcons name="stop" size={24} color={colors.textLight} />
          </TouchableOpacity>
        </View>

        <View style={styles.navigationMapContainer}>
          <GoogleMapsNavigation
            origin={{ latitude: 9.9167, longitude: 124.4167 }} // Alicia
            destination={{ latitude: 9.6496, longitude: 123.8547 }} // Tagbilaran
            onNavigationProgress={(progress) => console.log('Navigation progress:', progress)}
          />
        </View>

        {/* Navigation Instructions */}
        <View style={styles.navigationInstructions}>
          <View style={styles.routeInfo}>
            <Text style={styles.routeFromTo}>Alicia, Bohol → Tagbilaran City</Text>
            {navigationRoute && (
              <View style={styles.routeStats}>
                <Text style={styles.routeStat}>Distance: {navigationRoute.totalDistance}</Text>
                <Text style={styles.routeStat}>Duration: {navigationRoute.totalDuration}</Text>
              </View>
            )}
          </View>

          {currentNavigationStep && (
            <View style={styles.currentStep}>
              <View style={styles.stepHeader}>
                <MaterialIcons name="navigation" size={20} color={colors.primary} />
                <Text style={styles.stepNumber}>Step {navigationStepIndex + 1}</Text>
                <Text style={styles.stepDistance}>{currentNavigationStep.distance}</Text>
              </View>
              <Text style={styles.stepInstruction}>{currentNavigationStep.instruction}</Text>
            </View>
          )}

          <View style={styles.navigationControls}>
            {!isTestingNavigation ? (
              <TouchableOpacity
                style={styles.startNavigationButton}
                onPress={handleStartTurnByTurnNavigation}
                activeOpacity={0.7}
              >
                <MaterialIcons name="play-arrow" size={24} color={colors.textLight} />
                <Text style={styles.startNavigationText}>Start Turn-by-Turn</Text>
              </TouchableOpacity>
            ) : (
              <TouchableOpacity
                style={styles.stopNavigationButton}
                onPress={handleStopNavigation}
                activeOpacity={0.7}
              >
                <MaterialIcons name="stop" size={24} color={colors.textLight} />
                <Text style={styles.stopNavigationText}>Stop Navigation</Text>
              </TouchableOpacity>
            )}
          </View>

          {navigationRoute && navigationRoute.steps && (
            <ScrollView style={styles.stepsList}>
              <Text style={styles.stepsListTitle}>All Steps:</Text>
              {navigationRoute.steps.map((step: NavigationStep, index: number) => (
                <View 
                  key={index} 
                  style={[
                    styles.stepItem,
                    index === navigationStepIndex && styles.activeStepItem
                  ]}
                >
                  <View style={styles.stepItemHeader}>
                    <Text style={styles.stepItemNumber}>{index + 1}</Text>
                    <Text style={styles.stepItemDistance}>{step.distance}</Text>
                  </View>
                  <Text style={[
                    styles.stepItemInstruction,
                    index === navigationStepIndex && styles.activeStepInstruction
                  ]}>
                    {step.instruction}
                  </Text>
                </View>
              ))}
            </ScrollView>
          )}
        </View>
      </View>
    </Modal>

    {/* Waze-Style Navigator Modal */}
    <Modal
      visible={showWazeNavigator}
      animationType="slide"
      presentationStyle="fullScreen"
      onRequestClose={() => setShowWazeNavigator(false)}
    >
      <WazeNavigator
        origin={{ latitude: 9.9167, longitude: 124.4167 }} // Alicia, Bohol
        destination={{ latitude: 9.6496, longitude: 123.8547 }} // Tagbilaran City
        currentInstruction={{
          distance: "",
          instruction: "Continue straight",
          roadName: "Bohol Circumferential Road",
          maneuver: "straight",
          duration: "min"
        }}
        currentSpeed={0}
        totalDistance="90 km"
        totalDuration="2 hr 4 min"
        onClose={() => setShowWazeNavigator(false)}
      />
    </Modal>

    </View>
  );
};

const createStyles = (colors: any) => StyleSheet.create({
  screenContainer: {
    flex: 1,
    backgroundColor: colors.backgroundTertiary,
  },
  container: {
    flex: 1,
    backgroundColor: colors.backgroundTertiary,
    paddingHorizontal: 16,
    paddingTop: 8,
    paddingBottom: 120,
  },
  profileButton: {
    padding: 8,
  },
  cardTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 12,
    color: colors.text,
  },
  pendingRequestsHeaderCompact: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  pendingRequestsBadge: {
    backgroundColor: colors.primary,
    borderRadius: 12,
    paddingHorizontal: 8,
    paddingVertical: 4,
    minWidth: 24,
    alignItems: 'center',
  },
  pendingRequestsCount: {
    fontSize: 14,
    fontWeight: '700',
    color: colors.textLight,
  },
  pendingRequestsCompact: {
    gap: 8,
  },
  pendingRequestCompactItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: colors.backgroundTertiary,
    padding: 10,
    borderRadius: 8,
  },
  pendingRequestCompactLeft: {
    flex: 1,
  },
  pendingRequestCompactFlight: {
    fontSize: 14,
    fontWeight: '600',
    color: colors.text,
    marginBottom: 2,
  },
  pendingRequestCompactAirport: {
    fontSize: 12,
    color: colors.textSecondary,
  },
  pendingRequestCompactFare: {
    fontSize: 16,
    fontWeight: '700',
    color: colors.success,
  },
  moreRequestsText: {
    fontSize: 12,
    color: colors.textSecondary,
    textAlign: 'center',
    fontStyle: 'italic',
    marginTop: 4,
  },
  statsRow: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  statItem: {
    alignItems: 'center',
  },
  statValue: {
    fontSize: 20,
    fontWeight: 'bold',
    color: colors.text,
  },
  statLabel: {
    fontSize: 14,
    color: colors.textSecondary,
    marginTop: 4,
  },
  passengerName: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.text,
    marginBottom: 8,
  },
  rideDetails: {
    fontSize: 14,
    color: colors.textSecondary,
    marginBottom: 4,
  },
  fareAmount: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.success,
    marginTop: 8,
    marginBottom: 16,
  },
  rideActions: {
    flexDirection: 'row',
    gap: 12,
  },
  actionButton: {
    flex: 1,
  },
  loadingContainer: {
    alignItems: 'center',
    paddingVertical: 20,
  },
  loadingText: {
    fontSize: 16,
    color: colors.primary,
    textAlign: 'center',
    fontWeight: '600',
    marginBottom: 8,
  },
  loadingSubtext: {
    fontSize: 14,
    color: colors.textSecondary,
    textAlign: 'center',
    fontStyle: 'italic',
  },
  waitingContainer: {
    alignItems: 'center',
    paddingVertical: 20,
  },
  waitingText: {
    fontSize: 16,
    color: colors.textSecondary,
    textAlign: 'center',
    fontWeight: '600',
    marginBottom: 8,
  },
  waitingSubtext: {
    fontSize: 14,
    color: colors.textTertiary,
    textAlign: 'center',
    fontStyle: 'italic',
  },
  transferTypeContainer: {
    flexDirection: 'row',
    marginBottom: 12,
    gap: 8,
  },
  transferTypeBadge: {
    fontSize: 10,
    fontWeight: '700',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    textAlign: 'center',
  },
  arrivalBadge: {
    backgroundColor: colors.successLight,
    color: colors.success,
  },
  departureBadge: {
    backgroundColor: colors.backgroundTertiary,
    color: colors.primary,
  },
  flightInfoContainer: {
    backgroundColor: colors.backgroundTertiary,
    borderRadius: 8,
    padding: 8,
    marginBottom: 12,
  },
  flightInfo: {
    fontSize: 12,
    color: colors.text,
    fontWeight: '600',
    marginBottom: 2,
  },
  flightStatus: {
    fontSize: 10,
    fontWeight: '700',
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 4,
    textAlign: 'center',
    alignSelf: 'flex-start',
  },
  landedStatus: {
    backgroundColor: colors.backgroundTertiary,
    color: colors.primary,
  },
  onTimeStatus: {
    backgroundColor: colors.successLight,
    color: colors.success,
  },
  delayedStatus: {
    backgroundColor: colors.errorLight,
    color: colors.error,
  },
  vehicleInfoContainer: {
    backgroundColor: colors.backgroundTertiary,
    borderRadius: 6,
    padding: 6,
    marginBottom: 12,
  },
  vehicleInfo: {
    fontSize: 12,
    color: colors.text,
    fontWeight: '500',
    textAlign: 'center',
  },
  statusCompact: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  statusIndicators: {
    flexDirection: 'row',
    gap: 16,
  },
  statusIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  statusIcon: {
    fontSize: 16,
  },
  statusText: {
    fontSize: 12,
    color: colors.textSecondary,
    fontWeight: '500',
  },
  transferRequestHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  transferRequestHeaderRight: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  expandButton: {
    padding: 4,
  },
  transferRequestTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.text,
  },
  transferTypeBadgeCompact: {
    fontSize: 10,
    fontWeight: '700',
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 8,
    textAlign: 'center',
  },
  transferRequestContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  transferRequestMain: {
    flex: 1,
  },
  passengerNameCompact: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.text,
    marginBottom: 4,
  },
  flightInfoCompact: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    flexWrap: 'wrap',
  },
  flightNumberCompact: {
    fontSize: 14,
    fontWeight: '600',
    color: colors.primary,
  },
  airportCompact: {
    fontSize: 12,
    color: colors.textSecondary,
  },
  transferRequestFare: {
    alignItems: 'flex-end',
  },
  fareAmountCompact: {
    fontSize: 18,
    fontWeight: '700',
    color: colors.success,
    marginBottom: 2,
  },
  distanceCompact: {
    fontSize: 12,
    color: colors.textSecondary,
  },
  rideActionsCompact: {
    flexDirection: 'row',
    gap: 8,
  },
  actionButtonCompact: {
    flex: 1,
    paddingVertical: 8,
  },
  rideDetailsExpanded: {
    backgroundColor: colors.backgroundTertiary,
    borderRadius: 12,
    padding: 16,
    marginTop: 12,
    marginBottom: 12,
  },
  detailsSection: {
    marginBottom: 16,
  },
  detailsSectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.text,
    marginBottom: 12,
  },
  detailRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
    gap: 8,
  },
  detailText: {
    fontSize: 14,
    color: colors.textSecondary,
    flex: 1,
  },
  communicationButtons: {
    flexDirection: 'row',
    gap: 12,
    marginTop: 12,
  },
  commButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 12,
    gap: 6,
  },
  commButtonText: {
    fontSize: 14,
    fontWeight: '600',
    color: colors.textLight,
  },
  rejectedRequestsHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  rejectedRequestsBadge: {
    backgroundColor: colors.error,
    borderRadius: 12,
    paddingHorizontal: 8,
    paddingVertical: 4,
    minWidth: 24,
    alignItems: 'center',
  },
  rejectedRequestsCount: {
    fontSize: 14,
    fontWeight: '700',
    color: colors.textLight,
  },
  rejectedRequestsCompact: {
    gap: 8,
  },
  rejectedRequestCompactItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: colors.errorLight,
    padding: 10,
    borderRadius: 8,
    borderLeftWidth: 3,
    borderLeftColor: colors.error,
  },
  rejectedRequestCompactLeft: {
    flex: 1,
  },
  rejectedRequestCompactRight: {
    alignItems: 'flex-end',
  },
  rejectedRequestCompactFlight: {
    fontSize: 14,
    fontWeight: '600',
    color: colors.text,
    marginBottom: 2,
  },
  rejectedRequestCompactAirport: {
    fontSize: 12,
    color: colors.textSecondary,
  },
  rejectedRequestCompactFare: {
    fontSize: 14,
    fontWeight: '600',
    color: colors.textSecondary,
    marginBottom: 2,
  },
  moreRejectedText: {
    fontSize: 12,
    color: colors.error,
    textAlign: 'center',
    fontStyle: 'italic',
    marginTop: 4,
  },
  specialRequestsContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    backgroundColor: colors.warningLight,
    padding: 12,
    borderRadius: 8,
    borderLeftWidth: 3,
    borderLeftColor: colors.warning,
    gap: 8,
  },
  specialRequestsText: {
    fontSize: 14,
    color: colors.text,
    flex: 1,
    lineHeight: 20,
  },
  passengerInfoMain: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 4,
  },
  bookingMetaInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    marginTop: 4,
  },
  bookingTimeText: {
    fontSize: 11,
    color: colors.textTertiary,
    backgroundColor: colors.backgroundTertiary,
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 6,
  },
  passengerCountText: {
    fontSize: 11,
    color: colors.textSecondary,
    backgroundColor: colors.backgroundTertiary,
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 6,
  },
  vehicleTypeText: {
    fontSize: 11,
    color: colors.primary,
    backgroundColor: colors.backgroundTertiary,
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 6,
  },
  priorityIndicatorMain: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 2,
    marginTop: 4,
  },
  priorityTextMain: {
    fontSize: 10,
    fontWeight: '700',
  },
  tripDetailsMain: {
    backgroundColor: colors.backgroundTertiary,
    borderRadius: 8,
    padding: 12,
    marginTop: 12,
    flexDirection: 'row',
    gap: 16,
  },
  locationInfoMain: {
    flex: 2,
    gap: 6,
  },
  locationRowMain: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
  },
  locationTextMain: {
    fontSize: 12,
    color: colors.text,
    flex: 1,
  },
  serviceInfoMain: {
    flex: 1,
    gap: 6,
  },
  serviceRowMain: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  serviceTextMain: {
    fontSize: 11,
    color: colors.textSecondary,
    fontWeight: '500',
  },
  specialRequestsMain: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    backgroundColor: colors.warningLight,
    padding: 10,
    borderRadius: 8,
    marginTop: 8,
    gap: 6,
  },
  specialRequestsMainText: {
    fontSize: 12,
    color: colors.text,
    flex: 1,
    lineHeight: 16,
  },
  quickActionsMain: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    gap: 8,
    marginTop: 12,
    marginBottom: 8,
  },
  quickCallButton: {
    backgroundColor: colors.successLight,
    borderRadius: 20,
    padding: 8,
    borderWidth: 1,
    borderColor: colors.success,
  },
  quickMessageButton: {
    backgroundColor: colors.backgroundTertiary,
    borderRadius: 20,
    padding: 8,
    borderWidth: 1,
    borderColor: colors.primary,
  },
  // Passenger Details Main Styles
  passengerDetailsMain: {
    backgroundColor: colors.backgroundTertiary,
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    borderWidth: 1,
    borderColor: colors.border,
  },
  passengerInfoHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
    gap: 8,
  },
  passengerDetailsSectionTitle: {
    fontSize: 16,
    fontWeight: '700',
    color: colors.primary,
  },
  passengerInfoGrid: {
    flexDirection: 'row',
    gap: 16,
  },
  passengerInfoLeft: {
    flex: 2,
  },
  passengerInfoRight: {
    flex: 1,
    alignItems: 'flex-end',
  },
  passengerNameRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  passengerContactRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
    gap: 6,
  },
  passengerPhoneText: {
    fontSize: 14,
    color: colors.text,
    fontWeight: '500',
  },
  passengerMetaRow: {
    gap: 8,
  },
  passengerMetaItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
    marginBottom: 4,
  },
  passengerMetaText: {
    fontSize: 12,
    color: colors.textSecondary,
  },
  fareDisplayMain: {
    alignItems: 'flex-end',
    marginBottom: 8,
  },
  fareAmountLarge: {
    fontSize: 24,
    fontWeight: '700',
    color: colors.success,
  },
  fareDetailsText: {
    fontSize: 12,
    color: colors.textSecondary,
    marginTop: 2,
  },
  priorityDisplayMain: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  priorityTextLarge: {
    fontSize: 12,
    fontWeight: '700',
  },
  // Flight Details Main Styles
  flightDetailsMain: {
    backgroundColor: colors.backgroundTertiary,
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    borderWidth: 1,
    borderColor: colors.border,
  },
  flightInfoHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 12,
  },
  flightDetailsSectionTitle: {
    fontSize: 16,
    fontWeight: '700',
    color: colors.text,
    flex: 1,
    marginLeft: 8,
  },
  transferTypeBadgeMain: {
    fontSize: 11,
    fontWeight: '700',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    textAlign: 'center',
  },
  arrivalBadgeMain: {
    backgroundColor: colors.successLight,
    color: colors.success,
  },
  departureBadgeMain: {
    backgroundColor: colors.backgroundTertiary,
    color: colors.primary,
  },
  flightInfoGrid: {
    flexDirection: 'row',
    gap: 16,
  },
  flightInfoLeft: {
    flex: 2,
  },
  flightInfoRight: {
    flex: 1,
    alignItems: 'flex-end',
  },
  flightNumberMain: {
    fontSize: 16,
    fontWeight: '700',
    color: colors.primary,
    marginBottom: 4,
  },
  airportTerminalText: {
    fontSize: 14,
    color: colors.text,
    marginBottom: 4,
  },
  flightStatusText: {
    fontSize: 12,
    color: colors.textSecondary,
  },
  vehicleTypeMain: {
    fontSize: 14,
    fontWeight: '600',
    color: colors.text,
    marginBottom: 6,
  },
  meetGreetBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.successLight,
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 8,
    gap: 4,
    marginBottom: 6,
  },
  meetGreetText: {
    fontSize: 11,
    fontWeight: '600',
    color: colors.success,
  },
  paymentMethodText: {
    fontSize: 12,
    color: colors.textSecondary,
  },
  // Reject Status Styles
  rejectedStatusBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.errorLight,
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    gap: 4,
    borderWidth: 1,
    borderColor: colors.error,
  },
  rejectedStatusText: {
    fontSize: 10,
    fontWeight: '700',
    color: colors.error,
  },
  rejectedBookingCard: {
    opacity: 0.7,
    borderWidth: 2,
    borderColor: colors.error,
    backgroundColor: colors.errorLight,
  },
  rejectedTimeText: {
    fontSize: 12,
    color: colors.error,
    fontWeight: '500',
  },
  rejectedActionsContainer: {
    marginTop: 12,
    marginBottom: 8,
  },
  rejectedStatusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.errorLight,
    padding: 12,
    borderRadius: 8,
    gap: 8,
    borderLeftWidth: 4,
    borderLeftColor: colors.error,
  },
  rejectedMessage: {
    flex: 1,
    fontSize: 14,
    color: colors.error,
    fontWeight: '500',
  },
  // Simplified booking info styles
  bookingMainInfo: {
    backgroundColor: colors.backgroundTertiary,
    borderRadius: 12,
    padding: 14,
    marginBottom: 12,
  },
  passengerMainRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 12,
  },
  passengerMainLeft: {
    flex: 2,
  },
  passengerMainRight: {
    flex: 1,
    alignItems: 'flex-end',
  },
  passengerNameMain: {
    fontSize: 16,
    fontWeight: '700',
    color: colors.text,
    marginBottom: 4,
  },
  flightInfoMain: {
    fontSize: 14,
    color: colors.primary,
    fontWeight: '600',
  },
  fareAmountMain: {
    fontSize: 18,
    fontWeight: '700',
    color: colors.success,
    marginBottom: 2,
  },
  fareDetailsMain: {
    fontSize: 12,
    color: colors.textSecondary,
    marginBottom: 4,
  },
  locationMainInfo: {
    gap: 6,
  },
  locationMainRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
  },
  locationMainText: {
    fontSize: 12,
    color: colors.textSecondary,
    flex: 1,
  },
  // Simplified expanded styles
  expandedRow: {
    flexDirection: 'row',
    gap: 16,
    marginBottom: 12,
  },
  expandedLeft: {
    flex: 1,
  },
  expandedRight: {
    flex: 1,
  },
  expandedLabel: {
    fontSize: 12,
    fontWeight: '600',
    color: colors.textSecondary,
    marginBottom: 4,
  },
  expandedValue: {
    fontSize: 14,
    color: colors.text,
    marginBottom: 2,
  },
  expandedSpecialRequests: {
    marginBottom: 12,
    padding: 10,
    backgroundColor: colors.warningLight,
    borderRadius: 8,
  },
  expandedActions: {
    flexDirection: 'row',
    gap: 8,
  },
  expandedCallButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: colors.success,
    paddingVertical: 10,
    borderRadius: 8,
    gap: 4,
  },
  expandedMessageButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: colors.primary,
    paddingVertical: 10,
    borderRadius: 8,
    gap: 4,
  },
  expandedActionText: {
    fontSize: 14,
    fontWeight: '600',
    color: colors.textLight,
  },
  
  // New Simplified Dashboard Styles
  quickStatsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 16,
  },
  quickStatItem: {
    alignItems: 'center',
    flex: 1,
  },
  quickStatLabel: {
    fontSize: 12,
    color: colors.textSecondary,
    fontWeight: '500',
    marginBottom: 4,
  },
  quickStatValue: {
    fontSize: 20,
    fontWeight: '700',
    color: colors.text,
    marginBottom: 2,
  },
  quickStatSubtext: {
    fontSize: 10,
    color: colors.textSecondary,
  },
  quickStatDivider: {
    width: 1,
    height: 40,
    backgroundColor: colors.border,
  },
  seeMoreButton: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
    paddingVertical: 8,
    paddingHorizontal: 12,
    backgroundColor: colors.backgroundTertiary,
    borderRadius: 16,
  },
  seeMoreText: {
    fontSize: 12,
    color: colors.primary,
    fontWeight: '600',
  },
  
  // New Requests Styles
  newRequestsHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  newRequestsTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.text,
  },
  requestCountBadge: {
    backgroundColor: colors.primary,
    borderRadius: 12,
    paddingHorizontal: 8,
    paddingVertical: 4,
    minWidth: 24,
    alignItems: 'center',
  },
  requestCountText: {
    fontSize: 12,
    fontWeight: '700',
    color: colors.textLight,
  },
  simpleRequestsView: {
    alignItems: 'center',
    paddingVertical: 16,
  },
  simpleRequestsText: {
    fontSize: 14,
    color: colors.textSecondary,
    marginBottom: 12,
    textAlign: 'center',
  },
  viewRequestsButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.primary,
    paddingVertical: 10,
    paddingHorizontal: 16,
    borderRadius: 20,
    gap: 6,
  },
  viewRequestsText: {
    fontSize: 14,
    fontWeight: '600',
    color: colors.textLight,
  },
  toggleViewButton: {
    alignItems: 'center',
    paddingVertical: 8,
  },
  toggleViewText: {
    fontSize: 12,
    color: colors.primary,
    fontWeight: '500',
  },
  
  // Collapsible sections
  collapsibleHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 8,
  },
  collapsibleTitle: {
    fontSize: 14,
    fontWeight: '500',
    color: colors.textSecondary,
  },
  rejectedRequestsList: {
    marginTop: 8,
    gap: 6,
  },
  rejectedRequestItem: {
    backgroundColor: colors.backgroundTertiary,
    padding: 8,
    borderRadius: 6,
    borderLeftWidth: 3,
    borderLeftColor: colors.error,
  },
  rejectedRequestText: {
    fontSize: 12,
    color: colors.textSecondary,
  },
  
  // Driver Status Simplified
  driverStatusSimple: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  statusIndicatorLarge: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  statusIconLarge: {
    fontSize: 20,
  },
  statusTextLarge: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.text,
  },
  onlineIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
  },
  onlineDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: colors.success,
  },
  onlineText: {
    fontSize: 12,
    color: colors.success,
    fontWeight: '500',
  },
  
  // Simplified ride cards
  simpleRideCard: {
    backgroundColor: colors.backgroundSecondary,
  },
  transferRequestLeft: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  transferRequestRight: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  
  // Simplified booking info
  bookingMainInfoSimple: {
    backgroundColor: colors.backgroundTertiary,
    borderRadius: 8,
    padding: 12,
    marginBottom: 12,
  },
  passengerRowSimple: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 8,
  },
  passengerInfoSimple: {
    flex: 2,
  },
  passengerNameSimple: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.text,
    marginBottom: 2,
  },
  flightInfoSimple: {
    fontSize: 14,
    color: colors.primary,
    fontWeight: '500',
  },
  fareInfoSimple: {
    alignItems: 'flex-end',
    flex: 1,
  },
  fareAmountSimple: {
    fontSize: 18,
    fontWeight: '700',
    color: colors.success,
    marginBottom: 2,
  },
  tripTimeSimple: {
    fontSize: 11,
    color: colors.textSecondary,
  },
  locationInfoSimple: {
    gap: 4,
  },
  locationRowSimple: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
  },
  locationTextSimple: {
    fontSize: 12,
    color: colors.textSecondary,
    flex: 1,
  },
  
  // Simplified actions
  rejectedStatusSimple: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.errorLight,
    padding: 8,
    borderRadius: 6,
    gap: 6,
    marginTop: 8,
  },
  rejectedMessageSimple: {
    fontSize: 12,
    color: colors.error,
    fontWeight: '500',
  },
  rideActionsSimple: {
    flexDirection: 'row',
    gap: 10,
    marginTop: 12,
  },
  actionButtonSimple: {
    flex: 1,
    paddingVertical: 12,
  },
  declineButton: {
    backgroundColor: colors.backgroundTertiary,
    borderWidth: 1,
    borderColor: colors.textSecondary,
  },
  acceptButton: {
    backgroundColor: colors.success,
  },
  
  // Driver Status Simple Styles
  statusIndicatorsGrid: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    alignItems: 'center',
    backgroundColor: colors.backgroundTertiary,
    borderRadius: 12,
    paddingVertical: 20,
  },
  statusIndicatorItem: {
    alignItems: 'center',
    flex: 1,
  },
  statusSubtext: {
    fontSize: 11,
    color: colors.textSecondary,
    marginTop: 2,
    textAlign: 'center',
  },

  // View mode toggle
  
  // Communication Actions Styles
  communicationActions: {
    flexDirection: 'row',
    gap: 8,
    marginTop: 12,
    marginBottom: 8,
  },
  callButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: colors.success,
    paddingVertical: 10,
    borderRadius: 8,
    gap: 6,
  },
  messageButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: colors.primary,
    paddingVertical: 10,
    borderRadius: 8,
    gap: 6,
  },
  actionText: {
    fontSize: 14,
    fontWeight: '600',
    color: colors.textLight,
  },
  
  // Queued Booking Styles
  queuedBookingHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  queuedBadge: {
    backgroundColor: colors.warning,
    borderRadius: 12,
    paddingHorizontal: 8,
    paddingVertical: 4,
  },
  queuedBadgeText: {
    fontSize: 12,
    fontWeight: '700',
    color: colors.textLight,
  },
  
  // Service Message Styles
  serviceMessageContainer: {
    alignItems: 'center',
    paddingVertical: 20,
  },
  serviceMessageIcon: {
    fontSize: 32,
    marginBottom: 12,
  },
  serviceMessageTitle: {
    fontSize: 18,
    fontWeight: '700',
    color: colors.text,
    marginBottom: 8,
    textAlign: 'center',
  },
  serviceMessageText: {
    fontSize: 14,
    color: colors.textSecondary,
    textAlign: 'center',
    lineHeight: 20,
    marginBottom: 8,
  },
  serviceMessageNote: {
    fontSize: 12,
    color: colors.primary,
    fontWeight: '600',
    textAlign: 'center',
    marginTop: 4,
  },
  
  // Queue Position Styles
  queuedBookingSpacing: {
    marginTop: 12,
    borderTopWidth: 1,
    borderTopColor: colors.border,
    paddingTop: 12,
  },
  queuedBookingPosition: {
    position: 'absolute',
    top: -8,
    left: -8,
    backgroundColor: colors.primary,
    borderRadius: 12,
    width: 24,
    height: 24,
    alignItems: 'center',
    justifyContent: 'center',
    zIndex: 1,
  },
  queuePositionText: {
    fontSize: 10,
    fontWeight: '700',
    color: colors.textLight,
  },
  queueNote: {
    fontSize: 11,
    color: colors.textSecondary,
    textAlign: 'center',
    marginTop: 8,
    fontStyle: 'italic',
  },
  
  // Unified System Info Styles
  
  // Current Transfer Enhanced Styles
  currentTransferHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  navigateToCustomerButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.backgroundTertiary,
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    gap: 4,
  },
  navigateToCustomerText: {
    fontSize: 12,
    fontWeight: '600',
    color: colors.primary,
  },
  currentTransferMainInfo: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 16,
  },
  passengerSection: {
    flex: 2,
  },
  flightStatusBadge: {
    backgroundColor: colors.successLight,
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 6,
    marginLeft: 8,
  },
  flightStatusText: {
    fontSize: 10,
    fontWeight: '600',
    color: colors.success,
  },
  fareSection: {
    alignItems: 'flex-end',
    flex: 1,
  },
  tripMetrics: {
    fontSize: 12,
    color: colors.textSecondary,
    marginTop: 2,
  },
  routeInformation: {
    backgroundColor: colors.backgroundTertiary,
    borderRadius: 8,
    padding: 12,
    marginBottom: 16,
  },
  routePoint: {
    flexDirection: 'row',
    alignItems: 'center',
    marginVertical: 2,
  },
  routeLine: {
    width: 1,
    height: 16,
    backgroundColor: colors.border,
    marginLeft: 5.5,
    marginVertical: 2,
  },
  routeAddress: {
    fontSize: 13,
    color: colors.text,
    marginLeft: 8,
    flex: 1,
    lineHeight: 18,
  },
  currentTransferActions: {
    flexDirection: 'row',
    gap: 8,
    marginBottom: 16,
  },
  navigationButton: {
    flex: 2,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: colors.primary,
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    gap: 8,
  },
  navigationButtonText: {
    fontSize: 14,
    fontWeight: '600',
    color: colors.textLight,
  },
  callCustomerButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: colors.success,
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    gap: 6,
  },
  callCustomerButtonText: {
    fontSize: 14,
    fontWeight: '600',
    color: colors.textLight,
  },
  cancelButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: colors.error,
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    gap: 6,
  },
  cancelButtonText: {
    fontSize: 14,
    fontWeight: '600',
    color: colors.textLight,
  },
  
  // Test Navigation Styles
  testNavigationContainer: {
    flexDirection: 'column',
    gap: 16,
  },
  testNavigationInfo: {
    flex: 1,
  },
  testNavigationTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.text,
    marginBottom: 4,
  },
  testNavigationSubtitle: {
    fontSize: 14,
    color: colors.primary,
    fontWeight: '500',
    marginBottom: 6,
  },
  testNavigationDescription: {
    fontSize: 12,
    color: colors.textSecondary,
    lineHeight: 16,
  },
  testNavigationButtons: {
    flexDirection: 'row',
    gap: 8,
  },
  testNavigationButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: colors.primary,
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    gap: 8,
  },
  testNavigationButtonDisabled: {
    backgroundColor: colors.textSecondary,
    opacity: 0.7,
  },
  testNavigationButtonText: {
    fontSize: 14,
    fontWeight: '600',
    color: colors.textLight,
  },
  wazeNavigationButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#00D4AA',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    gap: 8,
  },
  wazeNavigationButtonText: {
    fontSize: 14,
    fontWeight: '600',
    color: colors.textLight,
  },
  
  // Navigation Modal Styles
  navigationModalContainer: {
    flex: 1,
    backgroundColor: colors.background,
  },
  navigationHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: colors.primary,
    paddingTop: 50,
    paddingBottom: 16,
    paddingHorizontal: 16,
  },
  backButton: {
    padding: 8,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
  },
  navigationTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.textLight,
  },
  stopButton: {
    padding: 8,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
  },
  navigationMapContainer: {
    flex: 2,
    backgroundColor: colors.backgroundTertiary,
  },
  navigationInstructions: {
    flex: 1,
    backgroundColor: colors.background,
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  routeInfo: {
    marginBottom: 16,
  },
  routeFromTo: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.text,
    marginBottom: 8,
  },
  routeStats: {
    flexDirection: 'row',
    gap: 16,
  },
  routeStat: {
    fontSize: 12,
    color: colors.textSecondary,
    backgroundColor: colors.backgroundTertiary,
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  currentStep: {
    backgroundColor: colors.primary,
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
  },
  stepHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    marginBottom: 8,
  },
  stepNumber: {
    fontSize: 14,
    fontWeight: '600',
    color: colors.textLight,
    flex: 1,
  },
  stepDistance: {
    fontSize: 12,
    color: colors.textLight,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 8,
  },
  stepInstruction: {
    fontSize: 16,
    color: colors.textLight,
    lineHeight: 22,
  },
  navigationControls: {
    marginBottom: 16,
  },
  startNavigationButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: colors.success,
    paddingVertical: 16,
    borderRadius: 12,
    gap: 8,
  },
  startNavigationText: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.textLight,
  },
  stopNavigationButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: colors.error,
    paddingVertical: 16,
    borderRadius: 12,
    gap: 8,
  },
  stopNavigationText: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.textLight,
  },
  stepsList: {
    flex: 1,
    backgroundColor: colors.backgroundTertiary,
    borderRadius: 12,
    padding: 12,
  },
  stepsListTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: colors.text,
    marginBottom: 12,
  },
  stepItem: {
    backgroundColor: colors.background,
    borderRadius: 8,
    padding: 12,
    marginBottom: 8,
    borderLeftWidth: 4,
    borderLeftColor: colors.border,
  },
  activeStepItem: {
    borderLeftColor: colors.primary,
    backgroundColor: colors.backgroundTertiary,
  },
  stepItemHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 4,
  },
  stepItemNumber: {
    fontSize: 12,
    fontWeight: '600',
    color: colors.primary,
    backgroundColor: colors.backgroundTertiary,
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 8,
    minWidth: 24,
    textAlign: 'center',
  },
  stepItemDistance: {
    fontSize: 10,
    color: colors.textSecondary,
  },
  stepItemInstruction: {
    fontSize: 12,
    color: colors.textSecondary,
    lineHeight: 16,
  },
  activeStepInstruction: {
    color: colors.text,
    fontWeight: '500',
  },
});

export default DashboardScreen;