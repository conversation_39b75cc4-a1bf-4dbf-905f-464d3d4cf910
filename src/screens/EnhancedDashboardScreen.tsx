// Enhanced Dashboard Screen - Example Implementation
import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, RefreshControl } from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';

// Import enhanced hooks instead of the old context
import { useAuth, useRides, useDriver, useEarnings, useUI } from '../context/hooks';
import { usePerformance } from '../context/hooks/usePerformance';
import { useOptimistic } from '../context/hooks/useOptimistic';

import Button from '../components/Button';
import Card from '../components/Card';
import BottomSpacer from '../components/BottomSpacer';

const EnhancedDashboardScreen: React.FC = () => {
  // Use specialized hooks for different concerns
  const auth = useAuth();
  const rides = useRides();
  const driver = useDriver();
  const earnings = useEarnings();
  const ui = useUI();
  const performance = usePerformance();
  const optimistic = useOptimistic();
  
  // Local state
  const [refreshing, setRefreshing] = useState(false);
  const [selectedRideFilter, setSelectedRideFilter] = useState<'all' | 'urgent' | 'high_value'>('all');

  // Performance tracking
  useEffect(() => {
    performance.measure.start('dashboard_render');
    return () => {
      performance.measure.end('dashboard_render');
    };
  }, [performance]);

  // Auto-refresh data
  const handleRefresh = async () => {
    setRefreshing(true);
    try {
      await Promise.all([
        rides.loadAvailableRides(),
        earnings.loadEarningsData(),
        driver.loadDriverProfile(),
      ]);
    } catch (error) {
      console.error('Failed to refresh data:', error);
    } finally {
      setRefreshing(false);
    }
  };

  // Handle going online/offline with optimistic updates
  const handleToggleOnlineStatus = async () => {
    if (!driver.canGoOnline() && !driver.isOnline) {
      return;
    }

    const updateId = optimistic.operations.setOnlineStatus(!driver.isOnline);
    
    try {
      await driver.setOnlineStatus(!driver.isOnline);
      optimistic.confirmUpdate(updateId);
    } catch (error) {
      // Rollback is handled automatically by the optimistic hook
      console.error('Failed to update online status:', error);
    }
  };

  // Handle ride acceptance with optimistic updates
  const handleAcceptRide = async (rideId: string) => {
    const updateId = await optimistic.operations.acceptRide(rideId, (acceptedRide) => {
      console.log('Ride accepted successfully:', acceptedRide.id);
    });

    try {
      const success = await rides.acceptRide(rideId);
      if (success && updateId) {
        optimistic.confirmUpdate(updateId);
      }
    } catch (error) {
      console.error('Failed to accept ride:', error);
    }
  };

  // Filter rides based on selection
  const getFilteredRides = () => {
    switch (selectedRideFilter) {
      case 'urgent':
        return rides.urgentRides;
      case 'high_value':
        return rides.highValueRides;
      default:
        return rides.availableRides;
    }
  };

  const filteredRides = getFilteredRides();

  return (
    <ScrollView 
      style={[styles.container, { backgroundColor: ui.themeConfig.colors.background }]}
      refreshControl={
        <RefreshControl
          refreshing={refreshing}
          onRefresh={handleRefresh}
          tintColor={ui.themeConfig.colors.primary}
        />
      }
    >
      {/* Driver Status Card */}
      <Card style={styles.statusCard}>
        <View style={styles.statusHeader}>
          <View style={styles.driverInfo}>
            <Text style={[styles.driverName, { color: ui.themeConfig.colors.text }]}>
              {auth.driver?.name}
            </Text>
            <Text style={[styles.statusText, { color: driver.getStatusColor() }]}>
              {driver.getStatusText()}
            </Text>
          </View>
          <TouchableOpacity
            style={[
              styles.onlineToggle,
              { backgroundColor: driver.isOnline ? '#4CAF50' : '#9E9E9E' }
            ]}
            onPress={handleToggleOnlineStatus}
            disabled={!driver.canGoOnline() && !driver.isOnline}
          >
            <MaterialIcons 
              name={driver.isOnline ? 'radio-button-checked' : 'radio-button-unchecked'} 
              size={24} 
              color="white" 
            />
          </TouchableOpacity>
        </View>
        
        {/* Performance Indicator (Development only) */}
        {__DEV__ && (
          <View style={styles.performanceIndicator}>
            <Text style={styles.performanceText}>
              Performance: {performance.getPerformanceGrade()} ({performance.performanceScore}/100)
            </Text>
            {optimistic.hasPendingUpdates() && (
              <Text style={styles.optimisticText}>
                ⏳ {optimistic.pendingCount} pending updates
              </Text>
            )}
          </View>
        )}
      </Card>


      {/* Current Ride */}
      {rides.currentRide && (
        <Card style={styles.currentRideCard}>
          <Text style={[styles.cardTitle, { color: ui.themeConfig.colors.text }]}>
            Current Ride
          </Text>
          <View style={styles.rideInfo}>
            <Text style={styles.passengerName}>
              {rides.currentRide.passengerName}
            </Text>
            <Text style={styles.rideDetails}>
              {rides.currentRide.pickupLocation.address} → {rides.currentRide.dropoffLocation.address}
            </Text>
            <Text style={styles.fare}>
              {earnings.formatEarnings(rides.currentRide.fare)}
            </Text>
          </View>
        </Card>
      )}

      {/* Available Rides */}
      {driver.isOnline && rides.shouldShowBookings() && (
        <Card style={styles.ridesCard}>
          <View style={styles.ridesHeader}>
            <Text style={[styles.cardTitle, { color: ui.themeConfig.colors.text }]}>
              Available Rides
            </Text>
            
            {/* Ride Filter Tabs */}
            <View style={styles.filterTabs}>
              {[
                { key: 'all', label: `All (${rides.availableRides.length})` },
                { key: 'urgent', label: `Urgent (${rides.urgentRides.length})` },
                { key: 'high_value', label: `High Value (${rides.highValueRides.length})` },
              ].map((filter) => (
                <TouchableOpacity
                  key={filter.key}
                  style={[
                    styles.filterTab,
                    selectedRideFilter === filter.key && styles.activeFilterTab
                  ]}
                  onPress={() => setSelectedRideFilter(filter.key as any)}
                >
                  <Text style={[
                    styles.filterTabText,
                    selectedRideFilter === filter.key && styles.activeFilterTabText
                  ]}>
                    {filter.label}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          </View>

          {rides.isLoading ? (
            <Text style={styles.loadingText}>Loading rides...</Text>
          ) : filteredRides.length === 0 ? (
            <Text style={styles.emptyText}>No rides available</Text>
          ) : (
            filteredRides.slice(0, 5).map((ride) => (
              <View key={ride.id} style={styles.rideCard}>
                <View style={styles.rideHeader}>
                  <Text style={styles.passengerName}>{ride.passengerName}</Text>
                  <View style={[
                    styles.priorityBadge,
                    { backgroundColor: ride.priority === 'high' ? '#FF5722' : '#2196F3' }
                  ]}>
                    <Text style={styles.priorityText}>
                      {ride.bookingStatus.toUpperCase()}
                    </Text>
                  </View>
                </View>
                
                <Text style={styles.rideLocation}>
                  📍 {ride.pickupLocation.address}
                </Text>
                <Text style={styles.rideLocation}>
                  🏁 {ride.dropoffLocation.address}
                </Text>
                
                <View style={styles.rideFooter}>
                  <Text style={styles.rideDistance}>
                    {earnings.formatDistance(ride.distance)}
                  </Text>
                  <Text style={styles.rideFare}>
                    {earnings.formatEarnings(ride.fare)}
                  </Text>
                  <Button
                    title="Accept"
                    onPress={() => handleAcceptRide(ride.id)}
                    disabled={!rides.canAcceptNewBooking}
                    style={styles.acceptButton}
                  />
                </View>
              </View>
            ))
          )}
        </Card>
      )}

      {/* Ride Analytics */}
      {rides.rideAnalytics && (
        <Card style={styles.analyticsCard}>
          <Text style={[styles.cardTitle, { color: ui.themeConfig.colors.text }]}>
            Ride Insights
          </Text>
          <View style={styles.analyticsGrid}>
            <View style={styles.analyticsItem}>
              <Text style={styles.analyticsValue}>
                {earnings.formatEarnings(rides.rideAnalytics.averageFare)}
              </Text>
              <Text style={styles.analyticsLabel}>Avg Fare</Text>
            </View>
            <View style={styles.analyticsItem}>
              <Text style={styles.analyticsValue}>
                {rides.rideAnalytics.averageDistance.toFixed(1)}km
              </Text>
              <Text style={styles.analyticsLabel}>Avg Distance</Text>
            </View>
            <View style={styles.analyticsItem}>
              <Text style={styles.analyticsValue}>
                {earnings.formatEarnings(rides.getEstimatedEarnings())}
              </Text>
              <Text style={styles.analyticsLabel}>Potential</Text>
            </View>
          </View>
        </Card>
      )}

      <BottomSpacer />
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
  },
  statusCard: {
    marginBottom: 16,
  },
  statusHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  driverInfo: {
    flex: 1,
  },
  driverName: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  statusText: {
    fontSize: 14,
    marginTop: 2,
  },
  onlineToggle: {
    width: 50,
    height: 50,
    borderRadius: 25,
    justifyContent: 'center',
    alignItems: 'center',
  },
  performanceIndicator: {
    marginTop: 8,
    padding: 8,
    backgroundColor: '#F0F0F0',
    borderRadius: 4,
  },
  performanceText: {
    fontSize: 12,
    color: '#666',
  },
  optimisticText: {
    fontSize: 12,
    color: '#FF9800',
    marginTop: 2,
  },
  cardTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 12,
  },
  currentRideCard: {
    marginBottom: 16,
    borderLeftWidth: 4,
    borderLeftColor: '#4CAF50',
  },
  rideInfo: {
    paddingLeft: 8,
  },
  passengerName: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  rideDetails: {
    fontSize: 14,
    color: '#666',
    marginBottom: 4,
  },
  fare: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#4CAF50',
  },
  ridesCard: {
    marginBottom: 16,
  },
  ridesHeader: {
    marginBottom: 12,
  },
  filterTabs: {
    flexDirection: 'row',
    marginTop: 8,
  },
  filterTab: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    backgroundColor: '#F0F0F0',
    borderRadius: 16,
    marginRight: 8,
  },
  activeFilterTab: {
    backgroundColor: '#007AFF',
  },
  filterTabText: {
    fontSize: 12,
    color: '#666',
  },
  activeFilterTabText: {
    color: '#FFF',
  },
  loadingText: {
    textAlign: 'center',
    color: '#666',
    padding: 20,
  },
  emptyText: {
    textAlign: 'center',
    color: '#666',
    padding: 20,
    fontStyle: 'italic',
  },
  rideCard: {
    backgroundColor: '#F9F9F9',
    padding: 12,
    borderRadius: 8,
    marginBottom: 8,
  },
  rideHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  priorityBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  priorityText: {
    color: '#FFF',
    fontSize: 10,
    fontWeight: 'bold',
  },
  rideLocation: {
    fontSize: 12,
    color: '#666',
    marginBottom: 2,
  },
  rideFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 8,
  },
  rideDistance: {
    fontSize: 12,
    color: '#666',
  },
  rideFare: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#4CAF50',
  },
  acceptButton: {
    paddingHorizontal: 16,
    paddingVertical: 6,
  },
  analyticsCard: {
    marginBottom: 16,
  },
  analyticsGrid: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  analyticsItem: {
    alignItems: 'center',
  },
  analyticsValue: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#2196F3',
  },
  analyticsLabel: {
    fontSize: 12,
    color: '#666',
    marginTop: 4,
  },
});

export default EnhancedDashboardScreen;