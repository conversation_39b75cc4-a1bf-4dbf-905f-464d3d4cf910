import React from 'react';
import { View, Text, StyleSheet, ScrollView, FlatList } from 'react-native';
import BottomSpacer from '../components/BottomSpacer';
import { useApp } from '../context/ApiIntegratedGlobalStateContext';
import Card from '../components/Card';
import { Trip } from '../types';
import { colors, shadows } from '../theme/colors';
const TripHistoryScreen: React.FC = () => {
  const { state: appState } = useApp();

  const formatDate = (date: Date) => {
    return new Date(date).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const getWeeklyEarnings = () => {
    const weekAgo = new Date();
    weekAgo.setDate(weekAgo.getDate() - 7);
    
    return appState.trips
      .filter(trip => new Date(trip.completedAt) >= weekAgo)
      .reduce((sum, trip) => sum + trip.earnings, 0);
  };

  const renderTripItem = ({ item }: { item: Trip }) => (
    <Card style={styles.tripCard}>
      <View style={styles.tripHeader}>
        <Text style={styles.tripDate}>{formatDate(item.completedAt)}</Text>
        <Text style={styles.tripEarnings}>+£{item.earnings.toFixed(2)}</Text>
      </View>
      <View style={styles.tripDetails}>
        <Text style={styles.tripDistance}>{item.distance}km</Text>
        <Text style={styles.tripDuration}>{item.duration}min</Text>
      </View>
    </Card>
  );

  return (
    <View style={styles.container}>
      <ScrollView contentContainerStyle={styles.scrollContainer}>
        <Card>
          <Text style={styles.sectionTitle}>Earnings Summary</Text>
          <View style={styles.earningsRow}>
            <View style={styles.earningItem}>
              <Text style={styles.earningAmount}>£{appState.totalEarnings.toFixed(2)}</Text>
              <Text style={styles.earningLabel}>Total Earnings</Text>
            </View>
            <View style={styles.earningItem}>
              <Text style={styles.earningAmount}>£{getWeeklyEarnings().toFixed(2)}</Text>
              <Text style={styles.earningLabel}>This Week</Text>
            </View>
          </View>
          <View style={styles.statsRow}>
            <View style={styles.statItem}>
              <Text style={styles.statValue}>{appState.trips.length}</Text>
              <Text style={styles.statLabel}>Total Trips</Text>
            </View>
            <View style={styles.statItem}>
              <Text style={styles.statValue}>
                {appState.trips.length > 0 
                  ? `£${(appState.totalEarnings / appState.trips.length).toFixed(2)}`
                  : '£0.00'
                }
              </Text>
              <Text style={styles.statLabel}>Avg per Trip</Text>
            </View>
          </View>
        </Card>

        <Text style={styles.sectionTitle}>Recent Trips</Text>
        
        {appState.trips.length === 0 ? (
          <Card>
            <Text style={styles.noTripsText}>No trips completed yet</Text>
          </Card>
        ) : (
          <FlatList
            data={appState.trips}
            renderItem={renderTripItem}
            keyExtractor={(item) => item.id}
            scrollEnabled={false}
            showsVerticalScrollIndicator={false}
          />
        )}
        
        {/* Add bottom spacer */}
        <BottomSpacer />
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.backgroundTertiary,
  },
  scrollContainer: {
    paddingHorizontal: 16,
    paddingVertical: 16,
    paddingBottom: 120,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: colors.text,
    marginBottom: 16,
    marginTop: 8,
  },
  earningsRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 24,
  },
  earningItem: {
    alignItems: 'center',
    flex: 1,
  },
  earningAmount: {
    fontSize: 24,
    fontWeight: 'bold',
    color: colors.success,
    marginBottom: 4,
  },
  earningLabel: {
    fontSize: 14,
    color: colors.textSecondary,
  },
  statsRow: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    borderTopWidth: 1,
    borderTopColor: colors.border,
    paddingTop: 16,
  },
  statItem: {
    alignItems: 'center',
  },
  statValue: {
    fontSize: 18,
    fontWeight: 'bold',
    color: colors.text,
  },
  statLabel: {
    fontSize: 14,
    color: colors.textSecondary,
    marginTop: 4,
  },
  tripCard: {
    marginBottom: 12,
  },
  tripHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  tripDate: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.text,
  },
  tripEarnings: {
    fontSize: 16,
    fontWeight: 'bold',
    color: colors.success,
  },
  tripDetails: {
    flexDirection: 'row',
    gap: 16,
  },
  tripDistance: {
    fontSize: 14,
    color: colors.textSecondary,
  },
  tripDuration: {
    fontSize: 14,
    color: colors.textSecondary,
  },
  noTripsText: {
    fontSize: 16,
    color: colors.textSecondary,
    textAlign: 'center',
    fontStyle: 'italic',
  },
});

export default TripHistoryScreen;