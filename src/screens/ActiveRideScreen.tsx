import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, Alert, Linking, Platform } from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { useApp } from '../context/ApiIntegratedGlobalStateContext';
import Button from '../components/Button';
import Card from '../components/Card';
import NavigationCard from '../components/NavigationCard';
import { colors, shadows } from '../theme/colors';

const ActiveRideScreen: React.FC = () => {
  const { 
    state: appState, 
    setCurrentRide, 
    addTrip, 
    setRideStatus: setAppRideStatus, 
    setQueueStatus, 
    startNextQueuedRide 
  } = useApp();
  const [rideStatus, setRideStatus] = useState<'heading_to_pickup' | 'arrived_at_pickup' | 'passenger_onboard' | 'arrived_at_destination'>('heading_to_pickup');
  const [timer, setTimer] = useState(0);

  useEffect(() => {
    const interval = setInterval(() => {
      setTimer(prev => prev + 1);
    }, 1000);

    return () => clearInterval(interval);
  }, []);

  // Sync local ride status with app state on mount
  useEffect(() => {
    if (appState.rideStatus) {
      setRideStatus(appState.rideStatus);
    } else if (appState.currentRide) {
      // Default to heading_to_pickup if no status set
      setRideStatus('heading_to_pickup');
      setAppRideStatus('heading_to_pickup');
      setQueueStatus('limited_queue');
    }
  }, [appState.currentRide, appState.rideStatus, setAppRideStatus, setQueueStatus]);

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  const handleCallPassenger = () => {
    if (appState.currentRide?.passengerPhone) {
      Linking.openURL(`tel:${appState.currentRide.passengerPhone}`);
    }
  };

  const getCurrentDestination = () => {
    if (!appState.currentRide) return null;
    
    return rideStatus === 'heading_to_pickup' || rideStatus === 'arrived_at_pickup' 
      ? appState.currentRide.pickupLocation 
      : appState.currentRide.dropoffLocation;
  };

  const getCurrentDestinationLabel = () => {
    return rideStatus === 'heading_to_pickup' || rideStatus === 'arrived_at_pickup'
      ? 'Pickup Location'
      : 'Drop-off Location';
  };

  const getEstimatedTime = () => {
    // Calculate estimated time based on distance and current status
    if (!appState.currentRide) return '5-7 min';
    
    const baseTime = rideStatus === 'heading_to_pickup' || rideStatus === 'arrived_at_pickup'
      ? Math.max(3, Math.floor(appState.currentRide.distance * 0.5)) // Pickup estimation
      : Math.max(5, Math.floor(appState.currentRide.distance * 0.8)); // Drop-off estimation
    
    return `${baseTime}-${baseTime + 2} min`;
  };

  const handleNavigationStart = () => {
    // Optional callback when navigation starts
    console.log('Navigation started to:', getCurrentDestinationLabel());
  };

  const handleNextStep = () => {
    switch (rideStatus) {
      case 'heading_to_pickup':
        setRideStatus('arrived_at_pickup');
        setAppRideStatus('arrived_at_pickup');
        setQueueStatus('limited_queue'); // Can still queue next booking
        break;
      case 'arrived_at_pickup':
        setRideStatus('passenger_onboard');
        setAppRideStatus('passenger_onboard');
        setQueueStatus('service_mode'); // Hide all bookings during service
        break;
      case 'passenger_onboard':
        setRideStatus('arrived_at_destination');
        setAppRideStatus('arrived_at_destination');
        setQueueStatus('service_mode'); // Still in service mode
        break;
      case 'arrived_at_destination':
        handleCompleteRide();
        break;
    }
  };

  const handleCompleteRide = () => {
    if (appState.currentRide) {
      const trip = {
        id: Date.now().toString(),
        rideId: appState.currentRide.id,
        earnings: appState.currentRide.fare,
        distance: appState.currentRide.distance,
        duration: Math.floor(timer / 60),
        completedAt: new Date(),
      };

      addTrip(trip);
      
      // Check if there are queued bookings
      if (appState.queuedBookings.length > 0) {
        const nextRide = startNextQueuedRide();
        if (nextRide) {
          setRideStatus('heading_to_pickup');
          Alert.alert(
            'Trip Completed', 
            `You earned £${appState.currentRide.fare.toFixed(2)}\n\nStarting next queued trip: ${nextRide.passengerName}\n\nRemaining in queue: ${appState.queuedBookings.length - 1}`,
            [{ text: 'OK' }]
          );
        } else {
          // Fallback if startNextQueuedRide fails
          setCurrentRide(null);
          setAppRideStatus(null);
          setQueueStatus('available');
          Alert.alert('Trip Completed', `You earned £${appState.currentRide.fare.toFixed(2)}`);
        }
      } else {
        // No queued bookings - back to available
        setCurrentRide(null);
        setAppRideStatus(null);
        setQueueStatus('available');
        Alert.alert('Trip Completed', `You earned £${appState.currentRide.fare.toFixed(2)}`);
      }
    }
  };

  const getStatusText = () => {
    switch (rideStatus) {
      case 'heading_to_pickup':
        return 'Heading to pickup location';
      case 'arrived_at_pickup':
        return 'Arrived at pickup location';
      case 'passenger_onboard':
        return 'Passenger onboard - En route to destination';
      case 'arrived_at_destination':
        return 'Arrived at destination';
      default:
        return '';
    }
  };

  const getButtonText = () => {
    switch (rideStatus) {
      case 'heading_to_pickup':
        return 'Arrived at Pickup';
      case 'arrived_at_pickup':
        return 'Start Trip';
      case 'passenger_onboard':
        return 'Complete Trip';
      case 'arrived_at_destination':
        return 'End Trip';
      default:
        return '';
    }
  };

  if (!appState.currentRide) {
    return (
      <View style={styles.container}>
        <Text style={styles.noRideText}>No active ride</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.timer}>{formatTime(timer)}</Text>
        <Text style={styles.status}>{getStatusText()}</Text>
      </View>

      <Card>
        <Text style={styles.cardTitle}>Passenger Information</Text>
        <Text style={styles.passengerName}>{appState.currentRide.passengerName}</Text>
        <Text style={styles.passengerPhone}>{appState.currentRide.passengerPhone}</Text>
        
        <View style={styles.actionButtonsRow}>
          <Button
            title="Call Passenger"
            onPress={handleCallPassenger}
            variant="secondary"
            style={styles.callButton}
          />
        </View>
      </Card>

      {getCurrentDestination() && (
        <NavigationCard
          destination={getCurrentDestination()!}
          destinationLabel={getCurrentDestinationLabel()}
          estimatedTime={getEstimatedTime()}
          distance={`${appState.currentRide.distance} km`}
          origin={appState.driver?.currentLocation}
          onNavigationStart={handleNavigationStart}
        />
      )}

      <Card>
        <Text style={styles.cardTitle}>Trip Details</Text>
        <View style={styles.locationRow}>
          <Text style={styles.locationLabel}>From:</Text>
          <Text style={styles.locationText}>{appState.currentRide.pickupLocation.address}</Text>
        </View>
        <View style={styles.locationRow}>
          <Text style={styles.locationLabel}>To:</Text>
          <Text style={styles.locationText}>{appState.currentRide.dropoffLocation.address}</Text>
        </View>
        <View style={styles.tripInfo}>
          <Text style={styles.tripInfoText}>
            Distance: {appState.currentRide.distance}km
          </Text>
          <Text style={styles.tripInfoText}>
            Fare: £{appState.currentRide.fare.toFixed(2)}
          </Text>
        </View>
      </Card>

      {appState.queuedBookings.length > 0 && (
        <Card>
          <Text style={styles.cardTitle}>Queued Bookings ({appState.queuedBookings.length})</Text>
          {appState.queuedBookings.slice(0, 3).map((booking, index) => (
            <View key={booking.id} style={[styles.queuedBookingInfo, index > 0 && styles.queuedBookingSpacing]}>
              <View style={styles.queuedBookingHeader}>
                <Text style={styles.queuePositionBadge}>#{index + 1}</Text>
                <Text style={styles.queuedPassengerName}>{booking.passengerName}</Text>
              </View>
              <Text style={styles.queuedFlightInfo}>
                {booking.flightNumber} • {booking.airport}
              </Text>
              <Text style={styles.queuedFare}>£{booking.fare.toFixed(2)}</Text>
            </View>
          ))}
          {appState.queuedBookings.length > 3 && (
            <Text style={styles.queuedMore}>+{appState.queuedBookings.length - 3} more in queue</Text>
          )}
          <Text style={styles.queuedNote}>
            {appState.queuedBookings.length === 1 ? 
              'This booking will start automatically after completing current trip' :
              `These ${appState.queuedBookings.length} bookings will start in order after completing current trip`
            }
          </Text>
        </Card>
      )}

      <View style={styles.actions}>
        <Button
          title={getButtonText()}
          onPress={handleNextStep}
          variant="success"
          style={styles.actionButton}
        />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.backgroundTertiary,
    paddingHorizontal: 16,
  },
  header: {
    paddingVertical: 20,
    alignItems: 'center',
  },
  timer: {
    fontSize: 32,
    fontWeight: 'bold',
    color: colors.primary,
    marginBottom: 8,
  },
  status: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.text,
    textAlign: 'center',
  },
  cardTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 12,
    color: colors.text,
  },
  passengerName: {
    fontSize: 20,
    fontWeight: 'bold',
    color: colors.text,
    marginBottom: 4,
  },
  passengerPhone: {
    fontSize: 16,
    color: colors.textSecondary,
    marginBottom: 16,
  },
  actionButtonsRow: {
    flexDirection: 'row',
    gap: 8,
    marginTop: 12,
  },
  callButton: {
    flex: 1,
  },
  locationRow: {
    flexDirection: 'row',
    marginBottom: 8,
  },
  locationLabel: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.text,
    width: 50,
  },
  locationText: {
    fontSize: 16,
    color: colors.textSecondary,
    flex: 1,
  },
  tripInfo: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 16,
  },
  tripInfoText: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.text,
  },
  actions: {
    marginTop: 'auto',
    paddingBottom: 32,
  },
  actionButton: {
    marginHorizontal: 0,
  },
  noRideText: {
    fontSize: 18,
    color: colors.textSecondary,
    textAlign: 'center',
    marginTop: 100,
  },
  queuedBookingInfo: {
    backgroundColor: colors.backgroundTertiary,
    borderRadius: 8,
    padding: 12,
    marginBottom: 8,
  },
  queuedPassengerName: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.text,
    marginBottom: 4,
  },
  queuedFlightInfo: {
    fontSize: 14,
    color: colors.primary,
    fontWeight: '500',
    marginBottom: 4,
  },
  queuedFare: {
    fontSize: 16,
    fontWeight: '700',
    color: colors.success,
  },
  queuedNote: {
    fontSize: 12,
    color: colors.textSecondary,
    fontStyle: 'italic',
    textAlign: 'center',
  },
  queuedBookingSpacing: {
    marginTop: 12,
    borderTopWidth: 1,
    borderTopColor: colors.border,
    paddingTop: 12,
  },
  queuedBookingHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  queuePositionBadge: {
    backgroundColor: colors.primary,
    color: colors.textLight,
    fontSize: 10,
    fontWeight: '700',
    borderRadius: 8,
    paddingHorizontal: 6,
    paddingVertical: 2,
    marginRight: 8,
    minWidth: 20,
    textAlign: 'center',
  },
  queuedMore: {
    fontSize: 11,
    color: colors.textSecondary,
    textAlign: 'center',
    marginTop: 8,
    fontStyle: 'italic',
  },
});

export default ActiveRideScreen;