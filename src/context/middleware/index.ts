// State Management Middleware
import { StateAction, StateMiddleware, GlobalState } from '../slices/types';
import AsyncStorage from '@react-native-async-storage/async-storage';

// Logging middleware
export const loggingMiddleware: StateMiddleware = (action, state, next) => {
  if (__DEV__ && state.system.debug.enabled) {
    const timestamp = new Date().toISOString();
    console.group(`🔄 Action: ${action.type} @ ${timestamp}`);
    console.log('Previous State:', state);
    console.log('Action:', action);
    
    next(action);
    
    console.log('Next State:', state);
    console.groupEnd();
  } else {
    next(action);
  }
};

// Performance monitoring middleware
export const performanceMiddleware: StateMiddleware = (action, state, next) => {
  const start = performance.now();
  
  next(action);
  
  const end = performance.now();
  const duration = end - start;
  
  // Track slow actions in development
  if (__DEV__ && duration > 16) { // 16ms is one frame at 60fps
    console.warn(`⚠️ Slow action detected: ${action.type} took ${duration.toFixed(2)}ms`);
  }
  
  // Update performance metrics
  if (state.system.debug.enabled) {
    next({
      type: 'system/updatePerformance',
      payload: {
        renderTime: duration,
      },
    });
  }
};

// Error handling middleware
export const errorHandlingMiddleware: StateMiddleware = (action, state, next) => {
  try {
    next(action);
  } catch (error) {
    console.error(`❌ Error in action ${action.type}:`, error);
    
    // Set system error
    next({
      type: 'system/setError',
      payload: {
        section: 'network',
        error: error instanceof Error ? error.message : 'Unknown error occurred',
      },
    });
    
    // In development, re-throw the error
    if (__DEV__) {
      throw error;
    }
  }
};

// Persistence middleware for critical data
export const persistenceMiddleware: StateMiddleware = (action, state, next) => {
  next(action);
  
  // Define which actions should trigger persistence
  const persistActions = [
    'auth/login',
    'auth/logout',
    'auth/updateDriver',
    'ui/setTheme',
    'ui/updateNotificationSettings',
    'driver/setLocation',
  ];
  
  if (persistActions.some(actionType => action.type === actionType)) {
    // Persist critical data asynchronously
    persistCriticalData(state).catch(error => {
      console.warn('Failed to persist data:', error);
    });
  }
};

// Optimistic updates middleware
export const optimisticMiddleware: StateMiddleware = (action, state, next) => {
  const optimisticActions = [
    'rides/acceptBooking',
    'rides/rejectBooking',
    'earnings/addTrip',
    'driver/setOnlineStatus',
  ];
  
  if (optimisticActions.some(actionType => action.type.includes(actionType))) {
    // Store the current state for potential rollback
    const rollbackState = { ...state };
    
    // Apply the optimistic update
    next(action);
    
    // Set a timeout for rollback if needed
    setTimeout(() => {
      // In a real implementation, you'd check if the API call succeeded
      // and rollback if it failed
      if (state.system.errors.network) {
        console.warn('Rolling back optimistic update due to network error');
        // Rollback logic would go here
      }
    }, 5000);
  } else {
    next(action);
  }
};

// Network status middleware
export const networkMiddleware: StateMiddleware = (action, state, next) => {
  // Check network status before API-related actions
  const apiActions = [
    'auth/',
    'rides/',
    'driver/',
    'earnings/',
  ];
  
  const isApiAction = apiActions.some(prefix => action.type.startsWith(prefix));
  
  if (isApiAction && !state.system.connection.isOnline) {
    console.warn(`🌐 Cannot perform ${action.type} while offline`);
    
    // Add to pending actions for later sync
    next({
      type: 'system/addPendingAction',
      payload: {
        type: action.type,
        payload: action.payload,
      },
    });
    
    // Set offline error
    next({
      type: 'system/setError',
      payload: {
        section: 'network',
        error: 'No internet connection. Action will be retried when online.',
      },
    });
    
    return;
  }
  
  next(action);
};

// Validation middleware
export const validationMiddleware: StateMiddleware = (action, state, next) => {
  // Validate action payloads
  const validationRules: Record<string, (payload: any) => boolean> = {
    'auth/login': (payload) => 
      payload && payload.driver && payload.token && typeof payload.token === 'string',
    'rides/setCurrentRide': (payload) => 
      payload === null || (payload && payload.id && payload.status),
    'driver/setLocation': (payload) => 
      payload && typeof payload.latitude === 'number' && typeof payload.longitude === 'number',
    'earnings/addTrip': (payload) => 
      payload && payload.id && typeof payload.earnings === 'number',
  };
  
  const validator = validationRules[action.type];
  if (validator && !validator((action as any).payload)) {
    console.error(`❌ Invalid payload for action ${action.type}:`, (action as any).payload);
    
    if (__DEV__) {
      throw new Error(`Invalid payload for action ${action.type}`);
    }
    
    return;
  }
  
  next(action);
};

// Rate limiting middleware
export const rateLimitingMiddleware: StateMiddleware = (() => {
  const actionCounts: Record<string, number> = {};
  const resetInterval = 1000; // 1 second
  
  // Reset counts periodically
  setInterval(() => {
    Object.keys(actionCounts).forEach(key => {
      actionCounts[key] = 0;
    });
  }, resetInterval);
  
  return (action, state, next) => {
    const rateLimits: Record<string, number> = {
      'driver/setLocation': 10, // Max 10 location updates per second
      'ui/setMapRegion': 5, // Max 5 map updates per second
      'system/updatePerformance': 20, // Max 20 performance updates per second
    };
    
    const limit = rateLimits[action.type];
    if (limit) {
      actionCounts[action.type] = (actionCounts[action.type] || 0) + 1;
      
      if (actionCounts[action.type] > limit) {
        if (__DEV__) {
          console.warn(`⚡ Rate limit exceeded for ${action.type}. Limit: ${limit}/second`);
        }
        return;
      }
    }
    
    next(action);
  };
})();

// Data transformation middleware
export const transformationMiddleware: StateMiddleware = (action, state, next) => {
  let transformedAction = action;
  
  // Transform specific actions
  switch (action.type) {
    case 'earnings/addTrip':
      // Ensure date is a Date object
      const tripPayload = (action as any).payload;
      if (tripPayload.completedAt && typeof tripPayload.completedAt === 'string') {
        transformedAction = {
          ...action,
          payload: {
            ...tripPayload,
            completedAt: new Date(tripPayload.completedAt),
          },
        };
      }
      break;
      
    case 'driver/setLocation':
      // Round coordinates to reasonable precision
      const locationPayload = (action as any).payload;
      transformedAction = {
        ...action,
        payload: {
          ...locationPayload,
          latitude: Math.round(locationPayload.latitude * 1000000) / 1000000,
          longitude: Math.round(locationPayload.longitude * 1000000) / 1000000,
        },
      };
      break;
  }
  
  next(transformedAction);
};

// Combine all middleware
export const createMiddleware = (): StateMiddleware[] => [
  validationMiddleware,
  rateLimitingMiddleware,
  transformationMiddleware,
  errorHandlingMiddleware,
  networkMiddleware,
  optimisticMiddleware,
  persistenceMiddleware,
  performanceMiddleware,
  loggingMiddleware,
];

// Helper function to persist critical data
async function persistCriticalData(state: GlobalState): Promise<void> {
  try {
    const criticalData = {
      auth: {
        isAuthenticated: state.auth.isAuthenticated,
        driver: state.auth.driver,
        token: state.auth.token,
        lastLoginTime: state.auth.lastLoginTime,
      },
      ui: {
        theme: state.ui.theme,
        notifications: state.ui.notifications,
        mapRegion: state.ui.mapRegion,
      },
      driver: {
        location: state.driver.location,
        isOnline: state.driver.isOnline,
      },
      system: {
        debug: state.system.debug,
      },
    };
    
    await AsyncStorage.setItem('criticalAppData', JSON.stringify(criticalData));
  } catch (error) {
    console.error('Failed to persist critical data:', error);
  }
}

// Helper function to restore critical data
export async function restoreCriticalData(): Promise<Partial<GlobalState> | null> {
  try {
    const data = await AsyncStorage.getItem('criticalAppData');
    if (data) {
      const parsed = JSON.parse(data);
      
      // Transform date strings back to Date objects
      if (parsed.auth?.lastLoginTime) {
        parsed.auth.lastLoginTime = new Date(parsed.auth.lastLoginTime);
      }
      
      return parsed;
    }
  } catch (error) {
    console.error('Failed to restore critical data:', error);
  }
  
  return null;
}

// Middleware composer
export function applyMiddleware(
  middlewares: StateMiddleware[],
  dispatch: (action: StateAction) => void,
  getState: () => GlobalState
) {
  return (action: StateAction) => {
    let index = 0;
    
    function next(currentAction: StateAction) {
      if (index < middlewares.length) {
        const middleware = middlewares[index++];
        middleware(currentAction, getState(), next);
      } else {
        dispatch(currentAction);
      }
    }
    
    next(action);
  };
}