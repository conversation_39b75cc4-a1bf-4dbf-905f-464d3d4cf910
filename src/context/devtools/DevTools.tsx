// Development Tools Component
import React, { useState, useMemo } from 'react';
import { View, Text, ScrollView, TouchableOpacity, StyleSheet, Modal, TextInput } from 'react-native';
import { useEnhancedState } from '../EnhancedStateContext';
import { useCache } from '../hooks/useCache';
import { usePerformance } from '../hooks/usePerformance';
import { useOptimistic } from '../hooks/useOptimistic';
import { useRealtime } from '../hooks/useRealtime';

interface DevToolsProps {
  visible: boolean;
  onClose: () => void;
}

const DevTools: React.FC<DevToolsProps> = ({ visible, onClose }) => {
  const { state, getDebugInfo, exportState, importState } = useEnhancedState();
  const cache = useCache();
  const performance = usePerformance();
  const optimistic = useOptimistic();
  const realtime = useRealtime();
  
  const [activeTab, setActiveTab] = useState<'state' | 'cache' | 'performance' | 'optimistic' | 'realtime'>('state');
  const [searchQuery, setSearchQuery] = useState('');
  const [importText, setImportText] = useState('');
  
  // Filter state based on search
  const filteredState = useMemo(() => {
    if (!searchQuery) return state;
    
    const filter = (obj: any, path = ''): any => {
      if (typeof obj !== 'object' || obj === null) return obj;
      
      const filtered: any = {};
      Object.keys(obj).forEach(key => {
        const currentPath = path ? `${path}.${key}` : key;
        const value = obj[key];
        
        if (currentPath.toLowerCase().includes(searchQuery.toLowerCase()) ||
            (typeof value === 'string' && value.toLowerCase().includes(searchQuery.toLowerCase()))) {
          filtered[key] = value;
        } else if (typeof value === 'object' && value !== null) {
          const nestedResult = filter(value, currentPath);
          if (Object.keys(nestedResult).length > 0) {
            filtered[key] = nestedResult;
          }
        }
      });
      return filtered;
    };
    
    return filter(state);
  }, [state, searchQuery]);
  
  const tabs = [
    { id: 'state', title: 'State', icon: '📊' },
    { id: 'cache', title: 'Cache', icon: '💾' },
    { id: 'performance', title: 'Performance', icon: '⚡' },
    { id: 'optimistic', title: 'Optimistic', icon: '🔄' },
    { id: 'realtime', title: 'Realtime', icon: '📡' },
  ];
  
  const handleExportState = () => {
    const stateJson = exportState();
    console.log('Exported State:', stateJson);
    // In a real app, you might copy to clipboard or share
  };
  
  const handleImportState = () => {
    if (importText.trim()) {
      try {
        importState(importText);
        setImportText('');
      } catch (error) {
        console.error('Failed to import state:', error);
      }
    }
  };
  
  const renderStateTab = () => (
    <ScrollView style={styles.tabContent}>
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>State Inspector</Text>
        <TextInput
          style={styles.searchInput}
          placeholder="Search state..."
          value={searchQuery}
          onChangeText={setSearchQuery}
        />
        <View style={styles.codeBlock}>
          <Text style={styles.codeText}>
            {JSON.stringify(filteredState, null, 2)}
          </Text>
        </View>
      </View>
      
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Actions</Text>
        <TouchableOpacity style={styles.button} onPress={handleExportState}>
          <Text style={styles.buttonText}>Export State</Text>
        </TouchableOpacity>
        <TextInput
          style={styles.textArea}
          placeholder="Paste state JSON to import..."
          value={importText}
          onChangeText={setImportText}
          multiline
        />
        <TouchableOpacity style={styles.button} onPress={handleImportState}>
          <Text style={styles.buttonText}>Import State</Text>
        </TouchableOpacity>
      </View>
    </ScrollView>
  );
  
  const renderCacheTab = () => (
    <ScrollView style={styles.tabContent}>
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Cache Statistics</Text>
        {Object.entries(cache.stats).map(([name, stats]: [string, any]) => (
          <View key={name} style={styles.statCard}>
            <Text style={styles.statTitle}>{name.toUpperCase()}</Text>
            <Text>Size: {stats.size} entries</Text>
            <Text>Hit Rate: {cache.formatHitRate(stats.hitRate)}</Text>
            <Text>Memory: {cache.formatMemoryUsage(stats.memoryUsage)}</Text>
            <TouchableOpacity 
              style={[styles.smallButton, { backgroundColor: cache.getCacheStatusColor(name) }]}
              onPress={() => cache.operations[name as keyof typeof cache.operations]?.clear?.()}
            >
              <Text style={styles.buttonText}>Clear</Text>
            </TouchableOpacity>
          </View>
        ))}
      </View>
      
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Cache Health</Text>
        <View style={[styles.healthCard, { backgroundColor: cache.isHealthy ? '#E8F5E8' : '#FFE8E8' }]}>
          <Text style={styles.healthStatus}>
            Status: {cache.isHealthy ? '✅ Healthy' : '⚠️ Issues Detected'}
          </Text>
          <Text>Memory Usage: {cache.formatMemoryUsage(cache.memoryUsage)}</Text>
          <Text>Hit Rate: {cache.formatHitRate(cache.hitRate)}</Text>
        </View>
        
        <TouchableOpacity style={styles.button} onPress={cache.clearAllCaches}>
          <Text style={styles.buttonText}>Clear All Caches</Text>
        </TouchableOpacity>
      </View>
    </ScrollView>
  );
  
  const renderPerformanceTab = () => (
    <ScrollView style={styles.tabContent}>
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Performance Score</Text>
        <View style={[styles.scoreCard, { backgroundColor: performance.getScoreColor() }]}>
          <Text style={styles.scoreText}>{performance.performanceScore}/100</Text>
          <Text style={styles.gradeText}>Grade: {performance.getPerformanceGrade()}</Text>
        </View>
      </View>
      
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Analytics</Text>
        <View style={styles.statCard}>
          <Text>Average Operation: {performance.formatDuration(performance.analytics.averageOperationTime)}</Text>
          <Text>Total Operations: {performance.analytics.totalOperations}</Text>
          <Text>Render Count: {performance.renderCount}</Text>
          {performance.analytics.slowestOperation && (
            <Text>Slowest: {performance.analytics.slowestOperation.name} ({performance.formatDuration(performance.analytics.slowestOperation.duration!)})</Text>
          )}
        </View>
      </View>
      
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Memory Usage</Text>
        {(() => {
          const memUsage = performance.memoryMonitor.getCurrentUsage();
          return memUsage ? (
            <View style={styles.statCard}>
              <Text>Used: {performance.memoryMonitor.formatBytes(memUsage.used)}</Text>
              <Text>Total: {performance.memoryMonitor.formatBytes(memUsage.total)}</Text>
              <Text>Percentage: {memUsage.percentage.toFixed(1)}%</Text>
            </View>
          ) : (
            <Text>Memory information not available</Text>
          );
        })()}
      </View>
      
      {performance.recommendations.length > 0 && (
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Recommendations</Text>
          {performance.recommendations.map((rec, index) => (
            <View key={index} style={styles.recommendationCard}>
              <Text style={styles.recommendationText}>💡 {rec}</Text>
            </View>
          ))}
        </View>
      )}
    </ScrollView>
  );
  
  const renderOptimisticTab = () => (
    <ScrollView style={styles.tabContent}>
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Optimistic Updates</Text>
        <View style={styles.statCard}>
          <Text>Success Rate: {optimistic.formatSuccessRate()}</Text>
          <Text>Failure Rate: {optimistic.formatFailureRate()}</Text>
          <Text>Pending: {optimistic.pendingCount}</Text>
          <Text>Health: {optimistic.isHealthy ? '✅ Healthy' : '⚠️ Issues'}</Text>
        </View>
      </View>
      
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Active Updates</Text>
        {optimistic.activeUpdates.length === 0 ? (
          <Text style={styles.emptyText}>No active optimistic updates</Text>
        ) : (
          optimistic.activeUpdates.map((update) => (
            <View key={update.id} style={styles.updateCard}>
              <Text style={styles.updateTitle}>{update.action.type}</Text>
              <Text>Status: {update.status}</Text>
              <Text>Duration: {Date.now() - update.timestamp}ms</Text>
              <Text>Retries: {update.retryCount}/{update.maxRetries}</Text>
            </View>
          ))
        )}
      </View>
      
      <TouchableOpacity style={styles.button} onPress={optimistic.cleanup.clearAll}>
        <Text style={styles.buttonText}>Clear All Updates</Text>
      </TouchableOpacity>
    </ScrollView>
  );
  
  const renderRealtimeTab = () => (
    <ScrollView style={styles.tabContent}>
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Connection Status</Text>
        <View style={[styles.connectionCard, { backgroundColor: realtime.statusIndicators.getStatusColor() }]}>
          <Text style={styles.connectionText}>
            {realtime.statusIndicators.getStatusText()}
          </Text>
          <Text>Quality: {realtime.statusIndicators.connectionQuality()}</Text>
          <Text>Last Check: {realtime.formatConnectionTime()}</Text>
          {realtime.connectionStatus.retryCount > 0 && (
            <Text>Retries: {realtime.connectionStatus.retryCount}</Text>
          )}
        </View>
      </View>
      
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Message Queue</Text>
        <View style={styles.statCard}>
          <Text>Queued Messages: {realtime.queueLength}</Text>
          <Text>Real-time Enabled: {realtime.isRealTimeEnabled ? '✅ Yes' : '❌ No'}</Text>
        </View>
      </View>
      
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Actions</Text>
        <TouchableOpacity 
          style={styles.button} 
          onPress={realtime.connectionManager.reconnect}
        >
          <Text style={styles.buttonText}>Reconnect</Text>
        </TouchableOpacity>
        <TouchableOpacity 
          style={styles.button} 
          onPress={realtime.queueManager.clearQueue}
        >
          <Text style={styles.buttonText}>Clear Queue</Text>
        </TouchableOpacity>
      </View>
    </ScrollView>
  );
  
  const renderTabContent = () => {
    switch (activeTab) {
      case 'state': return renderStateTab();
      case 'cache': return renderCacheTab();
      case 'performance': return renderPerformanceTab();
      case 'optimistic': return renderOptimisticTab();
      case 'realtime': return renderRealtimeTab();
      default: return null;
    }
  };
  
  if (!__DEV__ || !visible) {
    return null;
  }
  
  return (
    <Modal visible={visible} animationType="slide" presentationStyle="pageSheet">
      <View style={styles.container}>
        <View style={styles.header}>
          <Text style={styles.title}>🛠️ Dev Tools</Text>
          <TouchableOpacity style={styles.closeButton} onPress={onClose}>
            <Text style={styles.closeButtonText}>✕</Text>
          </TouchableOpacity>
        </View>
        
        <View style={styles.tabBar}>
          {tabs.map((tab) => (
            <TouchableOpacity
              key={tab.id}
              style={[
                styles.tab,
                activeTab === tab.id && styles.activeTab
              ]}
              onPress={() => setActiveTab(tab.id as any)}
            >
              <Text style={styles.tabIcon}>{tab.icon}</Text>
              <Text style={[
                styles.tabText,
                activeTab === tab.id && styles.activeTabText
              ]}>
                {tab.title}
              </Text>
            </TouchableOpacity>
          ))}
        </View>
        
        {renderTabContent()}
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
  },
  closeButton: {
    padding: 8,
  },
  closeButtonText: {
    fontSize: 18,
    color: '#666',
  },
  tabBar: {
    flexDirection: 'row',
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  tab: {
    flex: 1,
    padding: 12,
    alignItems: 'center',
  },
  activeTab: {
    borderBottomWidth: 2,
    borderBottomColor: '#007AFF',
  },
  tabIcon: {
    fontSize: 16,
    marginBottom: 4,
  },
  tabText: {
    fontSize: 12,
    color: '#666',
  },
  activeTabText: {
    color: '#007AFF',
    fontWeight: 'bold',
  },
  tabContent: {
    flex: 1,
    padding: 16,
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 12,
    color: '#333',
  },
  searchInput: {
    backgroundColor: '#fff',
    padding: 12,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#ddd',
    marginBottom: 12,
  },
  textArea: {
    backgroundColor: '#fff',
    padding: 12,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#ddd',
    minHeight: 100,
    marginVertical: 8,
  },
  codeBlock: {
    backgroundColor: '#1e1e1e',
    padding: 12,
    borderRadius: 8,
    maxHeight: 300,
  },
  codeText: {
    color: '#fff',
    fontFamily: 'monospace',
    fontSize: 12,
  },
  statCard: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  statTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 8,
    color: '#007AFF',
  },
  healthCard: {
    padding: 16,
    borderRadius: 8,
    marginBottom: 12,
  },
  healthStatus: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  scoreCard: {
    padding: 20,
    borderRadius: 12,
    alignItems: 'center',
    marginBottom: 16,
  },
  scoreText: {
    fontSize: 36,
    fontWeight: 'bold',
    color: '#fff',
  },
  gradeText: {
    fontSize: 18,
    color: '#fff',
    marginTop: 4,
  },
  connectionCard: {
    padding: 16,
    borderRadius: 8,
    alignItems: 'center',
    marginBottom: 12,
  },
  connectionText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#fff',
    marginBottom: 8,
  },
  updateCard: {
    backgroundColor: '#fff',
    padding: 12,
    borderRadius: 8,
    marginBottom: 8,
    borderLeftWidth: 4,
    borderLeftColor: '#FF9800',
  },
  updateTitle: {
    fontSize: 14,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  recommendationCard: {
    backgroundColor: '#FFF3CD',
    padding: 12,
    borderRadius: 8,
    marginBottom: 8,
    borderLeftWidth: 4,
    borderLeftColor: '#FFC107',
  },
  recommendationText: {
    color: '#856404',
  },
  button: {
    backgroundColor: '#007AFF',
    padding: 12,
    borderRadius: 8,
    alignItems: 'center',
    marginVertical: 4,
  },
  smallButton: {
    padding: 8,
    borderRadius: 6,
    alignItems: 'center',
    marginTop: 8,
    minWidth: 80,
  },
  buttonText: {
    color: '#fff',
    fontWeight: 'bold',
  },
  emptyText: {
    color: '#666',
    fontStyle: 'italic',
    textAlign: 'center',
    padding: 20,
  },
});

export default DevTools;