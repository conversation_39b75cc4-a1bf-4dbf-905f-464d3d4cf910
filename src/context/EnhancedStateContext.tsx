// Enhanced Global State Context with Advanced Features
import React, { createContext, useContext, useReducer, useCallback, useRef, useEffect, ReactNode } from 'react';
import { GlobalState, StateAction, initialGlobalState } from './slices';
import { authReducer } from './slices/authSlice';
import { rideReducer } from './slices/rideSlice';
import { driverReducer } from './slices/driverSlice';
import { earningsReducer } from './slices/earningsSlice';
import { uiReducer } from './slices/uiSlice';
import { systemReducer } from './slices/systemSlice';
import { createMiddleware, applyMiddleware, restoreCriticalData } from './middleware';
import { cacheManager, rideCacheManager, tripCacheManager, driverCacheManager, earningsCacheManager } from './cache/CacheManager';
import { serviceManager, authService, rideService, driverService, tripService, webSocketService, locationService } from '../services';
import { Driver, Ride, Trip, Location } from '../types';

// Enhanced Context Interface
interface EnhancedStateContextType {
  // State
  state: GlobalState;
  
  // Enhanced dispatch with middleware
  dispatch: (action: StateAction) => void;
  
  // Batch actions
  batchDispatch: (actions: StateAction[]) => void;
  
  // Optimistic updates
  optimisticDispatch: (action: StateAction, rollbackAction: StateAction, timeout?: number) => Promise<void>;
  
  // Auth methods
  login: (email: string, password: string) => Promise<void>;
  logout: () => Promise<void>;
  refreshAuth: () => Promise<void>;
  
  // Ride methods
  loadAvailableRides: () => Promise<void>;
  acceptRide: (rideId: string) => Promise<boolean>;
  rejectRide: (rideId: string) => Promise<void>;
  completeRide: (rideId: string) => Promise<void>;
  
  // Driver methods
  updateDriverLocation: (location: Location) => Promise<void>;
  setDriverOnlineStatus: (isOnline: boolean) => Promise<void>;
  loadDriverProfile: () => Promise<void>;
  updateDriverProfile: (updates: Partial<Driver>) => Promise<void>;
  
  // Earnings methods
  loadTripHistory: () => Promise<void>;
  loadEarningsData: () => Promise<void>;
  addTripOptimistically: (trip: Trip) => Promise<void>;
  
  // Cache methods
  invalidateCache: (pattern?: string) => Promise<void>;
  prefetchData: (keys: string[]) => Promise<void>;
  getCacheStats: () => any;
  
  // Utility methods
  refreshAllData: () => Promise<void>;
  subscribeToUpdates: (callback: (state: GlobalState) => void) => () => void;
  getStateSlice: <K extends keyof GlobalState>(slice: K) => GlobalState[K];
  
  // Debug methods (development only)
  getDebugInfo: () => any;
  timeTravel: (actionIndex: number) => void;
  exportState: () => string;
  importState: (stateJson: string) => void;
}

const EnhancedStateContext = createContext<EnhancedStateContextType | undefined>(undefined);

// Combined reducer with slice separation
const rootReducer = (state: GlobalState, action: StateAction): GlobalState => {
  const newState = {
    auth: authReducer(state.auth, action),
    rides: rideReducer(state.rides, action),
    driver: driverReducer(state.driver, action),
    earnings: earningsReducer(state.earnings, action),
    ui: uiReducer(state.ui, action),
    system: systemReducer(state.system, action),
  };

  return newState;
};

export const EnhancedStateProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [state, baseDispatch] = useReducer(rootReducer, initialGlobalState);
  const subscribersRef = useRef<Array<(state: GlobalState) => void>>([]);
  const middlewareRef = useRef<ReturnType<typeof applyMiddleware>>();
  const stateHistoryRef = useRef<GlobalState[]>([]);
  const initializationRef = useRef<Promise<void>>();

  // Enhanced dispatch with middleware
  const dispatch = useCallback((action: StateAction) => {
    if (!middlewareRef.current) {
      middlewareRef.current = applyMiddleware(
        createMiddleware(),
        baseDispatch,
        () => state
      );
    }
    
    middlewareRef.current(action);
    
    // Store in history for debugging
    if (__DEV__ && state.system.debug.enabled) {
      stateHistoryRef.current.push({ ...state });
      // Keep only last 50 states
      if (stateHistoryRef.current.length > 50) {
        stateHistoryRef.current = stateHistoryRef.current.slice(-50);
      }
    }
    
    // Notify subscribers
    subscribersRef.current.forEach(callback => {
      try {
        callback(state);
      } catch (error) {
        console.error('Error in state subscriber:', error);
      }
    });
  }, [state]);

  // Batch multiple actions
  const batchDispatch = useCallback((actions: StateAction[]) => {
    // Dispatch all actions synchronously to avoid multiple re-renders
    actions.forEach(action => dispatch(action));
  }, [dispatch]);

  // Optimistic updates with rollback capability
  const optimisticDispatch = useCallback(async (
    action: StateAction,
    rollbackAction: StateAction,
    timeout: number = 5000
  ): Promise<void> => {
    const currentState = { ...state };
    
    // Apply optimistic update
    dispatch(action);
    
    try {
      // Wait for confirmation or timeout
      await new Promise((resolve, reject) => {
        const timer = setTimeout(() => {
          reject(new Error('Optimistic update timeout'));
        }, timeout);
        
        // In a real app, you'd wait for API confirmation here
        // For now, we'll simulate success after a delay
        setTimeout(() => {
          clearTimeout(timer);
          resolve(true);
        }, 1000);
      });
    } catch (error) {
      // Rollback on failure
      console.warn('Rolling back optimistic update:', error);
      dispatch(rollbackAction);
    }
  }, [dispatch, state]);

  // Initialize app data and services
  const initializeApp = useCallback(async () => {
    if (initializationRef.current) {
      return initializationRef.current;
    }

    initializationRef.current = (async () => {
      try {
        dispatch({ type: 'system/setLoading', payload: { section: 'auth', loading: true } });

        // Restore critical data from storage
        const restoredData = await restoreCriticalData();
        if (restoredData) {
          // Apply restored data to state
          if (restoredData.auth?.isAuthenticated && restoredData.auth.driver) {
            dispatch({
              type: 'auth/login',
              payload: {
                driver: restoredData.auth.driver,
                token: restoredData.auth.token || 'restored-token',
              },
            });
          }
          
          if (restoredData.ui?.theme) {
            dispatch({ type: 'ui/setTheme', payload: restoredData.ui.theme });
          }
          
          if (restoredData.driver?.location) {
            dispatch({ type: 'driver/setLocation', payload: restoredData.driver.location });
          }
        }

        // Initialize services
        await serviceManager.initialize();

        // Check authentication status
        const authStatus = await authService.getAuthStatus();
        if (authStatus.isAuthenticated && authStatus.driver) {
          dispatch({
            type: 'auth/login',
            payload: {
              driver: authStatus.driver,
              token: 'service-token',
            },
          });

          // Load initial data
          await Promise.allSettled([
            loadTripHistory(),
            loadEarningsData(),
            loadDriverProfile(),
          ]);
        }

        // Initialize location services
        const hasLocationPermission = await locationService.requestPermissions();
        if (hasLocationPermission) {
          const currentLocation = await locationService.getCurrentLocation();
          if (currentLocation) {
            dispatch({
              type: 'driver/setLocation',
              payload: {
                latitude: currentLocation.coords.latitude,
                longitude: currentLocation.coords.longitude,
                address: `${currentLocation.coords.latitude}, ${currentLocation.coords.longitude}`,
              },
            });
          }
        }

        // Set up WebSocket if authenticated
        if (state.auth.isAuthenticated && state.auth.driver) {
          await webSocketService.connect(state.auth.driver.id);
          dispatch({
            type: 'system/setConnectionState',
            payload: { isWebSocketConnected: true },
          });
        }

        dispatch({
          type: 'system/setConnectionState',
          payload: { isApiConnected: true },
        });

      } catch (error) {
        console.error('Failed to initialize app:', error);
        dispatch({
          type: 'system/setError',
          payload: { section: 'network', error: 'Failed to initialize application' },
        });
      } finally {
        dispatch({ type: 'system/setLoading', payload: { section: 'auth', loading: false } });
      }
    })();

    return initializationRef.current;
  }, [state.auth.isAuthenticated, state.auth.driver]);

  // Auth methods
  const login = useCallback(async (email: string, password: string): Promise<void> => {
    dispatch({ type: 'auth/setLoading', payload: true });
    dispatch({ type: 'auth/setError', payload: null });

    try {
      const response = await authService.login({ email, password });
      
      dispatch({
        type: 'auth/login',
        payload: {
          driver: response.driver,
          token: response.token,
        },
      });

      // Connect WebSocket
      await webSocketService.connect(response.driver.id);
      dispatch({
        type: 'system/setConnectionState',
        payload: { isWebSocketConnected: true },
      });

      // Load initial data
      await Promise.allSettled([
        loadTripHistory(),
        loadEarningsData(),
        loadDriverProfile(),
      ]);

    } catch (error: any) {
      dispatch({ type: 'auth/setError', payload: error.message });
      throw error;
    } finally {
      dispatch({ type: 'auth/setLoading', payload: false });
    }
  }, [dispatch]);

  const logout = useCallback(async (): Promise<void> => {
    dispatch({ type: 'auth/setLoading', payload: true });
    
    try {
      await authService.logout();
      webSocketService.disconnect();
      await cacheManager.clear();
      
      dispatch({ type: 'auth/logout' });
    } catch (error: any) {
      console.error('Logout error:', error);
      // Continue with logout even if server request fails
      dispatch({ type: 'auth/logout' });
    } finally {
      dispatch({ type: 'auth/setLoading', payload: false });
    }
  }, [dispatch]);

  const refreshAuth = useCallback(async (): Promise<void> => {
    if (!state.auth.token) return;
    
    try {
      const refreshed = await authService.refreshToken(state.auth.token);
      dispatch({
        type: 'auth/refreshSession',
        payload: {
          token: refreshed.token,
          expiry: new Date(Date.now() + 24 * 60 * 60 * 1000),
        },
      });
    } catch (error) {
      console.warn('Failed to refresh token:', error);
      await logout();
    }
  }, [state.auth.token, dispatch, logout]);

  // Data loading methods with caching
  const loadAvailableRides = useCallback(async (): Promise<void> => {
    if (!state.auth.driver || !state.driver.location) return;

    const cacheKey = `rides:available:${state.auth.driver.id}:${state.driver.location.latitude}:${state.driver.location.longitude}`;

    try {
      dispatch({ type: 'rides/setLoading', payload: true });
      
      const cachedRides = await rideCacheManager.prefetch(
        cacheKey,
        async () => {
          const response = await rideService.getAvailableRides({
            driverId: state.auth.driver!.id,
            latitude: state.driver.location!.latitude,
            longitude: state.driver.location!.longitude,
            limit: 20,
          });
          return response.rides;
        },
        { strategy: 'stale-while-revalidate', ttl: 2 * 60 * 1000 }
      );

      dispatch({ type: 'rides/setAvailableRides', payload: cachedRides });
    } catch (error: any) {
      dispatch({ type: 'rides/setError', payload: error.message });
    } finally {
      dispatch({ type: 'rides/setLoading', payload: false });
    }
  }, [state.auth.driver, state.driver.location, dispatch]);

  const loadTripHistory = useCallback(async (): Promise<void> => {
    if (!state.auth.driver) return;

    const cacheKey = `trips:history:${state.auth.driver.id}`;

    try {
      dispatch({ type: 'earnings/setLoading', payload: true });
      
      const cachedTrips = await tripCacheManager.prefetch(
        cacheKey,
        async () => {
          const response = await tripService.getTripHistory({
            driverId: state.auth.driver!.id,
            limit: 50,
            sortBy: 'date',
            sortOrder: 'desc',
          });
          return response.trips.map(trip => ({
            id: trip.id,
            rideId: trip.rideId,
            earnings: trip.earnings,
            distance: trip.distance,
            duration: trip.duration,
            completedAt: new Date(trip.completedAt),
          }));
        },
        { strategy: 'cache-first', ttl: 15 * 60 * 1000 }
      );

      dispatch({ type: 'earnings/setTrips', payload: cachedTrips });
    } catch (error: any) {
      dispatch({ type: 'earnings/setError', payload: error.message });
    } finally {
      dispatch({ type: 'earnings/setLoading', payload: false });
    }
  }, [state.auth.driver, dispatch]);

  const loadEarningsData = useCallback(async (): Promise<void> => {
    if (!state.auth.driver) return;

    const cacheKey = `earnings:summary:${state.auth.driver.id}`;

    try {
      dispatch({ type: 'earnings/setLoading', payload: true });
      
      const cachedEarnings = await earningsCacheManager.prefetch(
        cacheKey,
        async () => {
          const [weeklyEarnings, todayStats] = await Promise.all([
            tripService.getEarnings({
              driverId: state.auth.driver!.id,
              period: 'week',
            }),
            tripService.getEarnings({
              driverId: state.auth.driver!.id,
              period: 'today',
            }),
          ]);

          return {
            week: weeklyEarnings.totalEarnings,
            today: todayStats.totalEarnings,
            todayTrips: todayStats.tripCount,
          };
        },
        { strategy: 'stale-while-revalidate', ttl: 10 * 60 * 1000 }
      );

      dispatch({
        type: 'earnings/updateEarnings',
        payload: {
          week: cachedEarnings.week,
          today: cachedEarnings.today,
        },
      });
      dispatch({ type: 'earnings/setTodayTrips', payload: cachedEarnings.todayTrips });

    } catch (error: any) {
      dispatch({ type: 'earnings/setError', payload: error.message });
    } finally {
      dispatch({ type: 'earnings/setLoading', payload: false });
    }
  }, [state.auth.driver, dispatch]);

  const loadDriverProfile = useCallback(async (): Promise<void> => {
    if (!state.auth.driver) return;

    const cacheKey = `driver:profile:${state.auth.driver.id}`;

    try {
      dispatch({ type: 'driver/setLoading', payload: true });
      
      const cachedProfile = await driverCacheManager.prefetch(
        cacheKey,
        async () => {
          return await driverService.getProfile(state.auth.driver!.id);
        },
        { strategy: 'cache-first', ttl: 30 * 60 * 1000 }
      );

      dispatch({ type: 'auth/updateDriver', payload: cachedProfile });
    } catch (error: any) {
      dispatch({ type: 'driver/setError', payload: error.message });
    } finally {
      dispatch({ type: 'driver/setLoading', payload: false });
    }
  }, [state.auth.driver, dispatch]);

  // Utility methods
  const refreshAllData = useCallback(async (): Promise<void> => {
    if (!state.auth.driver) return;

    await Promise.allSettled([
      loadTripHistory(),
      loadEarningsData(),
      loadDriverProfile(),
      loadAvailableRides(),
    ]);
  }, [state.auth.driver, loadTripHistory, loadEarningsData, loadDriverProfile, loadAvailableRides]);

  const subscribeToUpdates = useCallback((callback: (state: GlobalState) => void): (() => void) => {
    subscribersRef.current.push(callback);
    
    return () => {
      const index = subscribersRef.current.indexOf(callback);
      if (index > -1) {
        subscribersRef.current.splice(index, 1);
      }
    };
  }, []);

  const getStateSlice = useCallback(<K extends keyof GlobalState>(slice: K): GlobalState[K] => {
    return state[slice];
  }, [state]);

  // Cache methods
  const invalidateCache = useCallback(async (pattern?: string): Promise<void> => {
    if (pattern) {
      await Promise.all([
        cacheManager.invalidatePattern(pattern),
        rideCacheManager.invalidatePattern(pattern),
        tripCacheManager.invalidatePattern(pattern),
        driverCacheManager.invalidatePattern(pattern),
        earningsCacheManager.invalidatePattern(pattern),
      ]);
    } else {
      await Promise.all([
        cacheManager.clear(),
        rideCacheManager.clear(),
        tripCacheManager.clear(),
        driverCacheManager.clear(),
        earningsCacheManager.clear(),
      ]);
    }
  }, []);

  const getCacheStats = useCallback(() => ({
    general: cacheManager.getStats(),
    rides: rideCacheManager.getStats(),
    trips: tripCacheManager.getStats(),
    driver: driverCacheManager.getStats(),
    earnings: earningsCacheManager.getStats(),
  }), []);

  // Debug methods (development only)
  const getDebugInfo = useCallback(() => {
    if (!__DEV__) return null;
    
    return {
      stateHistory: stateHistoryRef.current,
      cacheStats: getCacheStats(),
      performance: state.system.performance,
      actionHistory: state.system.debug.actionHistory,
    };
  }, [state, getCacheStats]);

  const timeTravel = useCallback((actionIndex: number) => {
    if (!__DEV__ || !state.system.debug.enabled) return;
    
    const targetState = stateHistoryRef.current[actionIndex];
    if (targetState) {
      // This would require a more complex implementation
      console.log('Time travel to state:', targetState);
    }
  }, [state.system.debug.enabled]);

  const exportState = useCallback(() => {
    return JSON.stringify(state, null, 2);
  }, [state]);

  const importState = useCallback((stateJson: string) => {
    if (!__DEV__) return;
    
    try {
      const importedState = JSON.parse(stateJson) as GlobalState;
      // This would require careful validation and state restoration
      console.log('Import state:', importedState);
    } catch (error) {
      console.error('Failed to import state:', error);
    }
  }, []);

  // Initialize app on mount
  useEffect(() => {
    initializeApp();
  }, [initializeApp]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      webSocketService.disconnect();
    };
  }, []);

  const contextValue: EnhancedStateContextType = {
    state,
    dispatch,
    batchDispatch,
    optimisticDispatch,
    login,
    logout,
    refreshAuth,
    loadAvailableRides,
    acceptRide: async (rideId: string) => {
      // Implementation would go here
      return true;
    },
    rejectRide: async (rideId: string) => {
      // Implementation would go here
    },
    completeRide: async (rideId: string) => {
      // Implementation would go here
    },
    updateDriverLocation: async (location: Location) => {
      dispatch({ type: 'driver/setLocation', payload: location });
    },
    setDriverOnlineStatus: async (isOnline: boolean) => {
      dispatch({ type: 'driver/setOnlineStatus', payload: isOnline });
    },
    loadDriverProfile,
    updateDriverProfile: async (updates: Partial<Driver>) => {
      // Implementation would go here
    },
    loadTripHistory,
    loadEarningsData,
    addTripOptimistically: async (trip: Trip) => {
      await optimisticDispatch(
        { type: 'earnings/addTrip', payload: trip },
        { type: 'earnings/setError', payload: 'Failed to add trip' }
      );
    },
    invalidateCache,
    prefetchData: async (keys: string[]) => {
      // Implementation for prefetching specific data
    },
    getCacheStats,
    refreshAllData,
    subscribeToUpdates,
    getStateSlice,
    getDebugInfo,
    timeTravel,
    exportState,
    importState,
  };

  return (
    <EnhancedStateContext.Provider value={contextValue}>
      {children}
    </EnhancedStateContext.Provider>
  );
};

export const useEnhancedState = (): EnhancedStateContextType => {
  const context = useContext(EnhancedStateContext);
  if (!context) {
    throw new Error('useEnhancedState must be used within an EnhancedStateProvider');
  }
  return context;
};

export default EnhancedStateContext;