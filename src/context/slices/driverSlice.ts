// Driver State Slice
import { DriverSlice, StateAction } from './types';
import { Location, Driver } from '../../types';

export const initialDriverState: DriverSlice = {
  isOnline: false,
  location: null,
  availableDrivers: [],
  stats: {
    acceptanceRate: 0,
    cancellationRate: 0,
    rating: 0,
    totalTrips: 0,
  },
  loading: false,
  error: null,
  lastLocationUpdate: null,
};

export const driverReducer = (state: DriverSlice, action: StateAction): DriverSlice => {
  switch (action.type) {
    case 'driver/setOnlineStatus':
      return {
        ...state,
        isOnline: action.payload,
      };

    case 'driver/setLocation':
      return {
        ...state,
        location: action.payload,
        lastLocationUpdate: new Date(),
      };

    case 'driver/setAvailableDrivers':
      return {
        ...state,
        availableDrivers: action.payload,
      };

    case 'driver/updateStats':
      return {
        ...state,
        stats: {
          ...state.stats,
          ...action.payload,
        },
      };

    case 'driver/setLoading':
      return {
        ...state,
        loading: action.payload,
      };

    case 'driver/setError':
      return {
        ...state,
        error: action.payload,
        loading: false,
      };

    default:
      return state;
  }
};

// Driver selectors
export const driverSelectors = {
  isOnline: (state: DriverSlice) => state.isOnline,
  location: (state: DriverSlice) => state.location,
  availableDrivers: (state: DriverSlice) => state.availableDrivers,
  stats: (state: DriverSlice) => state.stats,
  isLoading: (state: DriverSlice) => state.loading,
  error: (state: DriverSlice) => state.error,
  lastLocationUpdate: (state: DriverSlice) => state.lastLocationUpdate,
  
  // Computed selectors
  hasLocation: (state: DriverSlice) => state.location !== null,
  isLocationStale: (state: DriverSlice, maxAgeMs: number = 5 * 60 * 1000) => {
    if (!state.lastLocationUpdate) return true;
    return Date.now() - state.lastLocationUpdate.getTime() > maxAgeMs;
  },
  nearbyDrivers: (targetLocation: Location, maxDistanceKm: number = 5) => (state: DriverSlice) => {
    if (!targetLocation) return [];
    
    return state.availableDrivers.filter(driver => {
      if (!driver.currentLocation) return false;
      const distance = calculateDistance(
        targetLocation.latitude,
        targetLocation.longitude,
        driver.currentLocation.latitude,
        driver.currentLocation.longitude
      );
      return distance <= maxDistanceKm;
    });
  },
  driverRating: (state: DriverSlice) => {
    const rating = state.stats.rating;
    return {
      value: rating,
      stars: Math.round(rating * 2) / 2, // Round to nearest 0.5
      text: rating >= 4.8 ? 'Excellent' : 
            rating >= 4.5 ? 'Very Good' : 
            rating >= 4.0 ? 'Good' : 
            rating >= 3.5 ? 'Fair' : 'Poor'
    };
  },
  performanceMetrics: (state: DriverSlice) => ({
    acceptanceRate: {
      value: state.stats.acceptanceRate,
      grade: state.stats.acceptanceRate >= 0.9 ? 'A' :
             state.stats.acceptanceRate >= 0.8 ? 'B' :
             state.stats.acceptanceRate >= 0.7 ? 'C' : 'D',
    },
    cancellationRate: {
      value: state.stats.cancellationRate,
      status: state.stats.cancellationRate <= 0.05 ? 'excellent' :
              state.stats.cancellationRate <= 0.1 ? 'good' :
              state.stats.cancellationRate <= 0.15 ? 'fair' : 'poor',
    },
    totalTrips: state.stats.totalTrips,
    experienceLevel: state.stats.totalTrips >= 1000 ? 'expert' :
                    state.stats.totalTrips >= 500 ? 'experienced' :
                    state.stats.totalTrips >= 100 ? 'intermediate' : 'beginner',
  }),
};

// Helper function to calculate distance between two points
function calculateDistance(lat1: number, lon1: number, lat2: number, lon2: number): number {
  const R = 6371; // Earth's radius in kilometers
  const dLat = (lat2 - lat1) * Math.PI / 180;
  const dLon = (lon2 - lon1) * Math.PI / 180;
  const a = 
    Math.sin(dLat / 2) * Math.sin(dLat / 2) +
    Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) * 
    Math.sin(dLon / 2) * Math.sin(dLon / 2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
  return R * c;
}