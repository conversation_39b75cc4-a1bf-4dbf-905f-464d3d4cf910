// State Slices Index
export * from './types';
export * from './authSlice';
export * from './rideSlice';
export * from './driverSlice';
export * from './earningsSlice';
export * from './uiSlice';
export * from './systemSlice';

import { GlobalState } from './types';
import { initialAuthState } from './authSlice';
import { initialRideState } from './rideSlice';
import { initialDriverState } from './driverSlice';
import { initialEarningsState } from './earningsSlice';
import { initialUIState } from './uiSlice';
import { initialSystemState } from './systemSlice';

// Combined initial state
export const initialGlobalState: GlobalState = {
  auth: initialAuthState,
  rides: initialRideState,
  driver: initialDriverState,
  earnings: initialEarningsState,
  ui: initialUIState,
  system: initialSystemState,
};