// Enhanced State Management Types
import { Driver, Ride, Location, Trip, ApiLoadingState, ApiErrorState, ConnectionState, CacheState, SyncState } from '../../types';

// State slice interfaces
export interface AuthSlice {
  isAuthenticated: boolean;
  driver: Driver | null;
  token: string | null;
  loading: boolean;
  error: string | null;
  lastLoginTime: Date | null;
  sessionExpiry: Date | null;
}

export interface RideSlice {
  currentRide: Ride | null;
  availableRides: Ride[];
  queuedBookings: Ride[];
  pendingBookings: Ride[];
  rideStatus: 'heading_to_pickup' | 'arrived_at_pickup' | 'passenger_onboard' | 'arrived_at_destination' | null;
  queueStatus: 'available' | 'limited_queue' | 'service_mode' | 'completing';
  maxQueueSize: number;
  lastRideUpdate: Date | null;
  loading: boolean;
  error: string | null;
}

export interface DriverSlice {
  isOnline: boolean;
  location: Location | null;
  availableDrivers: Driver[];
  stats: {
    acceptanceRate: number;
    cancellationRate: number;
    rating: number;
    totalTrips: number;
  };
  loading: boolean;
  error: string | null;
  lastLocationUpdate: Date | null;
}

export interface EarningsSlice {
  totalEarnings: number;
  weeklyEarnings: number;
  dailyEarnings: number;
  todayTrips: number;
  trips: Trip[];
  earnings: {
    today: number;
    week: number;
    month: number;
    total: number;
  };
  loading: boolean;
  error: string | null;
  lastEarningsUpdate: Date | null;
}

export interface UISlice {
  sidebarOpen: boolean;
  navigationVisible: boolean;
  activeScreen: string;
  mapRegion: {
    latitude: number;
    longitude: number;
    latitudeDelta: number;
    longitudeDelta: number;
  };
  theme: 'light' | 'dark';
  notifications: {
    enabled: boolean;
    sound: boolean;
    vibration: boolean;
  };
}

export interface SystemSlice {
  loading: ApiLoadingState;
  errors: ApiErrorState;
  connection: ConnectionState;
  cache: CacheState;
  sync: SyncState;
  performance: {
    renderTime: number;
    apiResponseTimes: Record<string, number>;
    memoryUsage: number;
  };
  debug: {
    enabled: boolean;
    logLevel: 'error' | 'warn' | 'info' | 'debug';
    actionHistory: Array<{ type: string; timestamp: Date; payload: any }>;
  };
}

// Combined state interface
export interface GlobalState {
  auth: AuthSlice;
  rides: RideSlice;
  driver: DriverSlice;
  earnings: EarningsSlice;
  ui: UISlice;
  system: SystemSlice;
}

// Action types with payload
export type StateAction = 
  // Auth actions
  | { type: 'auth/login'; payload: { driver: Driver; token: string } }
  | { type: 'auth/logout' }
  | { type: 'auth/updateDriver'; payload: Driver }
  | { type: 'auth/setLoading'; payload: boolean }
  | { type: 'auth/setError'; payload: string | null }
  | { type: 'auth/refreshSession'; payload: { token: string; expiry: Date } }
  
  // Ride actions
  | { type: 'rides/setCurrentRide'; payload: Ride | null }
  | { type: 'rides/setAvailableRides'; payload: Ride[] }
  | { type: 'rides/addAvailableRide'; payload: Ride }
  | { type: 'rides/removeAvailableRide'; payload: string }
  | { type: 'rides/setQueuedBookings'; payload: Ride[] }
  | { type: 'rides/addQueuedBooking'; payload: Ride }
  | { type: 'rides/removeQueuedBooking'; payload: string }
  | { type: 'rides/setRideStatus'; payload: RideSlice['rideStatus'] }
  | { type: 'rides/setQueueStatus'; payload: RideSlice['queueStatus'] }
  | { type: 'rides/setLoading'; payload: boolean }
  | { type: 'rides/setError'; payload: string | null }
  | { type: 'rides/updateLastUpdate' }
  
  // Driver actions
  | { type: 'driver/setOnlineStatus'; payload: boolean }
  | { type: 'driver/setLocation'; payload: Location }
  | { type: 'driver/setAvailableDrivers'; payload: Driver[] }
  | { type: 'driver/updateStats'; payload: Partial<DriverSlice['stats']> }
  | { type: 'driver/setLoading'; payload: boolean }
  | { type: 'driver/setError'; payload: string | null }
  
  // Earnings actions
  | { type: 'earnings/setTrips'; payload: Trip[] }
  | { type: 'earnings/addTrip'; payload: Trip }
  | { type: 'earnings/updateEarnings'; payload: Partial<EarningsSlice['earnings']> }
  | { type: 'earnings/setTodayTrips'; payload: number }
  | { type: 'earnings/setLoading'; payload: boolean }
  | { type: 'earnings/setError'; payload: string | null }
  
  // UI actions
  | { type: 'ui/toggleSidebar'; payload?: boolean }
  | { type: 'ui/setNavigationVisibility'; payload: boolean }
  | { type: 'ui/setActiveScreen'; payload: string }
  | { type: 'ui/setMapRegion'; payload: UISlice['mapRegion'] }
  | { type: 'ui/setTheme'; payload: 'light' | 'dark' }
  | { type: 'ui/updateNotificationSettings'; payload: Partial<UISlice['notifications']> }
  
  // System actions
  | { type: 'system/setLoading'; payload: { section: keyof ApiLoadingState; loading: boolean } }
  | { type: 'system/setError'; payload: { section: keyof ApiErrorState; error: string | null } }
  | { type: 'system/clearErrors' }
  | { type: 'system/setConnectionState'; payload: Partial<ConnectionState> }
  | { type: 'system/setCache'; payload: { section: keyof CacheState; data: any } }
  | { type: 'system/clearCache'; payload?: keyof CacheState }
  | { type: 'system/addPendingAction'; payload: { type: string; payload: any } }
  | { type: 'system/removePendingAction'; payload: string }
  | { type: 'system/setSyncStatus'; payload: { isSyncing: boolean; lastSyncTime?: Date } }
  | { type: 'system/updatePerformance'; payload: Partial<SystemSlice['performance']> }
  | { type: 'system/addDebugAction'; payload: { type: string; payload: any } }
  | { type: 'system/setDebugEnabled'; payload: boolean };

// Middleware types
export interface StateMiddleware {
  (action: StateAction, state: GlobalState, next: (action: StateAction) => void): void;
}

export interface StateListener {
  (state: GlobalState, previousState: GlobalState, action: StateAction): void;
}

// Cache configuration
export interface CacheConfig {
  ttl: number; // Time to live in milliseconds
  maxSize: number; // Maximum cache size
  strategies: {
    rides: 'stale-while-revalidate' | 'cache-first' | 'network-first';
    trips: 'stale-while-revalidate' | 'cache-first' | 'network-first';
    earnings: 'stale-while-revalidate' | 'cache-first' | 'network-first';
    driver: 'stale-while-revalidate' | 'cache-first' | 'network-first';
  };
}

// Optimistic update configuration
export interface OptimisticConfig {
  enabled: boolean;
  timeout: number; // Rollback timeout in milliseconds
  retryCount: number;
}