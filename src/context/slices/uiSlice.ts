// UI State Slice
import { UISlice, StateAction } from './types';

export const initialUIState: UISlice = {
  sidebarOpen: false,
  navigationVisible: true,
  activeScreen: 'Dashboard',
  mapRegion: {
    latitude: 37.7749,
    longitude: -122.4194,
    latitudeDelta: 0.0922,
    longitudeDelta: 0.0421,
  },
  theme: 'light',
  notifications: {
    enabled: true,
    sound: true,
    vibration: true,
  },
};

export const uiReducer = (state: UISlice, action: StateAction): UISlice => {
  switch (action.type) {
    case 'ui/toggleSidebar':
      return {
        ...state,
        sidebarOpen: action.payload !== undefined ? action.payload : !state.sidebarOpen,
      };

    case 'ui/setNavigationVisibility':
      return {
        ...state,
        navigationVisible: action.payload,
      };

    case 'ui/setActiveScreen':
      return {
        ...state,
        activeScreen: action.payload,
      };

    case 'ui/setMapRegion':
      return {
        ...state,
        mapRegion: action.payload,
      };

    case 'ui/setTheme':
      return {
        ...state,
        theme: action.payload,
      };

    case 'ui/updateNotificationSettings':
      return {
        ...state,
        notifications: {
          ...state.notifications,
          ...action.payload,
        },
      };

    default:
      return state;
  }
};

// UI selectors
export const uiSelectors = {
  sidebarOpen: (state: UISlice) => state.sidebarOpen,
  navigationVisible: (state: UISlice) => state.navigationVisible,
  activeScreen: (state: UISlice) => state.activeScreen,
  mapRegion: (state: UISlice) => state.mapRegion,
  theme: (state: UISlice) => state.theme,
  notifications: (state: UISlice) => state.notifications,
  
  // Computed selectors
  isDarkMode: (state: UISlice) => state.theme === 'dark',
  isLightMode: (state: UISlice) => state.theme === 'light',
  shouldShowNavigation: (state: UISlice) => state.navigationVisible,
  canReceiveNotifications: (state: UISlice) => state.notifications.enabled,
  notificationSettings: (state: UISlice) => ({
    sound: state.notifications.enabled && state.notifications.sound,
    vibration: state.notifications.enabled && state.notifications.vibration,
    visual: state.notifications.enabled,
  }),
  
  // Screen-specific selectors
  isActiveScreen: (screenName: string) => (state: UISlice) => 
    state.activeScreen === screenName,
    
  getMapCenter: (state: UISlice) => ({
    latitude: state.mapRegion.latitude,
    longitude: state.mapRegion.longitude,
  }),
  
  getMapZoomLevel: (state: UISlice) => {
    // Calculate zoom level from delta (approximate)
    const latZoom = Math.log2(360 / state.mapRegion.latitudeDelta);
    const lonZoom = Math.log2(360 / state.mapRegion.longitudeDelta);
    return Math.min(latZoom, lonZoom);
  },
};