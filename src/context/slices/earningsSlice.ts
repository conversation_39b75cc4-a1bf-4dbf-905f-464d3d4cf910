// Earnings State Slice
import { EarningsSlice, StateAction } from './types';
import { Trip } from '../../types';

export const initialEarningsState: EarningsSlice = {
  totalEarnings: 0,
  weeklyEarnings: 0,
  dailyEarnings: 0,
  todayTrips: 0,
  trips: [],
  earnings: {
    today: 0,
    week: 0,
    month: 0,
    total: 0,
  },
  loading: false,
  error: null,
  lastEarningsUpdate: null,
};

export const earningsReducer = (state: EarningsSlice, action: StateAction): EarningsSlice => {
  switch (action.type) {
    case 'earnings/setTrips':
      const totalFromTrips = action.payload.reduce((sum, trip) => sum + trip.earnings, 0);
      return {
        ...state,
        trips: action.payload,
        totalEarnings: totalFromTrips,
        earnings: {
          ...state.earnings,
          total: totalFromTrips,
        },
        lastEarningsUpdate: new Date(),
      };

    case 'earnings/addTrip':
      const newTrip = action.payload;
      return {
        ...state,
        trips: [newTrip, ...state.trips],
        totalEarnings: state.totalEarnings + newTrip.earnings,
        earnings: {
          ...state.earnings,
          total: state.earnings.total + newTrip.earnings,
          today: isToday(newTrip.completedAt) ? 
            state.earnings.today + newTrip.earnings : state.earnings.today,
          week: isThisWeek(newTrip.completedAt) ? 
            state.earnings.week + newTrip.earnings : state.earnings.week,
          month: isThisMonth(newTrip.completedAt) ? 
            state.earnings.month + newTrip.earnings : state.earnings.month,
        },
        todayTrips: isToday(newTrip.completedAt) ? state.todayTrips + 1 : state.todayTrips,
        lastEarningsUpdate: new Date(),
      };

    case 'earnings/updateEarnings':
      return {
        ...state,
        earnings: {
          ...state.earnings,
          ...action.payload,
        },
        totalEarnings: action.payload.total || state.totalEarnings,
        weeklyEarnings: action.payload.week || state.weeklyEarnings,
        dailyEarnings: action.payload.today || state.dailyEarnings,
        lastEarningsUpdate: new Date(),
      };

    case 'earnings/setTodayTrips':
      return {
        ...state,
        todayTrips: action.payload,
      };

    case 'earnings/setLoading':
      return {
        ...state,
        loading: action.payload,
      };

    case 'earnings/setError':
      return {
        ...state,
        error: action.payload,
        loading: false,
      };

    default:
      return state;
  }
};

// Helper functions for date calculations
const isToday = (date: Date): boolean => {
  const today = new Date();
  return date.toDateString() === today.toDateString();
};

const isThisWeek = (date: Date): boolean => {
  const today = new Date();
  const weekStart = new Date(today.setDate(today.getDate() - today.getDay()));
  const weekEnd = new Date(today.setDate(today.getDate() - today.getDay() + 6));
  return date >= weekStart && date <= weekEnd;
};

const isThisMonth = (date: Date): boolean => {
  const today = new Date();
  return date.getMonth() === today.getMonth() && date.getFullYear() === today.getFullYear();
};

// Earnings selectors
export const earningsSelectors = {
  totalEarnings: (state: EarningsSlice) => state.totalEarnings,
  weeklyEarnings: (state: EarningsSlice) => state.weeklyEarnings,
  dailyEarnings: (state: EarningsSlice) => state.dailyEarnings,
  todayTrips: (state: EarningsSlice) => state.todayTrips,
  trips: (state: EarningsSlice) => state.trips,
  earnings: (state: EarningsSlice) => state.earnings,
  isLoading: (state: EarningsSlice) => state.loading,
  error: (state: EarningsSlice) => state.error,
  lastUpdate: (state: EarningsSlice) => state.lastEarningsUpdate,
  
  // Computed selectors
  averageEarningsPerTrip: (state: EarningsSlice) => {
    if (state.trips.length === 0) return 0;
    return state.totalEarnings / state.trips.length;
  },
  
  todayStats: (state: EarningsSlice) => {
    const todayTrips = state.trips.filter(trip => isToday(trip.completedAt));
    return {
      tripCount: todayTrips.length,
      earnings: todayTrips.reduce((sum, trip) => sum + trip.earnings, 0),
      averagePerTrip: todayTrips.length > 0 ? 
        todayTrips.reduce((sum, trip) => sum + trip.earnings, 0) / todayTrips.length : 0,
      totalDistance: todayTrips.reduce((sum, trip) => sum + trip.distance, 0),
      totalDuration: todayTrips.reduce((sum, trip) => sum + trip.duration, 0),
    };
  },
  
  weeklyStats: (state: EarningsSlice) => {
    const weeklyTrips = state.trips.filter(trip => isThisWeek(trip.completedAt));
    return {
      tripCount: weeklyTrips.length,
      earnings: weeklyTrips.reduce((sum, trip) => sum + trip.earnings, 0),
      averagePerTrip: weeklyTrips.length > 0 ? 
        weeklyTrips.reduce((sum, trip) => sum + trip.earnings, 0) / weeklyTrips.length : 0,
      totalDistance: weeklyTrips.reduce((sum, trip) => sum + trip.distance, 0),
      totalDuration: weeklyTrips.reduce((sum, trip) => sum + trip.duration, 0),
    };
  },
  
  monthlyStats: (state: EarningsSlice) => {
    const monthlyTrips = state.trips.filter(trip => isThisMonth(trip.completedAt));
    return {
      tripCount: monthlyTrips.length,
      earnings: monthlyTrips.reduce((sum, trip) => sum + trip.earnings, 0),
      averagePerTrip: monthlyTrips.length > 0 ? 
        monthlyTrips.reduce((sum, trip) => sum + trip.earnings, 0) / monthlyTrips.length : 0,
      totalDistance: monthlyTrips.reduce((sum, trip) => sum + trip.distance, 0),
      totalDuration: monthlyTrips.reduce((sum, trip) => sum + trip.duration, 0),
    };
  },
  
  earningsTrend: (period: 'week' | 'month' = 'week') => (state: EarningsSlice) => {
    const now = new Date();
    const days = period === 'week' ? 7 : 30;
    const trend = [];
    
    for (let i = days - 1; i >= 0; i--) {
      const date = new Date(now);
      date.setDate(date.getDate() - i);
      
      const dayTrips = state.trips.filter(trip => {
        const tripDate = new Date(trip.completedAt);
        return tripDate.toDateString() === date.toDateString();
      });
      
      trend.push({
        date: date.toDateString(),
        earnings: dayTrips.reduce((sum, trip) => sum + trip.earnings, 0),
        tripCount: dayTrips.length,
      });
    }
    
    return trend;
  },
  
  topEarningTrips: (limit: number = 10) => (state: EarningsSlice) => 
    [...state.trips]
      .sort((a, b) => b.earnings - a.earnings)
      .slice(0, limit),
      
  recentTrips: (limit: number = 10) => (state: EarningsSlice) =>
    [...state.trips]
      .sort((a, b) => b.completedAt.getTime() - a.completedAt.getTime())
      .slice(0, limit),
      
  earningsGrowth: (state: EarningsSlice) => {
    const thisWeek = earningsSelectors.weeklyStats(state).earnings;
    const lastWeekTrips = state.trips.filter(trip => {
      const tripDate = trip.completedAt;
      const now = new Date();
      const weekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
      const twoWeeksAgo = new Date(now.getTime() - 14 * 24 * 60 * 60 * 1000);
      return tripDate >= twoWeeksAgo && tripDate < weekAgo;
    });
    const lastWeek = lastWeekTrips.reduce((sum, trip) => sum + trip.earnings, 0);
    
    if (lastWeek === 0) return 0;
    return ((thisWeek - lastWeek) / lastWeek) * 100;
  },
};