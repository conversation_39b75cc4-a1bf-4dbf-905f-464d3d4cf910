// Ride State Slice
import { RideSlice, StateAction } from './types';
import { Ride } from '../../types';

export const initialRideState: RideSlice = {
  currentRide: null,
  availableRides: [],
  queuedBookings: [],
  pendingBookings: [],
  rideStatus: null,
  queueStatus: 'available',
  maxQueueSize: 3,
  lastRideUpdate: null,
  loading: false,
  error: null,
};

export const rideReducer = (state: RideSlice, action: StateAction): RideSlice => {
  switch (action.type) {
    case 'rides/setCurrentRide':
      return {
        ...state,
        currentRide: action.payload,
        lastRideUpdate: new Date(),
      };

    case 'rides/setAvailableRides':
      return {
        ...state,
        availableRides: action.payload,
        lastRideUpdate: new Date(),
      };

    case 'rides/addAvailableRide':
      return {
        ...state,
        availableRides: [...state.availableRides, action.payload],
        lastRideUpdate: new Date(),
      };

    case 'rides/removeAvailableRide':
      return {
        ...state,
        availableRides: state.availableRides.filter(ride => ride.id !== action.payload),
        lastRideUpdate: new Date(),
      };

    case 'rides/setQueuedBookings':
      return {
        ...state,
        queuedBookings: action.payload,
        lastRideUpdate: new Date(),
      };

    case 'rides/addQueuedBooking':
      return {
        ...state,
        queuedBookings: [...state.queuedBookings, action.payload],
        lastRideUpdate: new Date(),
      };

    case 'rides/removeQueuedBooking':
      return {
        ...state,
        queuedBookings: state.queuedBookings.filter(booking => booking.id !== action.payload),
        lastRideUpdate: new Date(),
      };

    case 'rides/setRideStatus':
      const newMaxQueueSize = getMaxQueueSizeForStatus(action.payload);
      return {
        ...state,
        rideStatus: action.payload,
        maxQueueSize: newMaxQueueSize,
      };

    case 'rides/setQueueStatus':
      return {
        ...state,
        queueStatus: action.payload,
      };

    case 'rides/setLoading':
      return {
        ...state,
        loading: action.payload,
      };

    case 'rides/setError':
      return {
        ...state,
        error: action.payload,
        loading: false,
      };

    case 'rides/updateLastUpdate':
      return {
        ...state,
        lastRideUpdate: new Date(),
      };

    default:
      return state;
  }
};

// Helper functions
const getMaxQueueSizeForStatus = (
  status: RideSlice['rideStatus']
): number => {
  if (!status) return 3;
  switch (status) {
    case 'heading_to_pickup': return 3;
    case 'arrived_at_pickup': return 2;
    case 'passenger_onboard': return 0;
    case 'arrived_at_destination': return 1;
    default: return 3;
  }
};

// Ride selectors
export const rideSelectors = {
  currentRide: (state: RideSlice) => state.currentRide,
  availableRides: (state: RideSlice) => state.availableRides,
  queuedBookings: (state: RideSlice) => state.queuedBookings,
  pendingBookings: (state: RideSlice) => state.pendingBookings,
  rideStatus: (state: RideSlice) => state.rideStatus,
  queueStatus: (state: RideSlice) => state.queueStatus,
  maxQueueSize: (state: RideSlice) => state.maxQueueSize,
  isLoading: (state: RideSlice) => state.loading,
  error: (state: RideSlice) => state.error,
  
  // Computed selectors
  hasCurrentRide: (state: RideSlice) => state.currentRide !== null,
  availableRideCount: (state: RideSlice) => state.availableRides.length,
  queuedBookingCount: (state: RideSlice) => state.queuedBookings.length,
  canAcceptNewBooking: (state: RideSlice) => {
    if (!state.currentRide) return true;
    if (state.rideStatus === 'passenger_onboard' || state.rideStatus === 'arrived_at_destination') {
      return false;
    }
    return state.queuedBookings.length < state.maxQueueSize;
  },
  
  getAllBookings: (state: RideSlice) => [
    ...state.availableRides,
    ...state.queuedBookings,
    ...(state.currentRide ? [state.currentRide] : []),
    ...state.pendingBookings
  ],
  
  getBookingsByStatus: (status: Ride['status']) => (state: RideSlice) =>
    rideSelectors.getAllBookings(state).filter(ride => ride.status === status),
    
  getBookingsByType: (bookingStatus: Ride['bookingStatus']) => (state: RideSlice) =>
    rideSelectors.getAllBookings(state).filter(ride => ride.bookingStatus === bookingStatus),
    
  getActiveBookings: (state: RideSlice) =>
    rideSelectors.getAllBookings(state).filter(ride => 
      ride.status === 'pending' && (ride.bookingStatus === 'urgent' || ride.bookingStatus === 'new')
    ),
    
  getScheduledBookings: (state: RideSlice) =>
    rideSelectors.getAllBookings(state).filter(ride => 
      ride.bookingStatus === 'scheduled' && ride.scheduledTime
    ),
    
  getUrgentRides: (state: RideSlice) =>
    state.availableRides.filter(ride => 
      ride.bookingStatus === 'urgent' || ride.priority === 'high'
    ),
    
  getHighValueRides: (state: RideSlice) =>
    state.availableRides.filter(ride => 
      ride.fare > 50 || ride.bookingStatus === 'premium'
    ),
}