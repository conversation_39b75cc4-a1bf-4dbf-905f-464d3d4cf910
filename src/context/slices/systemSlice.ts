// System State Slice
import { SystemSlice, StateAction } from './types';
import { ApiLoadingState, ApiErrorState, ConnectionState, CacheState, SyncState } from '../../types';

export const initialSystemState: SystemSlice = {
  loading: {
    auth: false,
    rides: false,
    trips: false,
    driver: false,
    earnings: false,
    bookings: false,
    navigation: false,
    communication: false,
  },
  errors: {
    auth: null,
    rides: null,
    trips: null,
    driver: null,
    earnings: null,
    bookings: null,
    navigation: null,
    communication: null,
    network: null,
  },
  connection: {
    isOnline: true,
    isApiConnected: false,
    isWebSocketConnected: false,
    lastConnectionCheck: null,
    retryCount: 0,
  },
  cache: {
    rides: null,
    trips: null,
    earnings: null,
    driver: null,
  },
  sync: {
    pendingActions: [],
    lastSyncTime: null,
    isSyncing: false,
  },
  performance: {
    renderTime: 0,
    apiResponseTimes: {},
    memoryUsage: 0,
  },
  debug: {
    enabled: __DEV__,
    logLevel: __DEV__ ? 'debug' : 'error',
    actionHistory: [],
  },
};

export const systemReducer = (state: SystemSlice, action: StateAction): SystemSlice => {
  // Log action in debug mode
  const newState = (() => {
    switch (action.type) {
      case 'system/setLoading':
        return {
          ...state,
          loading: {
            ...state.loading,
            [action.payload.section]: action.payload.loading,
          },
        };

      case 'system/setError':
        return {
          ...state,
          errors: {
            ...state.errors,
            [action.payload.section]: action.payload.error,
          },
        };

      case 'system/clearErrors':
        const clearedErrors = Object.keys(state.errors).reduce(
          (acc, key) => ({ ...acc, [key]: null }),
          {} as ApiErrorState
        );
        return {
          ...state,
          errors: clearedErrors,
        };

      case 'system/setConnectionState':
        return {
          ...state,
          connection: {
            ...state.connection,
            ...action.payload,
            lastConnectionCheck: new Date(),
          },
        };

      case 'system/setCache':
        return {
          ...state,
          cache: {
            ...state.cache,
            [action.payload.section]: {
              data: action.payload.data,
              timestamp: Date.now(),
            },
          },
        };

      case 'system/clearCache':
        if (action.payload) {
          return {
            ...state,
            cache: {
              ...state.cache,
              [action.payload]: null,
            },
          };
        } else {
          return {
            ...state,
            cache: {
              rides: null,
              trips: null,
              earnings: null,
              driver: null,
            },
          };
        }

      case 'system/addPendingAction':
        return {
          ...state,
          sync: {
            ...state.sync,
            pendingActions: [
              ...state.sync.pendingActions,
              {
                id: `action-${Date.now()}-${Math.random()}`,
                type: action.payload.type,
                payload: action.payload.payload,
                timestamp: new Date(),
                retryCount: 0,
              },
            ],
          },
        };

      case 'system/removePendingAction':
        return {
          ...state,
          sync: {
            ...state.sync,
            pendingActions: state.sync.pendingActions.filter(
              pendingAction => pendingAction.id !== action.payload
            ),
          },
        };

      case 'system/setSyncStatus':
        return {
          ...state,
          sync: {
            ...state.sync,
            isSyncing: action.payload.isSyncing,
            lastSyncTime: action.payload.lastSyncTime || state.sync.lastSyncTime,
          },
        };

      case 'system/updatePerformance':
        return {
          ...state,
          performance: {
            ...state.performance,
            ...action.payload,
          },
        };

      case 'system/addDebugAction':
        const maxHistorySize = 100;
        const newHistory = [
          {
            type: action.payload.type,
            timestamp: new Date(),
            payload: action.payload.payload,
          },
          ...state.debug.actionHistory,
        ].slice(0, maxHistorySize);

        return {
          ...state,
          debug: {
            ...state.debug,
            actionHistory: newHistory,
          },
        };

      case 'system/setDebugEnabled':
        return {
          ...state,
          debug: {
            ...state.debug,
            enabled: action.payload,
          },
        };

      default:
        return state;
    }
  })();

  // Add to debug history if enabled
  if (newState.debug.enabled) {
    return systemReducer(newState, {
      type: 'system/addDebugAction',
      payload: { type: action.type, payload: action.type.startsWith('system/') ? null : action.payload }
    });
  }

  return newState;
};

// System selectors
export const systemSelectors = {
  loading: (state: SystemSlice) => state.loading,
  errors: (state: SystemSlice) => state.errors,
  connection: (state: SystemSlice) => state.connection,
  cache: (state: SystemSlice) => state.cache,
  sync: (state: SystemSlice) => state.sync,
  performance: (state: SystemSlice) => state.performance,
  debug: (state: SystemSlice) => state.debug,

  // Computed selectors
  isLoading: (section?: keyof ApiLoadingState) => (state: SystemSlice) => 
    section ? state.loading[section] : Object.values(state.loading).some(Boolean),

  hasError: (section?: keyof ApiErrorState) => (state: SystemSlice) => 
    section ? state.errors[section] !== null : Object.values(state.errors).some(error => error !== null),

  getError: (section: keyof ApiErrorState) => (state: SystemSlice) => 
    state.errors[section],

  getAllErrors: (state: SystemSlice) => 
    Object.entries(state.errors)
      .filter(([_, error]) => error !== null)
      .map(([section, error]) => ({ section, error })),

  isConnected: (state: SystemSlice) => 
    state.connection.isOnline && state.connection.isApiConnected,

  isOffline: (state: SystemSlice) => !state.connection.isOnline,

  isWebSocketConnected: (state: SystemSlice) => state.connection.isWebSocketConnected,

  hasPendingActions: (state: SystemSlice) => state.sync.pendingActions.length > 0,

  isSyncing: (state: SystemSlice) => state.sync.isSyncing,

  isDataStale: (section: keyof CacheState, maxAge: number = 5 * 60 * 1000) => (state: SystemSlice) => {
    const cached = state.cache[section];
    if (!cached) return true;
    const age = Date.now() - cached.timestamp;
    return age > maxAge;
  },

  getCachedData: (section: keyof CacheState) => (state: SystemSlice) => 
    state.cache[section]?.data,

  getLastSyncTime: (state: SystemSlice) => state.sync.lastSyncTime,

  getConnectionQuality: (state: SystemSlice) => {
    const { isOnline, isApiConnected, isWebSocketConnected, retryCount } = state.connection;
    
    if (!isOnline) return 'offline';
    if (isApiConnected && isWebSocketConnected && retryCount === 0) return 'excellent';
    if (isApiConnected && retryCount < 3) return 'good';
    if (isApiConnected && retryCount < 5) return 'fair';
    return 'poor';
  },

  getPerformanceMetrics: (state: SystemSlice) => ({
    averageRenderTime: state.performance.renderTime,
    averageApiResponseTime: Object.values(state.performance.apiResponseTimes)
      .reduce((sum, time, _, arr) => sum + time / arr.length, 0),
    memoryUsage: state.performance.memoryUsage,
    slowApiEndpoints: Object.entries(state.performance.apiResponseTimes)
      .filter(([_, time]) => time > 2000)
      .map(([endpoint, time]) => ({ endpoint, time })),
  }),

  getRecentActions: (limit: number = 10) => (state: SystemSlice) => 
    state.debug.actionHistory.slice(0, limit),

  getActionsByType: (actionType: string) => (state: SystemSlice) => 
    state.debug.actionHistory.filter(action => action.type.includes(actionType)),

  isDebugEnabled: (state: SystemSlice) => state.debug.enabled,
};