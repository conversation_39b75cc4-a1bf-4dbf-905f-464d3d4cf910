// Auth State Slice
import { AuthSlice, StateAction } from './types';
import { Driver } from '../../types';

export const initialAuthState: AuthSlice = {
  isAuthenticated: false,
  driver: null,
  token: null,
  loading: false,
  error: null,
  lastLoginTime: null,
  sessionExpiry: null,
};

export const authReducer = (state: AuthSlice, action: StateAction): AuthSlice => {
  switch (action.type) {
    case 'auth/login':
      return {
        ...state,
        isAuthenticated: true,
        driver: action.payload.driver,
        token: action.payload.token,
        error: null,
        loading: false,
        lastLoginTime: new Date(),
        sessionExpiry: new Date(Date.now() + 24 * 60 * 60 * 1000), // 24 hours
      };

    case 'auth/logout':
      return {
        ...initialAuthState,
      };

    case 'auth/updateDriver':
      return {
        ...state,
        driver: action.payload,
      };

    case 'auth/setLoading':
      return {
        ...state,
        loading: action.payload,
      };

    case 'auth/setError':
      return {
        ...state,
        error: action.payload,
        loading: false,
      };

    case 'auth/refreshSession':
      return {
        ...state,
        token: action.payload.token,
        sessionExpiry: action.payload.expiry,
      };

    default:
      return state;
  }
};

// Auth selectors
export const authSelectors = {
  isAuthenticated: (state: AuthSlice) => state.isAuthenticated,
  driver: (state: AuthSlice) => state.driver,
  token: (state: AuthSlice) => state.token,
  isLoading: (state: AuthSlice) => state.loading,
  error: (state: AuthSlice) => state.error,
  isSessionExpired: (state: AuthSlice) => 
    state.sessionExpiry ? new Date() > state.sessionExpiry : false,
  timeUntilExpiry: (state: AuthSlice) => 
    state.sessionExpiry ? Math.max(0, state.sessionExpiry.getTime() - Date.now()) : 0,
};