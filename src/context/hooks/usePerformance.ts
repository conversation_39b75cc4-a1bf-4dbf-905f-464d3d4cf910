// Performance Monitoring Hook
import { useCallback, useMemo, useRef, useEffect } from 'react';
import { useEnhancedState } from '../EnhancedStateContext';

interface PerformanceMetric {
  name: string;
  startTime: number;
  endTime?: number;
  duration?: number;
  metadata?: any;
}

export const usePerformance = () => {
  const { state, dispatch } = useEnhancedState();
  const metricsRef = useRef<Map<string, PerformanceMetric>>(new Map());
  const renderCountRef = useRef<number>(0);
  const lastRenderTimeRef = useRef<number>(Date.now());
  
  // Render performance tracking
  useEffect(() => {
    renderCountRef.current++;
    const now = Date.now();
    const timeSinceLastRender = now - lastRenderTimeRef.current;
    lastRenderTimeRef.current = now;
    
    if (timeSinceLastRender > 16) { // More than one frame at 60fps
      dispatch({
        type: 'system/updatePerformance',
        payload: {
          renderTime: timeSinceLastRender,
        },
      });
    }
  });
  
  // Performance measurement utilities
  const measure = useMemo(() => ({
    start: (name: string, metadata?: any) => {
      const metric: PerformanceMetric = {
        name,
        startTime: performance.now(),
        metadata,
      };
      metricsRef.current.set(name, metric);
    },
    
    end: (name: string) => {
      const metric = metricsRef.current.get(name);
      if (metric && !metric.endTime) {
        metric.endTime = performance.now();
        metric.duration = metric.endTime - metric.startTime;
        
        // Update system performance metrics
        if (name.startsWith('api_')) {
          dispatch({
            type: 'system/updatePerformance',
            payload: {
              apiResponseTimes: {
                [name]: metric.duration,
              },
            },
          });
        }
        
        // Log slow operations in development
        if (__DEV__ && metric.duration > 100) {
          console.warn(`⚠️ Slow operation detected: ${name} took ${metric.duration.toFixed(2)}ms`);
        }
        
        return metric.duration;
      }
      return null;
    },
    
    measure: async <T>(name: string, operation: () => Promise<T>, metadata?: any): Promise<T> => {
      measure.start(name, metadata);
      try {
        const result = await operation();
        return result;
      } finally {
        measure.end(name);
      }
    },
    
    measureSync: <T>(name: string, operation: () => T, metadata?: any): T => {
      measure.start(name, metadata);
      try {
        const result = operation();
        return result;
      } finally {
        measure.end(name);
      }
    },
  }), [dispatch]);
  
  // Memory usage monitoring
  const memoryMonitor = useMemo(() => ({
    getCurrentUsage: () => {
      if ('memory' in performance) {
        const memory = (performance as any).memory;
        return {
          used: memory.usedJSHeapSize,
          total: memory.totalJSHeapSize,
          limit: memory.jsHeapSizeLimit,
          percentage: (memory.usedJSHeapSize / memory.jsHeapSizeLimit) * 100,
        };
      }
      return null;
    },
    
    trackUsage: () => {
      const usage = memoryMonitor.getCurrentUsage();
      if (usage) {
        dispatch({
          type: 'system/updatePerformance',
          payload: {
            memoryUsage: usage.used,
          },
        });
        
        // Warn if memory usage is high
        if (usage.percentage > 80) {
          console.warn(`⚠️ High memory usage detected: ${usage.percentage.toFixed(1)}%`);
        }
      }
    },
    
    formatBytes: (bytes: number) => {
      const mb = bytes / (1024 * 1024);
      return `${mb.toFixed(1)} MB`;
    },
  }), [dispatch]);
  
  // React component performance tracking
  const componentMonitor = useMemo(() => ({
    trackRender: (componentName: string) => {
      const renderTime = Date.now() - lastRenderTimeRef.current;
      
      if (renderTime > 16) { // Slower than 60fps
        console.log(`📊 Component render: ${componentName} took ${renderTime}ms`);
      }
    },
    
    trackMount: (componentName: string) => {
      console.log(`🔧 Component mounted: ${componentName}`);
    },
    
    trackUnmount: (componentName: string) => {
      console.log(`🗑️ Component unmounted: ${componentName}`);
    },
    
    trackUpdate: (componentName: string, reason?: string) => {
      console.log(`🔄 Component updated: ${componentName}`, reason ? `(${reason})` : '');
    },
  }), []);
  
  // API performance tracking
  const apiMonitor = useMemo(() => ({
    trackRequest: async <T>(endpoint: string, request: () => Promise<T>): Promise<T> => {
      const metricName = `api_${endpoint.replace(/[^a-zA-Z0-9]/g, '_')}`;
      return await measure.measure(metricName, request, { endpoint });
    },
    
    trackBatchRequests: async (requests: Array<{ name: string; request: () => Promise<any> }>) => {
      const startTime = performance.now();
      const results = await Promise.allSettled(
        requests.map(({ name, request }) => 
          measure.measure(`api_batch_${name}`, request)
        )
      );
      const totalTime = performance.now() - startTime;
      
      console.log(`📦 Batch API requests completed in ${totalTime.toFixed(2)}ms`);
      return results;
    },
    
    getSlowEndpoints: (threshold: number = 1000) => {
      const apiTimes = state.system.performance.apiResponseTimes;
      return Object.entries(apiTimes)
        .filter(([, time]) => time > threshold)
        .sort(([, a], [, b]) => b - a)
        .map(([endpoint, time]) => ({ endpoint, time }));
    },
  }), [measure, state.system.performance.apiResponseTimes]);
  
  // Performance analytics
  const analytics = useMemo(() => {
    const metrics = Array.from(metricsRef.current.values());
    const completedMetrics = metrics.filter(m => m.duration !== undefined);
    
    if (completedMetrics.length === 0) {
      return {
        averageOperationTime: 0,
        slowestOperation: null,
        fastestOperation: null,
        totalOperations: 0,
        operationBreakdown: {},
      };
    }
    
    const durations = completedMetrics.map(m => m.duration!);
    const averageOperationTime = durations.reduce((sum, d) => sum + d, 0) / durations.length;
    
    const slowestOperation = completedMetrics.reduce((slowest, current) => 
      current.duration! > slowest.duration! ? current : slowest
    );
    
    const fastestOperation = completedMetrics.reduce((fastest, current) => 
      current.duration! < fastest.duration! ? current : fastest
    );
    
    const operationBreakdown = completedMetrics.reduce((breakdown, metric) => {
      const category = metric.name.split('_')[0];
      breakdown[category] = (breakdown[category] || 0) + 1;
      return breakdown;
    }, {} as Record<string, number>);
    
    return {
      averageOperationTime,
      slowestOperation,
      fastestOperation,
      totalOperations: completedMetrics.length,
      operationBreakdown,
    };
  }, []);
  
  // Performance recommendations
  const recommendations = useMemo(() => {
    const recs: string[] = [];
    
    if (analytics.averageOperationTime > 500) {
      recs.push('Consider optimizing slow operations or implementing caching');
    }
    
    if (renderCountRef.current > 100) {
      recs.push('High render count detected - check for unnecessary re-renders');
    }
    
    if (state.system.performance.renderTime > 50) {
      recs.push('Slow rendering detected - consider component optimization');
    }
    
    const memoryUsage = memoryMonitor.getCurrentUsage();
    if (memoryUsage && memoryUsage.percentage > 70) {
      recs.push('High memory usage - consider implementing cleanup strategies');
    }
    
    const slowEndpoints = apiMonitor.getSlowEndpoints(2000);
    if (slowEndpoints.length > 0) {
      recs.push(`Slow API endpoints detected: ${slowEndpoints.map(e => e.endpoint).join(', ')}`);
    }
    
    return recs;
  }, [analytics, state.system.performance, memoryMonitor, apiMonitor]);
  
  // Performance score calculation
  const performanceScore = useMemo(() => {
    let score = 100;
    
    // Deduct points for slow operations
    if (analytics.averageOperationTime > 100) score -= 20;
    if (analytics.averageOperationTime > 500) score -= 20;
    
    // Deduct points for slow rendering
    if (state.system.performance.renderTime > 32) score -= 15; // 2 frames
    if (state.system.performance.renderTime > 64) score -= 15; // 4 frames
    
    // Deduct points for memory usage
    const memoryUsage = memoryMonitor.getCurrentUsage();
    if (memoryUsage) {
      if (memoryUsage.percentage > 70) score -= 15;
      if (memoryUsage.percentage > 90) score -= 20;
    }
    
    // Deduct points for slow API calls
    const slowEndpoints = apiMonitor.getSlowEndpoints();
    score -= Math.min(30, slowEndpoints.length * 5);
    
    return Math.max(0, score);
  }, [analytics, state.system.performance, memoryMonitor, apiMonitor]);
  
  // Periodic memory tracking
  useEffect(() => {
    const interval = setInterval(() => {
      memoryMonitor.trackUsage();
    }, 30000); // Every 30 seconds
    
    return () => clearInterval(interval);
  }, [memoryMonitor]);
  
  return {
    // Measurement tools
    measure,
    
    // Monitors
    memoryMonitor,
    componentMonitor,
    apiMonitor,
    
    // Analytics
    analytics,
    performanceScore,
    recommendations,
    
    // Current metrics
    currentMetrics: Array.from(metricsRef.current.values()),
    systemPerformance: state.system.performance,
    renderCount: renderCountRef.current,
    
    // Utilities
    clearMetrics: () => metricsRef.current.clear(),
    
    getMetric: (name: string) => metricsRef.current.get(name),
    
    exportMetrics: () => ({
      metrics: Array.from(metricsRef.current.entries()),
      analytics,
      systemPerformance: state.system.performance,
      renderCount: renderCountRef.current,
      timestamp: new Date().toISOString(),
    }),
    
    // Formatted utilities
    formatDuration: (ms: number) => {
      if (ms < 1000) return `${ms.toFixed(1)}ms`;
      return `${(ms / 1000).toFixed(2)}s`;
    },
    
    getPerformanceGrade: () => {
      if (performanceScore >= 90) return 'A';
      if (performanceScore >= 80) return 'B';
      if (performanceScore >= 70) return 'C';
      if (performanceScore >= 60) return 'D';
      return 'F';
    },
    
    getScoreColor: () => {
      if (performanceScore >= 80) return '#4CAF50'; // Green
      if (performanceScore >= 60) return '#FF9800'; // Orange
      return '#F44336'; // Red
    },
    
    shouldShowPerformanceWarning: () => performanceScore < 70 || recommendations.length > 0,
  };
};