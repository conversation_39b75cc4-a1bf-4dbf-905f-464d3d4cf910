// Rides Management Hook
import { useCallback, useMemo, useEffect } from 'react';
import { useEnhancedState } from '../EnhancedStateContext';
import { rideSelectors } from '../slices/rideSlice';
import { Ride } from '../../types';

export const useRides = () => {
  const { state, dispatch, loadAvailableRides, acceptRide, rejectRide, completeRide } = useEnhancedState();
  
  // Memoized selectors
  const currentRide = useMemo(() => rideSelectors.currentRide(state.rides), [state.rides]);
  const availableRides = useMemo(() => rideSelectors.availableRides(state.rides), [state.rides]);
  const queuedBookings = useMemo(() => rideSelectors.queuedBookings(state.rides), [state.rides]);
  const rideStatus = useMemo(() => rideSelectors.rideStatus(state.rides), [state.rides]);
  const queueStatus = useMemo(() => rideSelectors.queueStatus(state.rides), [state.rides]);
  const canAcceptNewBooking = useMemo(() => rideSelectors.canAcceptNewBooking(state.rides), [state.rides]);
  const isLoading = useMemo(() => rideSelectors.isLoading(state.rides), [state.rides]);
  const error = useMemo(() => rideSelectors.error(state.rides), [state.rides]);
  
  // Computed selectors
  const urgentRides = useMemo(() => rideSelectors.getUrgentRides(state.rides), [state.rides]);
  const highValueRides = useMemo(() => rideSelectors.getHighValueRides(state.rides), [state.rides]);
  const allBookings = useMemo(() => rideSelectors.getAllBookings(state.rides), [state.rides]);
  const activeBookings = useMemo(() => rideSelectors.getActiveBookings(state.rides), [state.rides]);
  const scheduledBookings = useMemo(() => rideSelectors.getScheduledBookings(state.rides), [state.rides]);
  
  // Actions
  const setCurrentRide = useCallback((ride: Ride | null) => {
    dispatch({ type: 'rides/setCurrentRide', payload: ride });
  }, [dispatch]);
  
  const setRideStatus = useCallback((status: any) => {
    dispatch({ type: 'rides/setRideStatus', payload: status });
  }, [dispatch]);
  
  const addQueuedBooking = useCallback((booking: Ride) => {
    dispatch({ type: 'rides/addQueuedBooking', payload: booking });
  }, [dispatch]);
  
  const removeQueuedBooking = useCallback((bookingId: string) => {
    dispatch({ type: 'rides/removeQueuedBooking', payload: bookingId });
  }, [dispatch]);
  
  // Enhanced actions
  const acceptRideOptimistically = useCallback(async (rideId: string) => {
    const ride = availableRides.find(r => r.id === rideId);
    if (!ride) return false;
    
    // Optimistic update
    dispatch({ type: 'rides/removeAvailableRide', payload: rideId });
    
    if (!currentRide) {
      dispatch({ type: 'rides/setCurrentRide', payload: ride });
      dispatch({ type: 'rides/setRideStatus', payload: 'heading_to_pickup' });
    } else {
      dispatch({ type: 'rides/addQueuedBooking', payload: ride });
    }
    
    try {
      const success = await acceptRide(rideId);
      if (!success) {
        // Rollback on failure
        dispatch({ type: 'rides/addAvailableRide', payload: ride });
        if (!currentRide) {
          dispatch({ type: 'rides/setCurrentRide', payload: null });
          dispatch({ type: 'rides/setRideStatus', payload: null });
        } else {
          dispatch({ type: 'rides/removeQueuedBooking', payload: rideId });
        }
      }
      return success;
    } catch (error) {
      console.error('Failed to accept ride:', error);
      return false;
    }
  }, [availableRides, currentRide, dispatch, acceptRide]);
  
  const startNextQueuedRide = useCallback(() => {
    if (queuedBookings.length > 0) {
      const nextRide = queuedBookings[0];
      dispatch({ type: 'rides/setCurrentRide', payload: nextRide });
      dispatch({ type: 'rides/removeQueuedBooking', payload: nextRide.id });
      dispatch({ type: 'rides/setRideStatus', payload: 'heading_to_pickup' });
      dispatch({ type: 'rides/setQueueStatus', payload: 'limited_queue' });
      return nextRide;
    } else {
      dispatch({ type: 'rides/setQueueStatus', payload: 'available' });
      return null;
    }
  }, [queuedBookings, dispatch]);
  
  // Auto-refresh rides when online and no current ride
  useEffect(() => {
    if (state.driver.isOnline && !currentRide && !isLoading) {
      const interval = setInterval(() => {
        loadAvailableRides();
      }, 30000); // Refresh every 30 seconds
      
      return () => clearInterval(interval);
    }
  }, [state.driver.isOnline, currentRide, isLoading, loadAvailableRides]);
  
  // Ride analytics
  const rideAnalytics = useMemo(() => {
    const rides = availableRides;
    if (rides.length === 0) return null;
    
    const averageFare = rides.reduce((sum, ride) => sum + ride.fare, 0) / rides.length;
    const averageDistance = rides.reduce((sum, ride) => sum + ride.distance, 0) / rides.length;
    const farePerKm = averageFare / (averageDistance || 1);
    
    const rideTypes = rides.reduce((acc, ride) => {
      acc[ride.bookingStatus] = (acc[ride.bookingStatus] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);
    
    const priorities = rides.reduce((acc, ride) => {
      acc[ride.priority] = (acc[ride.priority] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);
    
    return {
      totalRides: rides.length,
      averageFare: Math.round(averageFare * 100) / 100,
      averageDistance: Math.round(averageDistance * 10) / 10,
      farePerKm: Math.round(farePerKm * 100) / 100,
      rideTypes,
      priorities,
      urgentCount: urgentRides.length,
      highValueCount: highValueRides.length,
    };
  }, [availableRides, urgentRides, highValueRides]);
  
  // Ride filters and sorts
  const filteredRides = useMemo(() => ({
    byDistance: (maxDistance: number) => availableRides.filter(ride => ride.distance <= maxDistance),
    byFare: (minFare: number) => availableRides.filter(ride => ride.fare >= minFare),
    byType: (type: Ride['bookingStatus']) => availableRides.filter(ride => ride.bookingStatus === type),
    byPriority: (priority: Ride['priority']) => availableRides.filter(ride => ride.priority === priority),
    nearby: (userLat: number, userLon: number, radiusKm: number = 5) => {
      return availableRides.filter(ride => {
        const distance = calculateDistance(
          userLat, userLon,
          ride.pickupLocation.latitude, ride.pickupLocation.longitude
        );
        return distance <= radiusKm;
      });
    },
  }), [availableRides]);
  
  const sortedRides = useMemo(() => ({
    byFare: (desc = true) => [...availableRides].sort((a, b) => 
      desc ? b.fare - a.fare : a.fare - b.fare
    ),
    byDistance: (desc = false) => [...availableRides].sort((a, b) => 
      desc ? b.distance - a.distance : a.distance - b.distance
    ),
    byTime: (desc = true) => [...availableRides].sort((a, b) => 
      desc ? b.createdAt.getTime() - a.createdAt.getTime() : a.createdAt.getTime() - b.createdAt.getTime()
    ),
    byPriority: () => [...availableRides].sort((a, b) => {
      const priorityOrder = { high: 3, medium: 2, low: 1 };
      return (priorityOrder[b.priority] || 0) - (priorityOrder[a.priority] || 0);
    }),
    smart: (userLat?: number, userLon?: number) => {
      return [...availableRides].sort((a, b) => {
        let scoreA = a.fare * 10; // Base score from fare
        let scoreB = b.fare * 10;
        
        // Priority bonus
        const priorityBonus = { high: 50, medium: 25, low: 0 };
        scoreA += priorityBonus[a.priority] || 0;
        scoreB += priorityBonus[b.priority] || 0;
        
        // Distance penalty (if location provided)
        if (userLat && userLon) {
          const distA = calculateDistance(userLat, userLon, a.pickupLocation.latitude, a.pickupLocation.longitude);
          const distB = calculateDistance(userLat, userLon, b.pickupLocation.latitude, b.pickupLocation.longitude);
          scoreA -= distA * 2; // Penalty for distance
          scoreB -= distB * 2;
        }
        
        // Time bonus for newer rides
        const ageA = Date.now() - a.createdAt.getTime();
        const ageB = Date.now() - b.createdAt.getTime();
        scoreA -= ageA / 60000; // Penalty for age in minutes
        scoreB -= ageB / 60000;
        
        return scoreB - scoreA;
      });
    },
  }), [availableRides]);
  
  return {
    // State
    currentRide,
    availableRides,
    queuedBookings,
    rideStatus,
    queueStatus,
    canAcceptNewBooking,
    isLoading,
    error,
    
    // Computed
    urgentRides,
    highValueRides,
    allBookings,
    activeBookings,
    scheduledBookings,
    rideAnalytics,
    
    // Filters and sorts
    filteredRides,
    sortedRides,
    
    // Actions
    loadAvailableRides,
    acceptRide: acceptRideOptimistically,
    rejectRide,
    completeRide,
    setCurrentRide,
    setRideStatus,
    addQueuedBooking,
    removeQueuedBooking,
    startNextQueuedRide,
    
    // Utilities
    shouldShowBookings: () => {
      if (!state.driver.isOnline) return false;
      if (!currentRide) return true;
      return rideStatus !== 'passenger_onboard' && rideStatus !== 'arrived_at_destination';
    },
    
    getRideById: (id: string) => allBookings.find(ride => ride.id === id),
    getEstimatedEarnings: () => availableRides.reduce((sum, ride) => sum + ride.fare, 0),
  };
};

// Helper function
function calculateDistance(lat1: number, lon1: number, lat2: number, lon2: number): number {
  const R = 6371; // Earth's radius in kilometers
  const dLat = (lat2 - lat1) * Math.PI / 180;
  const dLon = (lon2 - lon1) * Math.PI / 180;
  const a = 
    Math.sin(dLat / 2) * Math.sin(dLat / 2) +
    Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) * 
    Math.sin(dLon / 2) * Math.sin(dLon / 2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
  return R * c;
}