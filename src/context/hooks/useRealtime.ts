// Real-time Updates Hook
import { useCallback, useMemo, useEffect, useRef } from 'react';
import { useEnhancedState } from '../EnhancedStateContext';
import { webSocketService } from '../../services';

export const useRealtime = () => {
  const { state, dispatch, subscribeToUpdates } = useEnhancedState();
  const listenersRef = useRef<Set<string>>(new Set());
  
  // WebSocket connection status
  const connectionStatus = useMemo(() => ({
    isConnected: state.system.connection.isWebSocketConnected,
    isOnline: state.system.connection.isOnline,
    isApiConnected: state.system.connection.isApiConnected,
    retryCount: state.system.connection.retryCount,
    lastCheck: state.system.connection.lastConnectionCheck,
  }), [state.system.connection]);
  
  // Real-time event handlers
  const eventHandlers = useMemo(() => ({
    onNewRide: (callback: (ride: any) => void) => {
      const listenerId = `new_ride_${Date.now()}`;
      listenersRef.current.add(listenerId);
      
      // Subscribe to WebSocket events
      webSocketService.on('new_ride', callback);
      
      return () => {
        listenersRef.current.delete(listenerId);
        webSocketService.off('new_ride', callback);
      };
    },
    
    onRideUpdate: (callback: (rideUpdate: any) => void) => {
      const listenerId = `ride_update_${Date.now()}`;
      listenersRef.current.add(listenerId);
      
      webSocketService.on('ride_update', callback);
      
      return () => {
        listenersRef.current.delete(listenerId);
        webSocketService.off('ride_update', callback);
      };
    },
    
    onRideCancellation: (callback: (cancellation: any) => void) => {
      const listenerId = `ride_cancelled_${Date.now()}`;
      listenersRef.current.add(listenerId);
      
      webSocketService.on('ride_cancelled', callback);
      
      return () => {
        listenersRef.current.delete(listenerId);
        webSocketService.off('ride_cancelled', callback);
      };
    },
    
    onLocationUpdate: (callback: (location: any) => void) => {
      const listenerId = `location_update_${Date.now()}`;
      listenersRef.current.add(listenerId);
      
      webSocketService.on('location_update', callback);
      
      return () => {
        listenersRef.current.delete(listenerId);
        webSocketService.off('location_update', callback);
      };
    },
    
    onDriverStatusChange: (callback: (status: any) => void) => {
      const listenerId = `driver_status_${Date.now()}`;
      listenersRef.current.add(listenerId);
      
      webSocketService.on('driver_status_change', callback);
      
      return () => {
        listenersRef.current.delete(listenerId);
        webSocketService.off('driver_status_change', callback);
      };
    },
    
    onEarningsUpdate: (callback: (earnings: any) => void) => {
      const listenerId = `earnings_update_${Date.now()}`;
      listenersRef.current.add(listenerId);
      
      webSocketService.on('earnings_update', callback);
      
      return () => {
        listenersRef.current.delete(listenerId);
        webSocketService.off('earnings_update', callback);
      };
    },
    
    onSystemNotification: (callback: (notification: any) => void) => {
      const listenerId = `system_notification_${Date.now()}`;
      listenersRef.current.add(listenerId);
      
      webSocketService.on('system_notification', callback);
      
      return () => {
        listenersRef.current.delete(listenerId);
        webSocketService.off('system_notification', callback);
      };
    },
  }), []);
  
  // Connection management
  const connectionManager = useMemo(() => ({
    connect: async () => {
      if (state.auth.isAuthenticated && state.auth.driver) {
        try {
          await webSocketService.connect(state.auth.driver.id);
          dispatch({
            type: 'system/setConnectionState',
            payload: { isWebSocketConnected: true, retryCount: 0 },
          });
        } catch (error) {
          console.error('Failed to connect WebSocket:', error);
          dispatch({
            type: 'system/setConnectionState',
            payload: { isWebSocketConnected: false },
          });
        }
      }
    },
    
    disconnect: () => {
      webSocketService.disconnect();
      dispatch({
        type: 'system/setConnectionState',
        payload: { isWebSocketConnected: false },
      });
    },
    
    reconnect: async () => {
      await connectionManager.disconnect();
      setTimeout(() => {
        connectionManager.connect();
      }, 1000);
    },
    
    checkConnection: async () => {
      // Ping the server to check connection
      try {
        const isConnected = await webSocketService.ping();
        dispatch({
          type: 'system/setConnectionState',
          payload: {
            isWebSocketConnected: isConnected,
            lastConnectionCheck: new Date(),
          },
        });
        return isConnected;
      } catch (error) {
        dispatch({
          type: 'system/setConnectionState',
          payload: {
            isWebSocketConnected: false,
            lastConnectionCheck: new Date(),
            retryCount: state.system.connection.retryCount + 1,
          },
        });
        return false;
      }
    },
  }), [state.auth.isAuthenticated, state.auth.driver, dispatch, state.system.connection.retryCount]);
  
  // Auto-reconnection logic
  useEffect(() => {
    if (!connectionStatus.isConnected && state.auth.isAuthenticated && connectionStatus.isOnline) {
      const maxRetries = 5;
      const retryDelay = Math.min(1000 * Math.pow(2, connectionStatus.retryCount), 30000); // Exponential backoff, max 30s
      
      if (connectionStatus.retryCount < maxRetries) {
        const timer = setTimeout(() => {
          console.log(`Attempting to reconnect WebSocket (attempt ${connectionStatus.retryCount + 1})`);
          connectionManager.reconnect();
        }, retryDelay);
        
        return () => clearTimeout(timer);
      }
    }
  }, [connectionStatus, state.auth.isAuthenticated, connectionManager]);
  
  // Periodic connection health check
  useEffect(() => {
    if (connectionStatus.isConnected) {
      const interval = setInterval(() => {
        connectionManager.checkConnection();
      }, 30000); // Check every 30 seconds
      
      return () => clearInterval(interval);
    }
  }, [connectionStatus.isConnected, connectionManager]);
  
  // Real-time data synchronization
  const syncManager = useMemo(() => ({
    syncRides: () => {
      webSocketService.emit('sync_rides', {
        driverId: state.auth.driver?.id,
        location: state.driver.location,
      });
    },
    
    syncDriverStatus: () => {
      webSocketService.emit('sync_driver_status', {
        driverId: state.auth.driver?.id,
        isOnline: state.driver.isOnline,
        location: state.driver.location,
        currentRide: state.rides.currentRide?.id,
      });
    },
    
    syncLocation: () => {
      if (state.driver.location && state.auth.driver) {
        webSocketService.emit('location_update', {
          driverId: state.auth.driver.id,
          location: state.driver.location,
          timestamp: new Date().toISOString(),
        });
      }
    },
    
    requestDataSync: () => {
      webSocketService.emit('request_sync', {
        driverId: state.auth.driver?.id,
        types: ['rides', 'earnings', 'trips'],
      });
    },
  }), [state.auth.driver, state.driver.location, state.driver.isOnline, state.rides.currentRide]);
  
  // Message queue for offline handling
  const messageQueue = useRef<any[]>([]);
  
  const queueManager = useMemo(() => ({
    addToQueue: (message: any) => {
      messageQueue.current.push({
        ...message,
        timestamp: Date.now(),
      });
    },
    
    processQueue: async () => {
      if (connectionStatus.isConnected && messageQueue.current.length > 0) {
        const messages = [...messageQueue.current];
        messageQueue.current = [];
        
        for (const message of messages) {
          try {
            webSocketService.emit(message.type, message.payload);
          } catch (error) {
            console.error('Failed to process queued message:', error);
            // Re-queue failed messages
            messageQueue.current.push(message);
          }
        }
      }
    },
    
    clearQueue: () => {
      messageQueue.current = [];
    },
    
    getQueueLength: () => messageQueue.current.length,
  }), [connectionStatus.isConnected]);
  
  // Process queue when connection is restored
  useEffect(() => {
    if (connectionStatus.isConnected) {
      queueManager.processQueue();
    }
  }, [connectionStatus.isConnected, queueManager]);
  
  // Cleanup listeners on unmount
  useEffect(() => {
    return () => {
      listenersRef.current.forEach(listenerId => {
        console.log('Cleaning up WebSocket listener:', listenerId);
      });
      listenersRef.current.clear();
    };
  }, []);
  
  // Real-time status indicators
  const statusIndicators = useMemo(() => ({
    connectionQuality: () => {
      if (!connectionStatus.isOnline) return 'offline';
      if (!connectionStatus.isConnected) return 'disconnected';
      if (connectionStatus.retryCount > 0) return 'unstable';
      return 'excellent';
    },
    
    getStatusColor: () => {
      switch (statusIndicators.connectionQuality()) {
        case 'excellent': return '#4CAF50';
        case 'unstable': return '#FF9800';
        case 'disconnected': return '#F44336';
        case 'offline': return '#9E9E9E';
        default: return '#9E9E9E';
      }
    },
    
    getStatusText: () => {
      switch (statusIndicators.connectionQuality()) {
        case 'excellent': return 'Connected';
        case 'unstable': return 'Reconnecting...';
        case 'disconnected': return 'Disconnected';
        case 'offline': return 'Offline';
        default: return 'Unknown';
      }
    },
    
    shouldShowConnectionWarning: () => {
      return !connectionStatus.isConnected || connectionStatus.retryCount > 2;
    },
  }), [connectionStatus]);
  
  return {
    // Connection status
    connectionStatus,
    statusIndicators,
    
    // Event handlers
    eventHandlers,
    
    // Connection management
    connectionManager,
    
    // Data synchronization
    syncManager,
    
    // Message queue management
    queueManager,
    queueLength: queueManager.getQueueLength(),
    
    // Utilities
    isRealTimeEnabled: connectionStatus.isConnected,
    
    emit: (event: string, data: any) => {
      if (connectionStatus.isConnected) {
        webSocketService.emit(event, data);
      } else {
        queueManager.addToQueue({ type: event, payload: data });
      }
    },
    
    // State subscription
    subscribeToStateChanges: (callback: (state: any) => void) => {
      return subscribeToUpdates(callback);
    },
    
    // Formatted utilities
    formatConnectionTime: () => {
      if (!connectionStatus.lastCheck) return 'Never';
      const diff = Date.now() - connectionStatus.lastCheck.getTime();
      const seconds = Math.floor(diff / 1000);
      const minutes = Math.floor(seconds / 60);
      const hours = Math.floor(minutes / 60);
      
      if (hours > 0) return `${hours}h ago`;
      if (minutes > 0) return `${minutes}m ago`;
      return `${seconds}s ago`;
    },
    
    getRetryCountText: () => {
      return connectionStatus.retryCount > 0 
        ? `(${connectionStatus.retryCount} retries)` 
        : '';
    },
  };
};