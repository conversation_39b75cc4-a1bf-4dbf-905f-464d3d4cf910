// Authentication Hook
import { useCallback, useMemo } from 'react';
import { useEnhancedState } from '../EnhancedStateContext';
import { authSelectors } from '../slices/authSlice';

export const useAuth = () => {
  const { state, dispatch, login, logout, refreshAuth } = useEnhancedState();
  
  // Memoized selectors
  const authState = useMemo(() => authSelectors.isAuthenticated(state.auth), [state.auth]);
  const driver = useMemo(() => authSelectors.driver(state.auth), [state.auth]);
  const token = useMemo(() => authSelectors.token(state.auth), [state.auth]);
  const isLoading = useMemo(() => authSelectors.isLoading(state.auth), [state.auth]);
  const error = useMemo(() => authSelectors.error(state.auth), [state.auth]);
  const isSessionExpired = useMemo(() => authSelectors.isSessionExpired(state.auth), [state.auth]);
  const timeUntilExpiry = useMemo(() => authSelectors.timeUntilExpiry(state.auth), [state.auth]);
  
  // Actions
  const updateDriver = useCallback((driver: any) => {
    dispatch({ type: 'auth/updateDriver', payload: driver });
  }, [dispatch]);
  
  const setLoading = useCallback((loading: boolean) => {
    dispatch({ type: 'auth/setLoading', payload: loading });
  }, [dispatch]);
  
  const setError = useCallback((error: string | null) => {
    dispatch({ type: 'auth/setError', payload: error });
  }, [dispatch]);
  
  // Computed values
  const sessionStatus = useMemo(() => {
    if (!authState) return 'unauthenticated';
    if (isSessionExpired) return 'expired';
    if (timeUntilExpiry < 5 * 60 * 1000) return 'expiring_soon'; // 5 minutes
    return 'active';
  }, [authState, isSessionExpired, timeUntilExpiry]);
  
  const driverProfile = useMemo(() => {
    if (!driver) return null;
    
    return {
      ...driver,
      initials: driver.name.split(' ').map(n => n[0]).join('').toUpperCase(),
      hasProfilePhoto: !!driver.profilePhoto,
      vehicleInfo: `${driver.vehicleType} - ${driver.licensePlate}`,
    };
  }, [driver]);
  
  return {
    // State
    isAuthenticated: authState,
    driver: driverProfile,
    token,
    isLoading,
    error,
    sessionStatus,
    timeUntilExpiry,
    
    // Actions
    login,
    logout,
    refreshAuth,
    updateDriver,
    setLoading,
    setError,
    
    // Computed
    needsSessionRefresh: sessionStatus === 'expiring_soon',
    isSessionValid: sessionStatus === 'active',
  };
};