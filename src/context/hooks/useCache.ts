// Cache Management Hook
import { useCallback, useMemo } from 'react';
import { useEnhancedState } from '../EnhancedStateContext';
import { cacheManager, rideCacheManager, tripCacheManager, driverCacheManager, earningsCacheManager } from '../cache/CacheManager';

export const useCache = () => {
  const { getCacheStats, invalidateCache, prefetchData } = useEnhancedState();
  
  // Cache statistics
  const cacheStats = useMemo(() => getCacheStats(), [getCacheStats]);
  
  // Cache operations
  const operations = useMemo(() => ({
    // General cache operations
    clear: async (pattern?: string) => {
      await invalidateCache(pattern);
    },
    
    clearAll: async () => {
      await Promise.all([
        cacheManager.clear(),
        rideCacheManager.clear(),
        tripCacheManager.clear(),
        driverCacheManager.clear(),
        earningsCacheManager.clear(),
      ]);
    },
    
    // Domain-specific cache operations
    rides: {
      clear: () => rideCacheManager.clear(),
      invalidate: (pattern: string) => rideCacheManager.invalidatePattern(pattern),
      get: <T>(key: string) => rideCacheManager.get<T>(key),
      set: <T>(key: string, data: T, options?: any) => rideCacheManager.set(key, data, options),
      has: (key: string) => rideCacheManager.has(key),
      isStale: (key: string, maxAge?: number) => rideCacheManager.isStale(key, maxAge),
    },
    
    trips: {
      clear: () => tripCacheManager.clear(),
      invalidate: (pattern: string) => tripCacheManager.invalidatePattern(pattern),
      get: <T>(key: string) => tripCacheManager.get<T>(key),
      set: <T>(key: string, data: T, options?: any) => tripCacheManager.set(key, data, options),
      has: (key: string) => tripCacheManager.has(key),
      isStale: (key: string, maxAge?: number) => tripCacheManager.isStale(key, maxAge),
    },
    
    driver: {
      clear: () => driverCacheManager.clear(),
      invalidate: (pattern: string) => driverCacheManager.invalidatePattern(pattern),
      get: <T>(key: string) => driverCacheManager.get<T>(key),
      set: <T>(key: string, data: T, options?: any) => driverCacheManager.set(key, data, options),
      has: (key: string) => driverCacheManager.has(key),
      isStale: (key: string, maxAge?: number) => driverCacheManager.isStale(key, maxAge),
    },
    
    earnings: {
      clear: () => earningsCacheManager.clear(),
      invalidate: (pattern: string) => earningsCacheManager.invalidatePattern(pattern),
      get: <T>(key: string) => earningsCacheManager.get<T>(key),
      set: <T>(key: string, data: T, options?: any) => earningsCacheManager.set(key, data, options),
      has: (key: string) => earningsCacheManager.has(key),
      isStale: (key: string, maxAge?: number) => earningsCacheManager.isStale(key, maxAge),
    },
  }), [invalidateCache]);
  
  // Smart cache strategies
  const strategies = useMemo(() => ({
    // Preload critical data
    preloadCritical: async (driverId: string) => {
      const criticalKeys = [
        `driver:profile:${driverId}`,
        `trips:recent:${driverId}`,
        `earnings:summary:${driverId}`,
      ];
      
      await Promise.allSettled(
        criticalKeys.map(key => {
          // This would trigger data loading with caching
          return prefetchData([key]);
        })
      );
    },
    
    // Warmup cache for expected data
    warmupForOnlineMode: async (driverId: string, location: { lat: number; lon: number }) => {
      const keys = [
        `rides:available:${driverId}:${location.lat}:${location.lon}`,
        `driver:nearby:${location.lat}:${location.lon}`,
      ];
      
      await prefetchData(keys);
    },
    
    // Smart invalidation based on actions
    smartInvalidate: async (action: string, payload?: any) => {
      switch (action) {
        case 'ride_accepted':
          await operations.rides.invalidate('available');
          break;
        case 'trip_completed':
          await Promise.all([
            operations.trips.invalidate('recent'),
            operations.earnings.invalidate('summary'),
            operations.rides.invalidate('available'),
          ]);
          break;
        case 'location_updated':
          await operations.rides.invalidate('available');
          break;
        case 'profile_updated':
          await operations.driver.invalidate('profile');
          break;
        default:
          console.log('No cache invalidation strategy for action:', action);
      }
    },
    
    // Background refresh stale data
    refreshStaleData: async () => {
      const staleChecks = [
        { manager: rideCacheManager, pattern: 'rides:available', maxAge: 2 * 60 * 1000 },
        { manager: tripCacheManager, pattern: 'trips:recent', maxAge: 10 * 60 * 1000 },
        { manager: earningsCacheManager, pattern: 'earnings:summary', maxAge: 5 * 60 * 1000 },
        { manager: driverCacheManager, pattern: 'driver:profile', maxAge: 30 * 60 * 1000 },
      ];
      
      // This would check for stale data and refresh in background
      staleChecks.forEach(({ manager, pattern, maxAge }) => {
        // Implementation would depend on the specific cache manager
        console.log(`Checking stale data for pattern: ${pattern}`);
      });
    },
  }), [operations, prefetchData]);
  
  // Cache health monitoring
  const health = useMemo(() => {
    const totalStats = cacheStats;
    const totalMemory = Object.values(totalStats).reduce((sum: number, stat: any) => 
      sum + (stat.memoryUsage || 0), 0);
    
    const totalHitRate = Object.values(totalStats).reduce((sum: number, stat: any) => 
      sum + (stat.hitRate || 0), 0) / Object.keys(totalStats).length;
    
    const isHealthy = totalMemory < 50 * 1024 * 1024 && totalHitRate > 0.7; // 50MB and 70% hit rate
    
    return {
      isHealthy,
      totalMemoryUsage: totalMemory,
      averageHitRate: totalHitRate,
      recommendations: [] as string[],
    };
  }, [cacheStats]);
  
  // Add health recommendations
  useMemo(() => {
    health.recommendations = [];
    
    if (health.totalMemoryUsage > 50 * 1024 * 1024) {
      health.recommendations.push('Consider reducing cache size or clearing old entries');
    }
    
    if (health.averageHitRate < 0.5) {
      health.recommendations.push('Cache hit rate is low, review caching strategies');
    }
    
    Object.entries(cacheStats).forEach(([name, stats]: [string, any]) => {
      if (stats.expiredEntries > 10) {
        health.recommendations.push(`Clean up expired entries in ${name} cache`);
      }
    });
  }, [health, cacheStats]);
  
  // Cache debugging utilities
  const debug = useMemo(() => ({
    inspectCache: (cacheName: 'general' | 'rides' | 'trips' | 'driver' | 'earnings') => {
      return cacheStats[cacheName];
    },
    
    findLargestEntries: () => {
      // This would analyze cache entries by size
      return Object.entries(cacheStats).map(([name, stats]: [string, any]) => ({
        cache: name,
        memoryUsage: stats.memoryUsage,
        entryCount: stats.size,
      })).sort((a, b) => b.memoryUsage - a.memoryUsage);
    },
    
    getCacheTimeline: () => {
      // This would show cache operations over time
      return {
        hits: [],
        misses: [],
        sets: [],
        evictions: [],
      };
    },
    
    exportCacheData: () => {
      return JSON.stringify({
        stats: cacheStats,
        health: health,
        timestamp: new Date().toISOString(),
      }, null, 2);
    },
  }), [cacheStats, health]);
  
  // Auto-cleanup and optimization
  const optimization = useMemo(() => ({
    autoCleanup: () => {
      // This would run automatically
      console.log('Running cache auto-cleanup');
    },
    
    optimizeMemoryUsage: async () => {
      if (health.totalMemoryUsage > 40 * 1024 * 1024) { // 40MB threshold
        // Clear least used caches first
        const cachesByUsage = debug.findLargestEntries();
        const targetCache = cachesByUsage[0];
        
        if (targetCache) {
          await operations[targetCache.cache as keyof typeof operations]?.clear();
          console.log(`Cleared ${targetCache.cache} cache to optimize memory`);
        }
      }
    },
    
    balanceLoadAcrossCaches: () => {
      // This would rebalance cache sizes based on usage patterns
      console.log('Balancing cache load');
    },
    
    predictAndPreload: async (userBehaviorPattern: string) => {
      // Based on user behavior, preload likely needed data
      switch (userBehaviorPattern) {
        case 'going_online':
          await strategies.warmupForOnlineMode('current_driver_id', { lat: 0, lon: 0 });
          break;
        case 'checking_earnings':
          // Preload earnings data
          break;
        case 'looking_for_rides':
          // Preload available rides
          break;
      }
    },
  }), [health, debug, operations, strategies]);
  
  return {
    // Statistics
    stats: cacheStats,
    health,
    
    // Operations
    operations,
    strategies,
    debug,
    optimization,
    
    // Quick utilities
    clearCache: operations.clear,
    clearAllCaches: operations.clearAll,
    
    // Health checks
    isHealthy: health.isHealthy,
    memoryUsage: health.totalMemoryUsage,
    hitRate: health.averageHitRate,
    
    // Formatted utilities
    formatMemoryUsage: (bytes: number) => {
      const mb = bytes / (1024 * 1024);
      return `${mb.toFixed(1)} MB`;
    },
    
    formatHitRate: (rate: number) => {
      return `${(rate * 100).toFixed(1)}%`;
    },
    
    // Cache status indicators
    getCacheStatusColor: (cacheName: string) => {
      const stats = cacheStats[cacheName as keyof typeof cacheStats] as any;
      if (!stats) return '#9E9E9E';
      
      if (stats.hitRate > 0.8) return '#4CAF50'; // Green
      if (stats.hitRate > 0.6) return '#FF9800'; // Orange
      return '#F44336'; // Red
    },
    
    shouldShowCacheWarning: () => {
      return !health.isHealthy || health.recommendations.length > 0;
    },
  };
};