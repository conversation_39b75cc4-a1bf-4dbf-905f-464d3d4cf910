// UI State Management Hook
import { useCallback, useMemo } from 'react';
import { useEnhancedState } from '../EnhancedStateContext';
import { uiSelectors } from '../slices/uiSlice';

export const useUI = () => {
  const { state, dispatch } = useEnhancedState();
  
  // Memoized selectors
  const sidebarOpen = useMemo(() => uiSelectors.sidebarOpen(state.ui), [state.ui]);
  const navigationVisible = useMemo(() => uiSelectors.navigationVisible(state.ui), [state.ui]);
  const activeScreen = useMemo(() => uiSelectors.activeScreen(state.ui), [state.ui]);
  const mapRegion = useMemo(() => uiSelectors.mapRegion(state.ui), [state.ui]);
  const theme = useMemo(() => uiSelectors.theme(state.ui), [state.ui]);
  const notifications = useMemo(() => uiSelectors.notifications(state.ui), [state.ui]);
  
  // Computed selectors
  const isDarkMode = useMemo(() => uiSelectors.isDarkMode(state.ui), [state.ui]);
  const isLightMode = useMemo(() => uiSelectors.isLightMode(state.ui), [state.ui]);
  const shouldShowNavigation = useMemo(() => uiSelectors.shouldShowNavigation(state.ui), [state.ui]);
  const canReceiveNotifications = useMemo(() => uiSelectors.canReceiveNotifications(state.ui), [state.ui]);
  const notificationSettings = useMemo(() => uiSelectors.notificationSettings(state.ui), [state.ui]);
  const mapCenter = useMemo(() => uiSelectors.getMapCenter(state.ui), [state.ui]);
  const mapZoomLevel = useMemo(() => uiSelectors.getMapZoomLevel(state.ui), [state.ui]);
  
  const isActiveScreen = useCallback((screenName: string) => 
    uiSelectors.isActiveScreen(screenName)(state.ui), [state.ui]);
  
  // Actions
  const toggleSidebar = useCallback((open?: boolean) => {
    dispatch({ type: 'ui/toggleSidebar', payload: open });
  }, [dispatch]);
  
  const setNavigationVisibility = useCallback((visible: boolean) => {
    dispatch({ type: 'ui/setNavigationVisibility', payload: visible });
  }, [dispatch]);
  
  const setActiveScreen = useCallback((screenName: string) => {
    dispatch({ type: 'ui/setActiveScreen', payload: screenName });
  }, [dispatch]);
  
  const setMapRegion = useCallback((region: any) => {
    dispatch({ type: 'ui/setMapRegion', payload: region });
  }, [dispatch]);
  
  const setTheme = useCallback((newTheme: 'light' | 'dark') => {
    dispatch({ type: 'ui/setTheme', payload: newTheme });
  }, [dispatch]);
  
  const toggleTheme = useCallback(() => {
    const newTheme = isDarkMode ? 'light' : 'dark';
    setTheme(newTheme);
  }, [isDarkMode, setTheme]);
  
  const updateNotificationSettings = useCallback((settings: any) => {
    dispatch({ type: 'ui/updateNotificationSettings', payload: settings });
  }, [dispatch]);
  
  // Enhanced UI utilities
  const screenManager = useMemo(() => ({
    navigate: (screenName: string) => {
      setActiveScreen(screenName);
    },
    
    goBack: () => {
      // This would integrate with navigation history
      console.log('Navigate back');
    },
    
    isModal: (screenName: string) => {
      const modalScreens = ['ProfileModal', 'SettingsModal', 'HelpModal'];
      return modalScreens.includes(screenName);
    },
    
    canNavigate: (screenName: string) => {
      // Check if navigation is allowed based on current state
      if (state.rides.currentRide && state.rides.rideStatus === 'passenger_onboard') {
        const allowedScreens = ['ActiveRide', 'Navigation', 'Emergency'];
        return allowedScreens.includes(screenName);
      }
      return true;
    },
    
    getScreenTitle: (screenName: string) => {
      const titles: Record<string, string> = {
        'Dashboard': 'Dashboard',
        'ActiveRide': 'Active Ride',
        'TripHistory': 'Trip History',
        'Earnings': 'Earnings',
        'Profile': 'Profile',
        'Settings': 'Settings',
        'BookingsList': 'Available Rides',
        'Map': 'Map View',
        'Help': 'Help & Support',
      };
      return titles[screenName] || screenName;
    },
  }), [setActiveScreen, state.rides.currentRide, state.rides.rideStatus]);
  
  const mapManager = useMemo(() => ({
    centerOnDriver: () => {
      if (state.driver.location) {
        setMapRegion({
          latitude: state.driver.location.latitude,
          longitude: state.driver.location.longitude,
          latitudeDelta: 0.01,
          longitudeDelta: 0.01,
        });
      }
    },
    
    centerOnRide: () => {
      if (state.rides.currentRide) {
        const { pickupLocation } = state.rides.currentRide;
        setMapRegion({
          latitude: pickupLocation.latitude,
          longitude: pickupLocation.longitude,
          latitudeDelta: 0.01,
          longitudeDelta: 0.01,
        });
      }
    },
    
    fitRideRoute: () => {
      if (state.rides.currentRide && state.driver.location) {
        const { pickupLocation, dropoffLocation } = state.rides.currentRide;
        const driverLocation = state.driver.location;
        
        // Calculate bounds for all three points
        const lats = [driverLocation.latitude, pickupLocation.latitude, dropoffLocation.latitude];
        const lons = [driverLocation.longitude, pickupLocation.longitude, dropoffLocation.longitude];
        
        const minLat = Math.min(...lats);
        const maxLat = Math.max(...lats);
        const minLon = Math.min(...lons);
        const maxLon = Math.max(...lons);
        
        const latDelta = (maxLat - minLat) * 1.5; // Add 50% padding
        const lonDelta = (maxLon - minLon) * 1.5;
        
        setMapRegion({
          latitude: (minLat + maxLat) / 2,
          longitude: (minLon + maxLon) / 2,
          latitudeDelta: Math.max(latDelta, 0.01),
          longitudeDelta: Math.max(lonDelta, 0.01),
        });
      }
    },
    
    zoomIn: () => {
      setMapRegion({
        ...mapRegion,
        latitudeDelta: mapRegion.latitudeDelta * 0.5,
        longitudeDelta: mapRegion.longitudeDelta * 0.5,
      });
    },
    
    zoomOut: () => {
      setMapRegion({
        ...mapRegion,
        latitudeDelta: mapRegion.latitudeDelta * 2,
        longitudeDelta: mapRegion.longitudeDelta * 2,
      });
    },
  }), [state.driver.location, state.rides.currentRide, mapRegion, setMapRegion]);
  
  const themeConfig = useMemo(() => {
    const baseTheme = {
      colors: {
        primary: '#007AFF',
        secondary: '#5AC8FA',
        success: '#4CD964',
        warning: '#FF9500',
        error: '#FF3B30',
        info: '#5AC8FA',
      },
      spacing: {
        xs: 4,
        sm: 8,
        md: 16,
        lg: 24,
        xl: 32,
        xxl: 40,
      },
      borderRadius: {
        sm: 4,
        md: 8,
        lg: 12,
        xl: 16,
      },
      shadows: {
        sm: {
          shadowOffset: { width: 0, height: 1 },
          shadowOpacity: 0.1,
          shadowRadius: 3,
          elevation: 2,
        },
        md: {
          shadowOffset: { width: 0, height: 2 },
          shadowOpacity: 0.15,
          shadowRadius: 5,
          elevation: 4,
        },
        lg: {
          shadowOffset: { width: 0, height: 4 },
          shadowOpacity: 0.2,
          shadowRadius: 8,
          elevation: 6,
        },
      },
    };
    
    if (isDarkMode) {
      return {
        ...baseTheme,
        colors: {
          ...baseTheme.colors,
          background: '#000000',
          surface: '#1C1C1E',
          text: '#FFFFFF',
          textSecondary: '#8E8E93',
          border: '#38383A',
          card: '#2C2C2E',
        },
      };
    } else {
      return {
        ...baseTheme,
        colors: {
          ...baseTheme.colors,
          background: '#FFFFFF',
          surface: '#F2F2F7',
          text: '#000000',
          textSecondary: '#8E8E93',
          border: '#E5E5EA',
          card: '#FFFFFF',
        },
      };
    }
  }, [isDarkMode]);
  
  const notificationManager = useMemo(() => ({
    canShowNotification: (type: 'sound' | 'vibration' | 'visual') => {
      return notificationSettings[type];
    },
    
    requestPermissions: async () => {
      // This would request notification permissions
      return true;
    },
    
    scheduleNotification: (notification: any) => {
      // Schedule a notification
      console.log('Schedule notification:', notification);
    },
    
    clearNotifications: () => {
      // Clear all notifications
      console.log('Clear notifications');
    },
    
    updateBadge: (count: number) => {
      // Update app badge count
      console.log('Update badge:', count);
    },
  }), [notificationSettings]);
  
  const layoutManager = useMemo(() => ({
    getScreenPadding: () => {
      return {
        top: shouldShowNavigation ? 0 : themeConfig.spacing.md,
        bottom: themeConfig.spacing.md,
        left: themeConfig.spacing.md,
        right: themeConfig.spacing.md,
      };
    },
    
    getSafeAreaInsets: () => {
      // This would get actual safe area insets
      return {
        top: 44,
        bottom: 34,
        left: 0,
        right: 0,
      };
    },
    
    isTabletLayout: () => {
      // Check if should use tablet layout
      return false; // Would check actual device dimensions
    },
    
    getGridColumns: () => {
      return 2; // Would vary based on screen size
    },
  }), [shouldShowNavigation, themeConfig]);
  
  return {
    // State
    sidebarOpen,
    navigationVisible,
    activeScreen,
    mapRegion,
    theme,
    notifications,
    
    // Computed
    isDarkMode,
    isLightMode,
    shouldShowNavigation,
    canReceiveNotifications,
    notificationSettings,
    mapCenter,
    mapZoomLevel,
    themeConfig,
    
    // Functions
    isActiveScreen,
    
    // Actions
    toggleSidebar,
    setNavigationVisibility,
    setActiveScreen,
    setMapRegion,
    setTheme,
    toggleTheme,
    updateNotificationSettings,
    
    // Managers
    screenManager,
    mapManager,
    notificationManager,
    layoutManager,
    
    // Utilities
    formatScreenName: (name: string) => screenManager.getScreenTitle(name),
    
    getThemeColor: (colorName: keyof typeof themeConfig.colors) => 
      themeConfig.colors[colorName],
    
    getSpacing: (size: keyof typeof themeConfig.spacing) => 
      themeConfig.spacing[size],
    
    getShadow: (size: keyof typeof themeConfig.shadows) => 
      themeConfig.shadows[size],
    
    shouldUseDarkStatusBar: () => isDarkMode,
    
    getStatusBarStyle: () => isDarkMode ? 'light-content' : 'dark-content',
  };
};