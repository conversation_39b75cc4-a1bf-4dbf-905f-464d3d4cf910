// Earnings and Trip History Hook
import { useCallback, useMemo, useEffect } from 'react';
import { useEnhancedState } from '../EnhancedStateContext';
import { earningsSelectors } from '../slices/earningsSlice';
import { Trip } from '../../types';

export const useEarnings = () => {
  const { state, dispatch, loadTripHistory, loadEarningsData, addTripOptimistically } = useEnhancedState();
  
  // Memoized selectors
  const totalEarnings = useMemo(() => earningsSelectors.totalEarnings(state.earnings), [state.earnings]);
  const weeklyEarnings = useMemo(() => earningsSelectors.weeklyEarnings(state.earnings), [state.earnings]);
  const dailyEarnings = useMemo(() => earningsSelectors.dailyEarnings(state.earnings), [state.earnings]);
  const todayTrips = useMemo(() => earningsSelectors.todayTrips(state.earnings), [state.earnings]);
  const trips = useMemo(() => earningsSelectors.trips(state.earnings), [state.earnings]);
  const earnings = useMemo(() => earningsSelectors.earnings(state.earnings), [state.earnings]);
  const isLoading = useMemo(() => earningsSelectors.isLoading(state.earnings), [state.earnings]);
  const error = useMemo(() => earningsSelectors.error(state.earnings), [state.earnings]);
  
  // Computed selectors
  const averageEarningsPerTrip = useMemo(() => earningsSelectors.averageEarningsPerTrip(state.earnings), [state.earnings]);
  const todayStats = useMemo(() => earningsSelectors.todayStats(state.earnings), [state.earnings]);
  const weeklyStats = useMemo(() => earningsSelectors.weeklyStats(state.earnings), [state.earnings]);
  const monthlyStats = useMemo(() => earningsSelectors.monthlyStats(state.earnings), [state.earnings]);
  const earningsGrowth = useMemo(() => earningsSelectors.earningsGrowth(state.earnings), [state.earnings]);
  
  const earningsTrend = useCallback((period: 'week' | 'month' = 'week') => 
    earningsSelectors.earningsTrend(period)(state.earnings), [state.earnings]);
  
  const topEarningTrips = useCallback((limit: number = 10) => 
    earningsSelectors.topEarningTrips(limit)(state.earnings), [state.earnings]);
  
  const recentTrips = useCallback((limit: number = 10) => 
    earningsSelectors.recentTrips(limit)(state.earnings), [state.earnings]);
  
  // Actions
  const addTrip = useCallback((trip: Trip) => {
    dispatch({ type: 'earnings/addTrip', payload: trip });
  }, [dispatch]);
  
  const updateEarnings = useCallback((earningsUpdate: any) => {
    dispatch({ type: 'earnings/updateEarnings', payload: earningsUpdate });
  }, [dispatch]);
  
  const setLoading = useCallback((loading: boolean) => {
    dispatch({ type: 'earnings/setLoading', payload: loading });
  }, [dispatch]);
  
  const setError = useCallback((error: string | null) => {
    dispatch({ type: 'earnings/setError', payload: error });
  }, [dispatch]);
  
  // Enhanced analytics
  const earningsAnalytics = useMemo(() => {
    if (trips.length === 0) {
      return {
        totalTrips: 0,
        totalEarnings: 0,
        averagePerTrip: 0,
        totalDistance: 0,
        totalDuration: 0,
        averageDistance: 0,
        averageDuration: 0,
        efficiency: 0,
        bestDay: null,
        bestWeek: null,
        busyHours: [],
        earningsDistribution: [],
      };
    }
    
    const totalDistance = trips.reduce((sum, trip) => sum + trip.distance, 0);
    const totalDuration = trips.reduce((sum, trip) => sum + trip.duration, 0);
    const averageDistance = totalDistance / trips.length;
    const averageDuration = totalDuration / trips.length;
    const efficiency = totalEarnings / (totalDuration / 60); // Earnings per hour
    
    // Find best performing day
    const dailyEarnings = trips.reduce((acc, trip) => {
      const day = trip.completedAt.toDateString();
      acc[day] = (acc[day] || 0) + trip.earnings;
      return acc;
    }, {} as Record<string, number>);
    
    const bestDay = Object.entries(dailyEarnings)
      .sort(([, a], [, b]) => b - a)[0];
    
    // Busy hours analysis
    const hourlyEarnings = trips.reduce((acc, trip) => {
      const hour = trip.completedAt.getHours();
      acc[hour] = (acc[hour] || 0) + trip.earnings;
      return acc;
    }, {} as Record<number, number>);
    
    const busyHours = Object.entries(hourlyEarnings)
      .sort(([, a], [, b]) => b - a)
      .slice(0, 5)
      .map(([hour, earnings]) => ({ hour: parseInt(hour), earnings }));
    
    // Earnings distribution
    const earningsDistribution = [
      { range: '£0-20', count: trips.filter(t => t.earnings < 20).length },
      { range: '£20-40', count: trips.filter(t => t.earnings >= 20 && t.earnings < 40).length },
      { range: '£40-60', count: trips.filter(t => t.earnings >= 40 && t.earnings < 60).length },
      { range: '£60-80', count: trips.filter(t => t.earnings >= 60 && t.earnings < 80).length },
      { range: '£80+', count: trips.filter(t => t.earnings >= 80).length },
    ];
    
    return {
      totalTrips: trips.length,
      totalEarnings,
      averagePerTrip: averageEarningsPerTrip,
      totalDistance: Math.round(totalDistance * 10) / 10,
      totalDuration: Math.round(totalDuration),
      averageDistance: Math.round(averageDistance * 10) / 10,
      averageDuration: Math.round(averageDuration),
      efficiency: Math.round(efficiency * 100) / 100,
      bestDay: bestDay ? { date: bestDay[0], earnings: bestDay[1] } : null,
      busyHours,
      earningsDistribution,
    };
  }, [trips, totalEarnings, averageEarningsPerTrip]);
  
  // Goal tracking
  const goalTracking = useMemo(() => {
    const dailyGoal = 200; // £200 per day
    const weeklyGoal = 1000; // £1000 per week
    const monthlyGoal = 4000; // £4000 per month
    
    return {
      daily: {
        goal: dailyGoal,
        current: todayStats.earnings,
        percentage: Math.round((todayStats.earnings / dailyGoal) * 100),
        remaining: Math.max(0, dailyGoal - todayStats.earnings),
      },
      weekly: {
        goal: weeklyGoal,
        current: weeklyStats.earnings,
        percentage: Math.round((weeklyStats.earnings / weeklyGoal) * 100),
        remaining: Math.max(0, weeklyGoal - weeklyStats.earnings),
      },
      monthly: {
        goal: monthlyGoal,
        current: monthlyStats.earnings,
        percentage: Math.round((monthlyStats.earnings / monthlyGoal) * 100),
        remaining: Math.max(0, monthlyGoal - monthlyStats.earnings),
      },
    };
  }, [todayStats, weeklyStats, monthlyStats]);
  
  // Predictions
  const predictions = useMemo(() => {
    if (trips.length < 7) return null; // Need at least a week of data
    
    const recentTrips = trips.filter(trip => {
      const daysDiff = (Date.now() - trip.completedAt.getTime()) / (1000 * 60 * 60 * 24);
      return daysDiff <= 7;
    });
    
    if (recentTrips.length === 0) return null;
    
    const avgDailyEarnings = recentTrips.reduce((sum, trip) => sum + trip.earnings, 0) / 7;
    const avgDailyTrips = recentTrips.length / 7;
    
    return {
      projectedWeeklyEarnings: Math.round(avgDailyEarnings * 7),
      projectedMonthlyEarnings: Math.round(avgDailyEarnings * 30),
      projectedTripsToday: Math.round(avgDailyTrips),
      projectedEarningsToday: Math.round(avgDailyEarnings),
      trendDirection: earningsGrowth > 0 ? 'up' : earningsGrowth < 0 ? 'down' : 'stable',
    };
  }, [trips, earningsGrowth]);
  
  // Trip filters and analysis
  const tripFilters = useMemo(() => ({
    byDateRange: (startDate: Date, endDate: Date) => 
      trips.filter(trip => trip.completedAt >= startDate && trip.completedAt <= endDate),
      
    byEarningsRange: (minEarnings: number, maxEarnings: number) => 
      trips.filter(trip => trip.earnings >= minEarnings && trip.earnings <= maxEarnings),
      
    byDistance: (minDistance: number, maxDistance: number) => 
      trips.filter(trip => trip.distance >= minDistance && trip.distance <= maxDistance),
      
    byDuration: (minDuration: number, maxDuration: number) => 
      trips.filter(trip => trip.duration >= minDuration && trip.duration <= maxDuration),
      
    highValue: (threshold = 50) => trips.filter(trip => trip.earnings >= threshold),
    
    recent: (days = 7) => {
      const cutoff = new Date(Date.now() - days * 24 * 60 * 60 * 1000);
      return trips.filter(trip => trip.completedAt >= cutoff);
    },
    
    byDayOfWeek: (dayOfWeek: number) => // 0 = Sunday, 1 = Monday, etc.
      trips.filter(trip => trip.completedAt.getDay() === dayOfWeek),
      
    byTimeOfDay: (startHour: number, endHour: number) => 
      trips.filter(trip => {
        const hour = trip.completedAt.getHours();
        return hour >= startHour && hour <= endHour;
      }),
  }), [trips]);
  
  // Auto-refresh earnings data
  useEffect(() => {
    const interval = setInterval(() => {
      if (state.auth.isAuthenticated && !isLoading) {
        loadEarningsData();
      }
    }, 5 * 60 * 1000); // Every 5 minutes
    
    return () => clearInterval(interval);
  }, [state.auth.isAuthenticated, isLoading, loadEarningsData]);
  
  return {
    // State
    totalEarnings,
    weeklyEarnings,
    dailyEarnings,
    todayTrips,
    trips,
    earnings,
    isLoading,
    error,
    
    // Computed
    averageEarningsPerTrip,
    todayStats,
    weeklyStats,
    monthlyStats,
    earningsGrowth,
    earningsAnalytics,
    goalTracking,
    predictions,
    
    // Functions
    earningsTrend,
    topEarningTrips,
    recentTrips,
    tripFilters,
    
    // Actions
    loadTripHistory,
    loadEarningsData,
    addTrip,
    addTripOptimistically,
    updateEarnings,
    setLoading,
    setError,
    
    // Utilities
    formatEarnings: (amount: number) => `£${amount.toFixed(2)}`,
    formatDistance: (distance: number) => `${distance.toFixed(1)} km`,
    formatDuration: (minutes: number) => {
      const hours = Math.floor(minutes / 60);
      const mins = minutes % 60;
      return hours > 0 ? `${hours}h ${mins}m` : `${mins}m`;
    },
    
    getEarningsForPeriod: (startDate: Date, endDate: Date) => {
      const filtered = tripFilters.byDateRange(startDate, endDate);
      return filtered.reduce((sum, trip) => sum + trip.earnings, 0);
    },
    
    getTripsForPeriod: (startDate: Date, endDate: Date) => {
      return tripFilters.byDateRange(startDate, endDate).length;
    },
    
    isGoalMet: (period: 'daily' | 'weekly' | 'monthly') => {
      return goalTracking[period].percentage >= 100;
    },
    
    getPerformanceGrade: () => {
      const weeklyPercentage = goalTracking.weekly.percentage;
      if (weeklyPercentage >= 120) return 'A+';
      if (weeklyPercentage >= 100) return 'A';
      if (weeklyPercentage >= 80) return 'B';
      if (weeklyPercentage >= 60) return 'C';
      return 'D';
    },
  };
};