// Driver Status and Location Hook
import { useCallback, useMemo, useEffect } from 'react';
import { useEnhancedState } from '../EnhancedStateContext';
import { driverSelectors } from '../slices/driverSlice';
import { Location } from '../../types';

export const useDriver = () => {
  const { state, dispatch, updateDriverLocation, setDriverOnlineStatus, loadDriverProfile, updateDriverProfile } = useEnhancedState();
  
  // Memoized selectors
  const isOnline = useMemo(() => driverSelectors.isOnline(state.driver), [state.driver]);
  const location = useMemo(() => driverSelectors.location(state.driver), [state.driver]);
  const availableDrivers = useMemo(() => driverSelectors.availableDrivers(state.driver), [state.driver]);
  const stats = useMemo(() => driverSelectors.stats(state.driver), [state.driver]);
  const isLoading = useMemo(() => driverSelectors.isLoading(state.driver), [state.driver]);
  const error = useMemo(() => driverSelectors.error(state.driver), [state.driver]);
  const lastLocationUpdate = useMemo(() => driverSelectors.lastLocationUpdate(state.driver), [state.driver]);
  
  // Computed selectors
  const hasLocation = useMemo(() => driverSelectors.hasLocation(state.driver), [state.driver]);
  const isLocationStale = useCallback((maxAgeMs?: number) => 
    driverSelectors.isLocationStale(state.driver, maxAgeMs), [state.driver]);
  const driverRating = useMemo(() => driverSelectors.driverRating(state.driver), [state.driver]);
  const performanceMetrics = useMemo(() => driverSelectors.performanceMetrics(state.driver), [state.driver]);
  
  const nearbyDrivers = useCallback((maxDistanceKm?: number) => {
    if (!location) return [];
    return driverSelectors.nearbyDrivers(location, maxDistanceKm)(state.driver);
  }, [location, state.driver]);
  
  // Enhanced driver status
  const driverStatus = useMemo(() => {
    if (!state.auth.isAuthenticated) return 'offline';
    if (!isOnline) return 'offline';
    if (state.rides.currentRide) {
      switch (state.rides.rideStatus) {
        case 'heading_to_pickup': return 'en_route_to_pickup';
        case 'arrived_at_pickup': return 'waiting_for_passenger';
        case 'passenger_onboard': return 'in_trip';
        case 'arrived_at_destination': return 'completing_trip';
        default: return 'available';
      }
    }
    if (state.rides.queuedBookings.length > 0) return 'busy_with_queue';
    return 'available';
  }, [state.auth.isAuthenticated, isOnline, state.rides.currentRide, state.rides.rideStatus, state.rides.queuedBookings]);
  
  // Location tracking and management
  const locationManager = useMemo(() => ({
    startTracking: async () => {
      // This would integrate with the LocationService
      dispatch({ type: 'driver/setLoading', payload: true });
      try {
        // Start location tracking
        // Implementation would depend on LocationService
        dispatch({ type: 'driver/setError', payload: null });
      } catch (error: any) {
        dispatch({ type: 'driver/setError', payload: error.message });
      } finally {
        dispatch({ type: 'driver/setLoading', payload: false });
      }
    },
    
    stopTracking: () => {
      // Stop location tracking
      // Implementation would depend on LocationService
    },
    
    updateLocation: async (newLocation: Location) => {
      await updateDriverLocation(newLocation);
    },
    
    getCurrentLocation: async (): Promise<Location | null> => {
      try {
        // Get current location from LocationService
        // Return formatted location
        return location;
      } catch (error) {
        console.error('Failed to get current location:', error);
        return null;
      }
    },
    
    isInServiceArea: (location: Location): boolean => {
      // Check if location is within service boundaries
      // This would be configurable per city/region
      const serviceBounds = {
        north: 51.6,
        south: 51.3,
        east: 0.3,
        west: -0.5,
      };
      
      return (
        location.latitude >= serviceBounds.south &&
        location.latitude <= serviceBounds.north &&
        location.longitude >= serviceBounds.west &&
        location.longitude <= serviceBounds.east
      );
    },
  }), [location, dispatch, updateDriverLocation]);
  
  // Driver performance insights
  const performanceInsights = useMemo(() => {
    const { acceptanceRate, cancellationRate, rating, totalTrips } = stats;
    const insights = [];
    
    if (acceptanceRate < 0.8) {
      insights.push({
        type: 'warning',
        title: 'Acceptance Rate',
        message: 'Your acceptance rate is below 80%. Try accepting more rides to improve your rating.',
        priority: 'high',
      });
    }
    
    if (cancellationRate > 0.1) {
      insights.push({
        type: 'warning',
        title: 'Cancellation Rate',
        message: 'Your cancellation rate is high. Avoid cancelling rides after acceptance.',
        priority: 'medium',
      });
    }
    
    if (rating < 4.0 && totalTrips > 10) {
      insights.push({
        type: 'alert',
        title: 'Driver Rating',
        message: 'Your rating needs improvement. Focus on customer service and safe driving.',
        priority: 'high',
      });
    }
    
    if (totalTrips < 10) {
      insights.push({
        type: 'info',
        title: 'New Driver',
        message: 'Complete more trips to unlock advanced features and better insights.',
        priority: 'low',
      });
    }
    
    if (acceptanceRate > 0.9 && rating > 4.5) {
      insights.push({
        type: 'success',
        title: 'Excellent Performance',
        message: 'You\'re doing great! Keep up the excellent service.',
        priority: 'low',
      });
    }
    
    return insights;
  }, [stats]);
  
  // Driver zones and heat maps
  const zoneAnalytics = useMemo(() => {
    if (!availableDrivers.length) return null;
    
    // Analyze driver distribution
    const zones = availableDrivers.reduce((acc, driver) => {
      if (!driver.currentLocation) return acc;
      
      // Simple zone classification based on coordinates
      const lat = driver.currentLocation.latitude;
      const lon = driver.currentLocation.longitude;
      const zone = `${Math.floor(lat * 100) / 100}-${Math.floor(lon * 100) / 100}`;
      
      acc[zone] = (acc[zone] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);
    
    const hotZones = Object.entries(zones)
      .sort(([, a], [, b]) => b - a)
      .slice(0, 5)
      .map(([zone, count]) => ({ zone, driverCount: count }));
    
    return {
      totalActiveDrivers: availableDrivers.length,
      zones,
      hotZones,
      averageDriversPerZone: Object.values(zones).reduce((sum, count) => sum + count, 0) / Object.keys(zones).length,
    };
  }, [availableDrivers]);
  
  // Actions
  const setOnlineStatus = useCallback(async (online: boolean) => {
    if (!location && online) {
      throw new Error('Cannot go online without location');
    }
    await setDriverOnlineStatus(online);
  }, [location, setDriverOnlineStatus]);
  
  const updateStats = useCallback((updates: any) => {
    dispatch({ type: 'driver/updateStats', payload: updates });
  }, [dispatch]);
  
  const setLoading = useCallback((loading: boolean) => {
    dispatch({ type: 'driver/setLoading', payload: loading });
  }, [dispatch]);
  
  const setError = useCallback((error: string | null) => {
    dispatch({ type: 'driver/setError', payload: error });
  }, [dispatch]);
  
  // Auto-refresh driver data
  useEffect(() => {
    if (state.auth.isAuthenticated && isOnline && !isLoading) {
      const interval = setInterval(() => {
        loadDriverProfile();
      }, 10 * 60 * 1000); // Every 10 minutes
      
      return () => clearInterval(interval);
    }
  }, [state.auth.isAuthenticated, isOnline, isLoading, loadDriverProfile]);
  
  // Location staleness check
  useEffect(() => {
    if (isOnline && location && isLocationStale(5 * 60 * 1000)) { // 5 minutes
      console.warn('Driver location is stale, requesting update');
      locationManager.getCurrentLocation();
    }
  }, [isOnline, location, isLocationStale, locationManager]);
  
  return {
    // State
    isOnline,
    location,
    availableDrivers,
    stats,
    isLoading,
    error,
    lastLocationUpdate,
    
    // Computed
    hasLocation,
    isLocationStale,
    driverRating,
    performanceMetrics,
    driverStatus,
    performanceInsights,
    zoneAnalytics,
    nearbyDrivers,
    
    // Location management
    locationManager,
    
    // Actions
    setOnlineStatus,
    updateDriverLocation,
    loadDriverProfile,
    updateDriverProfile,
    updateStats,
    setLoading,
    setError,
    
    // Utilities
    canGoOnline: () => {
      if (!state.auth.isAuthenticated) return false;
      if (!location) return false;
      if (!locationManager.isInServiceArea(location)) return false;
      return true;
    },
    
    getStatusColor: () => {
      switch (driverStatus) {
        case 'available': return '#4CAF50';
        case 'busy_with_queue': return '#FF9800';
        case 'en_route_to_pickup': return '#2196F3';
        case 'waiting_for_passenger': return '#9C27B0';
        case 'in_trip': return '#FF5722';
        case 'completing_trip': return '#607D8B';
        case 'offline': return '#9E9E9E';
        default: return '#9E9E9E';
      }
    },
    
    getStatusText: () => {
      switch (driverStatus) {
        case 'available': return 'Available';
        case 'busy_with_queue': return 'Busy (Rides Queued)';
        case 'en_route_to_pickup': return 'En Route to Pickup';
        case 'waiting_for_passenger': return 'Waiting for Passenger';
        case 'in_trip': return 'In Trip';
        case 'completing_trip': return 'Completing Trip';
        case 'offline': return 'Offline';
        default: return 'Unknown';
      }
    },
    
    getPerformanceGrade: () => performanceMetrics.acceptanceRate.grade,
    
    shouldShowLocationWarning: () => !hasLocation || isLocationStale(10 * 60 * 1000),
    
    getLocationAccuracy: () => {
      // This would depend on actual location data
      return lastLocationUpdate ? 'GPS' : 'Unknown';
    },
  };
};