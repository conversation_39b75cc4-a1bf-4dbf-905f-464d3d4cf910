// Optimistic Updates Hook
import { useCallback, useMemo, useRef } from 'react';
import { useEnhancedState } from '../EnhancedStateContext';
import { StateAction } from '../slices/types';

interface OptimisticUpdate {
  id: string;
  action: StateAction;
  rollbackAction: StateAction;
  timestamp: number;
  timeout: number;
  status: 'pending' | 'confirmed' | 'failed' | 'rolled_back';
  retryCount: number;
  maxRetries: number;
}

export const useOptimistic = () => {
  const { state, dispatch, optimisticDispatch } = useEnhancedState();
  const optimisticUpdatesRef = useRef<Map<string, OptimisticUpdate>>(new Map());
  
  // Create optimistic update
  const createOptimisticUpdate = useCallback((
    action: StateAction,
    rollbackAction: StateAction,
    options: {
      timeout?: number;
      maxRetries?: number;
      onConfirm?: () => void;
      onRollback?: () => void;
      onRetry?: (attempt: number) => void;
    } = {}
  ): string => {
    const updateId = `optimistic_${Date.now()}_${Math.random()}`;
    const timeout = options.timeout || 5000;
    const maxRetries = options.maxRetries || 3;
    
    const optimisticUpdate: OptimisticUpdate = {
      id: updateId,
      action,
      rollbackAction,
      timestamp: Date.now(),
      timeout,
      status: 'pending',
      retryCount: 0,
      maxRetries,
    };
    
    optimisticUpdatesRef.current.set(updateId, optimisticUpdate);
    
    // Apply optimistic update immediately
    dispatch(action);
    
    // Set timeout for rollback
    setTimeout(async () => {
      const update = optimisticUpdatesRef.current.get(updateId);
      if (update && update.status === 'pending') {
        update.status = 'failed';
        await rollbackUpdate(updateId);
        options.onRollback?.();
      }
    }, timeout);
    
    return updateId;
  }, [dispatch]);
  
  // Confirm optimistic update
  const confirmUpdate = useCallback((updateId: string, serverResponse?: any) => {
    const update = optimisticUpdatesRef.current.get(updateId);
    if (update && update.status === 'pending') {
      update.status = 'confirmed';
      
      // If server response differs from optimistic update, apply the correction
      if (serverResponse && serverResponse !== update.action.payload) {
        dispatch({
          ...update.action,
          payload: serverResponse,
        });
      }
      
      // Clean up after a delay
      setTimeout(() => {
        optimisticUpdatesRef.current.delete(updateId);
      }, 1000);
    }
  }, [dispatch]);
  
  // Rollback optimistic update
  const rollbackUpdate = useCallback(async (updateId: string) => {
    const update = optimisticUpdatesRef.current.get(updateId);
    if (update && (update.status === 'pending' || update.status === 'failed')) {
      update.status = 'rolled_back';
      dispatch(update.rollbackAction);
      
      // Clean up after a delay
      setTimeout(() => {
        optimisticUpdatesRef.current.delete(updateId);
      }, 1000);
    }
  }, [dispatch]);
  
  // Retry failed optimistic update
  const retryUpdate = useCallback(async (updateId: string, newAction?: StateAction) => {
    const update = optimisticUpdatesRef.current.get(updateId);
    if (update && update.status === 'failed' && update.retryCount < update.maxRetries) {
      update.retryCount++;
      update.status = 'pending';
      update.timestamp = Date.now();
      
      const actionToExecute = newAction || update.action;
      dispatch(actionToExecute);
      
      // Set new timeout
      setTimeout(() => {
        const currentUpdate = optimisticUpdatesRef.current.get(updateId);
        if (currentUpdate && currentUpdate.status === 'pending') {
          currentUpdate.status = 'failed';
          rollbackUpdate(updateId);
        }
      }, update.timeout);
      
      return true;
    }
    return false;
  }, [dispatch, rollbackUpdate]);
  
  // High-level optimistic operations
  const operations = useMemo(() => ({
    // Accept ride optimistically
    acceptRide: async (rideId: string, onConfirm?: (result: any) => void) => {
      const ride = state.rides.availableRides.find(r => r.id === rideId);
      if (!ride) return null;
      
      const updateId = createOptimisticUpdate(
        { type: 'rides/removeAvailableRide', payload: rideId },
        { type: 'rides/addAvailableRide', payload: ride },
        {
          timeout: 10000, // 10 seconds for ride acceptance
          onConfirm: () => onConfirm?.(ride),
          onRollback: () => {
            dispatch({
              type: 'system/setError',
              payload: { section: 'rides', error: 'Failed to accept ride' },
            });
          },
        }
      );
      
      // Apply additional optimistic changes
      if (!state.rides.currentRide) {
        dispatch({ type: 'rides/setCurrentRide', payload: ride });
        dispatch({ type: 'rides/setRideStatus', payload: 'heading_to_pickup' });
      } else {
        dispatch({ type: 'rides/addQueuedBooking', payload: ride });
      }
      
      return updateId;
    },
    
    // Update driver location optimistically
    updateLocation: (location: any) => {
      return createOptimisticUpdate(
        { type: 'driver/setLocation', payload: location },
        { type: 'driver/setLocation', payload: state.driver.location },
        {
          timeout: 3000, // Quick timeout for location updates
        }
      );
    },
    
    // Complete trip optimistically
    completeTrip: (tripData: any) => {
      return createOptimisticUpdate(
        { type: 'earnings/addTrip', payload: tripData },
        { type: 'earnings/setError', payload: 'Failed to complete trip' },
        {
          timeout: 15000, // Longer timeout for trip completion
          onConfirm: () => {
            // Additional actions on confirmation
            dispatch({ type: 'rides/setCurrentRide', payload: null });
            dispatch({ type: 'rides/setRideStatus', payload: null });
          },
        }
      );
    },
    
    // Update driver status optimistically
    setOnlineStatus: (isOnline: boolean) => {
      return createOptimisticUpdate(
        { type: 'driver/setOnlineStatus', payload: isOnline },
        { type: 'driver/setOnlineStatus', payload: state.driver.isOnline },
        {
          timeout: 5000,
          onRollback: () => {
            dispatch({
              type: 'system/setError',
              payload: { section: 'driver', error: 'Failed to update online status' },
            });
          },
        }
      );
    },
    
    // Update profile optimistically
    updateProfile: (profileData: any) => {
      return createOptimisticUpdate(
        { type: 'auth/updateDriver', payload: { ...state.auth.driver, ...profileData } },
        { type: 'auth/updateDriver', payload: state.auth.driver },
        {
          timeout: 8000,
          onRollback: () => {
            dispatch({
              type: 'system/setError',
              payload: { section: 'driver', error: 'Failed to update profile' },
            });
          },
        }
      );
    },
  }), [state, dispatch, createOptimisticUpdate]);
  
  // Statistics and monitoring
  const statistics = useMemo(() => {
    const updates = Array.from(optimisticUpdatesRef.current.values());
    const totalUpdates = updates.length;
    const pendingUpdates = updates.filter(u => u.status === 'pending').length;
    const confirmedUpdates = updates.filter(u => u.status === 'confirmed').length;
    const failedUpdates = updates.filter(u => u.status === 'failed').length;
    const rolledBackUpdates = updates.filter(u => u.status === 'rolled_back').length;
    
    const successRate = totalUpdates > 0 ? confirmedUpdates / totalUpdates : 0;
    const failureRate = totalUpdates > 0 ? (failedUpdates + rolledBackUpdates) / totalUpdates : 0;
    
    return {
      totalUpdates,
      pendingUpdates,
      confirmedUpdates,
      failedUpdates,
      rolledBackUpdates,
      successRate,
      failureRate,
    };
  }, []);
  
  // Health monitoring
  const health = useMemo(() => {
    const updates = Array.from(optimisticUpdatesRef.current.values());
    const now = Date.now();
    const staleUpdates = updates.filter(u => 
      u.status === 'pending' && (now - u.timestamp) > u.timeout * 2
    );
    const highFailureRate = statistics.failureRate > 0.3;
    const tooManyPending = statistics.pendingUpdates > 10;
    
    const isHealthy = staleUpdates.length === 0 && !highFailureRate && !tooManyPending;
    
    return {
      isHealthy,
      issues: [
        ...(staleUpdates.length > 0 ? [`${staleUpdates.length} stale optimistic updates`] : []),
        ...(highFailureRate ? ['High failure rate for optimistic updates'] : []),
        ...(tooManyPending ? ['Too many pending optimistic updates'] : []),
      ],
      staleUpdates: staleUpdates.length,
    };
  }, [statistics]);
  
  // Cleanup utilities
  const cleanup = useMemo(() => ({
    clearAll: () => {
      optimisticUpdatesRef.current.clear();
    },
    
    clearCompleted: () => {
      const updates = Array.from(optimisticUpdatesRef.current.entries());
      updates.forEach(([id, update]) => {
        if (update.status === 'confirmed' || update.status === 'rolled_back') {
          optimisticUpdatesRef.current.delete(id);
        }
      });
    },
    
    clearStale: () => {
      const now = Date.now();
      const updates = Array.from(optimisticUpdatesRef.current.entries());
      updates.forEach(([id, update]) => {
        if (update.status === 'pending' && (now - update.timestamp) > update.timeout * 2) {
          rollbackUpdate(id);
        }
      });
    },
    
    forceRollbackAll: () => {
      const updates = Array.from(optimisticUpdatesRef.current.entries());
      updates.forEach(([id, update]) => {
        if (update.status === 'pending') {
          rollbackUpdate(id);
        }
      });
    },
  }), [rollbackUpdate]);
  
  return {
    // Core operations
    createOptimisticUpdate,
    confirmUpdate,
    rollbackUpdate,
    retryUpdate,
    
    // High-level operations
    operations,
    
    // Monitoring
    statistics,
    health,
    
    // Current state
    activeUpdates: Array.from(optimisticUpdatesRef.current.values()),
    pendingCount: statistics.pendingUpdates,
    isHealthy: health.isHealthy,
    
    // Utilities
    cleanup,
    
    // Quick access methods
    hasPendingUpdates: () => statistics.pendingUpdates > 0,
    getUpdateStatus: (updateId: string) => optimisticUpdatesRef.current.get(updateId)?.status,
    
    // Formatted utilities
    formatSuccessRate: () => `${(statistics.successRate * 100).toFixed(1)}%`,
    formatFailureRate: () => `${(statistics.failureRate * 100).toFixed(1)}%`,
    
    // Debug utilities (development only)
    debugInfo: __DEV__ ? {
      allUpdates: Array.from(optimisticUpdatesRef.current.entries()),
      export: () => JSON.stringify(Array.from(optimisticUpdatesRef.current.entries()), null, 2),
    } : undefined,
  };
};