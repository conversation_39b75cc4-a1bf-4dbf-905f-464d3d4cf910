// Intelligent Caching System
import AsyncStorage from '@react-native-async-storage/async-storage';
import { CacheConfig } from '../slices/types';

export interface CacheEntry<T = any> {
  data: T;
  timestamp: number;
  ttl: number;
  etag?: string;
  version: string;
  accessCount: number;
  lastAccess: number;
}

export interface CacheOptions {
  ttl?: number;
  maxSize?: number;
  strategy?: 'stale-while-revalidate' | 'cache-first' | 'network-first';
  persistToDisk?: boolean;
  compression?: boolean;
}

export class CacheManager {
  private cache = new Map<string, CacheEntry>();
  private maxSize: number;
  private defaultTTL: number;
  private persistencePrefix = 'cache:';
  
  constructor(options: CacheOptions = {}) {
    this.maxSize = options.maxSize || 100;
    this.defaultTTL = options.ttl || 5 * 60 * 1000; // 5 minutes
    
    // Initialize cleanup interval
    this.startCleanupInterval();
    
    // Restore from persistent storage
    if (options.persistToDisk) {
      this.restoreFromDisk();
    }
  }

  /**
   * Set data in cache
   */
  async set<T>(
    key: string, 
    data: T, 
    options: CacheOptions = {}
  ): Promise<void> {
    const ttl = options.ttl || this.defaultTTL;
    const entry: CacheEntry<T> = {
      data,
      timestamp: Date.now(),
      ttl,
      version: '1.0',
      accessCount: 0,
      lastAccess: Date.now(),
    };

    // Enforce cache size limit
    if (this.cache.size >= this.maxSize) {
      this.evictLeastUsed();
    }

    this.cache.set(key, entry);

    // Persist to disk if enabled
    if (options.persistToDisk) {
      await this.persistToDisk(key, entry);
    }
  }

  /**
   * Get data from cache
   */
  get<T>(key: string): T | null {
    const entry = this.cache.get(key) as CacheEntry<T> | undefined;
    
    if (!entry) {
      return null;
    }

    // Check if entry is expired
    if (this.isExpired(entry)) {
      this.cache.delete(key);
      this.removeFromDisk(key);
      return null;
    }

    // Update access statistics
    entry.accessCount++;
    entry.lastAccess = Date.now();
    
    return entry.data;
  }

  /**
   * Check if data exists and is fresh
   */
  has(key: string): boolean {
    const entry = this.cache.get(key);
    return entry ? !this.isExpired(entry) : false;
  }

  /**
   * Check if data is stale but exists
   */
  isStale(key: string, maxAge?: number): boolean {
    const entry = this.cache.get(key);
    if (!entry) return true;
    
    const age = Date.now() - entry.timestamp;
    const threshold = maxAge || entry.ttl;
    
    return age > threshold;
  }

  /**
   * Get stale data (useful for stale-while-revalidate)
   */
  getStale<T>(key: string): T | null {
    const entry = this.cache.get(key) as CacheEntry<T> | undefined;
    return entry ? entry.data : null;
  }

  /**
   * Invalidate specific key
   */
  async invalidate(key: string): Promise<void> {
    this.cache.delete(key);
    await this.removeFromDisk(key);
  }

  /**
   * Invalidate keys matching pattern
   */
  async invalidatePattern(pattern: string): Promise<void> {
    const regex = new RegExp(pattern);
    const keysToDelete: string[] = [];
    
    for (const key of this.cache.keys()) {
      if (regex.test(key)) {
        keysToDelete.push(key);
      }
    }
    
    for (const key of keysToDelete) {
      await this.invalidate(key);
    }
  }

  /**
   * Clear all cache
   */
  async clear(): Promise<void> {
    this.cache.clear();
    
    try {
      const keys = await AsyncStorage.getAllKeys();
      const cacheKeys = keys.filter(key => key.startsWith(this.persistencePrefix));
      if (cacheKeys.length > 0) {
        await AsyncStorage.multiRemove(cacheKeys);
      }
    } catch (error) {
      console.error('Failed to clear persistent cache:', error);
    }
  }

  /**
   * Get cache statistics
   */
  getStats() {
    const entries = Array.from(this.cache.values());
    const totalSize = entries.length;
    const hitRate = entries.reduce((sum, entry) => sum + entry.accessCount, 0) / totalSize || 0;
    const averageAge = entries.reduce((sum, entry) => 
      sum + (Date.now() - entry.timestamp), 0) / totalSize || 0;
    
    return {
      size: totalSize,
      maxSize: this.maxSize,
      hitRate,
      averageAge,
      memoryUsage: this.estimateMemoryUsage(),
      expiredEntries: entries.filter(entry => this.isExpired(entry)).length,
    };
  }

  /**
   * Prefetch data with background refresh
   */
  async prefetch<T>(
    key: string,
    fetcher: () => Promise<T>,
    options: CacheOptions = {}
  ): Promise<T> {
    const cached = this.get<T>(key);
    
    if (cached && !this.isStale(key)) {
      return cached;
    }
    
    // If we have stale data and using stale-while-revalidate
    if (cached && options.strategy === 'stale-while-revalidate') {
      // Return stale data immediately
      setTimeout(async () => {
        try {
          const fresh = await fetcher();
          await this.set(key, fresh, options);
        } catch (error) {
          console.warn(`Background refresh failed for ${key}:`, error);
        }
      }, 0);
      
      return cached;
    }
    
    // Fetch fresh data
    const data = await fetcher();
    await this.set(key, data, options);
    return data;
  }

  /**
   * Batch operations
   */
  async setMany<T>(entries: Array<{ key: string; data: T; options?: CacheOptions }>): Promise<void> {
    await Promise.all(
      entries.map(({ key, data, options }) => this.set(key, data, options))
    );
  }

  getMany<T>(keys: string[]): Array<{ key: string; data: T | null }> {
    return keys.map(key => ({ key, data: this.get<T>(key) }));
  }

  // Private methods

  private isExpired(entry: CacheEntry): boolean {
    return Date.now() - entry.timestamp > entry.ttl;
  }

  private evictLeastUsed(): void {
    let leastUsedKey = '';
    let leastUsedScore = Infinity;
    
    // Calculate usage score (access count / age)
    for (const [key, entry] of this.cache.entries()) {
      const age = Date.now() - entry.lastAccess;
      const score = entry.accessCount / (age + 1);
      
      if (score < leastUsedScore) {
        leastUsedScore = score;
        leastUsedKey = key;
      }
    }
    
    if (leastUsedKey) {
      this.cache.delete(leastUsedKey);
      this.removeFromDisk(leastUsedKey);
    }
  }

  private startCleanupInterval(): void {
    // Clean up expired entries every 5 minutes
    setInterval(() => {
      this.cleanupExpired();
    }, 5 * 60 * 1000);
  }

  private cleanupExpired(): void {
    const keysToDelete: string[] = [];
    
    for (const [key, entry] of this.cache.entries()) {
      if (this.isExpired(entry)) {
        keysToDelete.push(key);
      }
    }
    
    keysToDelete.forEach(key => {
      this.cache.delete(key);
      this.removeFromDisk(key);
    });
    
    if (keysToDelete.length > 0) {
      console.log(`🧹 Cleaned up ${keysToDelete.length} expired cache entries`);
    }
  }

  private async persistToDisk(key: string, entry: CacheEntry): Promise<void> {
    try {
      const diskKey = `${this.persistencePrefix}${key}`;
      await AsyncStorage.setItem(diskKey, JSON.stringify(entry));
    } catch (error) {
      console.warn(`Failed to persist cache entry ${key}:`, error);
    }
  }

  private async removeFromDisk(key: string): Promise<void> {
    try {
      const diskKey = `${this.persistencePrefix}${key}`;
      await AsyncStorage.removeItem(diskKey);
    } catch (error) {
      console.warn(`Failed to remove cache entry ${key} from disk:`, error);
    }
  }

  private async restoreFromDisk(): Promise<void> {
    try {
      const keys = await AsyncStorage.getAllKeys();
      const cacheKeys = keys.filter(key => key.startsWith(this.persistencePrefix));
      
      if (cacheKeys.length === 0) return;
      
      const entries = await AsyncStorage.multiGet(cacheKeys);
      
      for (const [diskKey, value] of entries) {
        if (!value) continue;
        
        try {
          const entry = JSON.parse(value) as CacheEntry;
          const key = diskKey.replace(this.persistencePrefix, '');
          
          // Only restore if not expired
          if (!this.isExpired(entry)) {
            this.cache.set(key, entry);
          } else {
            // Remove expired entries from disk
            await AsyncStorage.removeItem(diskKey);
          }
        } catch (error) {
          console.warn(`Failed to restore cache entry ${diskKey}:`, error);
          await AsyncStorage.removeItem(diskKey);
        }
      }
      
      console.log(`📱 Restored ${this.cache.size} cache entries from disk`);
    } catch (error) {
      console.error('Failed to restore cache from disk:', error);
    }
  }

  private estimateMemoryUsage(): number {
    let totalSize = 0;
    
    for (const entry of this.cache.values()) {
      // Rough estimation of memory usage
      totalSize += JSON.stringify(entry).length * 2; // UTF-16 encoding
    }
    
    return totalSize;
  }
}

// Singleton instance
export const cacheManager = new CacheManager({
  maxSize: 200,
  ttl: 5 * 60 * 1000, // 5 minutes
  persistToDisk: true,
});

// Domain-specific cache managers
export const rideCacheManager = new CacheManager({
  maxSize: 50,
  ttl: 2 * 60 * 1000, // 2 minutes for rides (more dynamic)
  persistToDisk: false,
});

export const tripCacheManager = new CacheManager({
  maxSize: 100,
  ttl: 30 * 60 * 1000, // 30 minutes for trips (more static)
  persistToDisk: true,
});

export const driverCacheManager = new CacheManager({
  maxSize: 20,
  ttl: 10 * 60 * 1000, // 10 minutes for driver data
  persistToDisk: true,
});

export const earningsCacheManager = new CacheManager({
  maxSize: 30,
  ttl: 15 * 60 * 1000, // 15 minutes for earnings
  persistToDisk: true,
});