import React, { createContext, useContext, useReducer, ReactNode, useEffect } from 'react';
import { Driver, Ride, Location, Trip, ApiLoadingState, ApiErrorState, ConnectionState, CacheState, SyncState } from '../types';
import { generateMockAvailableRides } from '../data/mockData';

interface GlobalState {
  // Auth state
  isAuthenticated: boolean;
  driver: Driver | null;
  token: string | null;
  
  // App state
  currentRide: Ride | null;
  location: Location | null;
  trips: Trip[];
  totalEarnings: number;
  pendingBookings: Ride[];
  weeklyEarnings: number;
  todayTrips: number;
  availableDrivers: Driver[];
  sidebarOpen: boolean;
  navigationVisible: boolean;
  isOnline: boolean;
  queueStatus: 'available' | 'limited_queue' | 'service_mode' | 'completing';
  queuedBookings: Ride[];
  maxQueueSize: number;
  rideStatus: 'heading_to_pickup' | 'arrived_at_pickup' | 'passenger_onboard' | 'arrived_at_destination' | null;
  availableRides: Ride[];
  mockDataGenerated: boolean;
  mapRegion: {
    latitude: number;
    longitude: number;
    latitudeDelta: number;
    longitudeDelta: number;
  };
  
  // API State Management
  loading: ApiLoadingState;
  errors: ApiErrorState;
  connection: ConnectionState;
  cache: CacheState;
  sync: SyncState;
}

type GlobalAction = 
  // Auth actions
  | { type: 'LOGIN'; payload: { driver: Driver; token: string } }
  | { type: 'LOGOUT' }
  | { type: 'UPDATE_DRIVER'; payload: Driver }
  // App actions
  | { type: 'SET_LOCATION'; payload: Location }
  | { type: 'SET_CURRENT_RIDE'; payload: Ride | null }
  | { type: 'ADD_TRIP'; payload: Trip }
  | { type: 'UPDATE_EARNINGS'; payload: number }
  | { type: 'SET_AVAILABLE_DRIVERS'; payload: Driver[] }
  | { type: 'TOGGLE_SIDEBAR'; payload?: boolean }
  | { type: 'SET_MAP_REGION'; payload: { latitude: number; longitude: number; latitudeDelta: number; longitudeDelta: number } }
  | { type: 'ADD_PENDING_BOOKING'; payload: Ride }
  | { type: 'REMOVE_PENDING_BOOKING'; payload: string }
  | { type: 'SET_NAVIGATION_VISIBILITY'; payload: boolean }
  | { type: 'SET_ONLINE_STATUS'; payload: boolean }
  | { type: 'SET_QUEUE_STATUS'; payload: 'available' | 'limited_queue' | 'service_mode' | 'completing' }
  | { type: 'ADD_QUEUED_BOOKING'; payload: Ride }
  | { type: 'REMOVE_QUEUED_BOOKING'; payload: string }
  | { type: 'SET_QUEUED_BOOKINGS'; payload: Ride[] }
  | { type: 'SET_MAX_QUEUE_SIZE'; payload: number }
  | { type: 'SET_RIDE_STATUS'; payload: 'heading_to_pickup' | 'arrived_at_pickup' | 'passenger_onboard' | 'arrived_at_destination' | null }
  | { type: 'SET_AVAILABLE_RIDES'; payload: Ride[] }
  | { type: 'REMOVE_AVAILABLE_RIDE'; payload: string }
  | { type: 'SET_MOCK_DATA_GENERATED'; payload: boolean }
  // API State Management actions
  | { type: 'SET_LOADING'; payload: { section: keyof ApiLoadingState; loading: boolean } }
  | { type: 'SET_ERROR'; payload: { section: keyof ApiErrorState; error: string | null } }
  | { type: 'SET_CONNECTION_STATE'; payload: Partial<ConnectionState> }
  | { type: 'SET_CACHE'; payload: { section: keyof CacheState; data: any } }
  | { type: 'CLEAR_CACHE'; payload?: keyof CacheState }
  | { type: 'ADD_PENDING_ACTION'; payload: { type: string; payload: any } }
  | { type: 'REMOVE_PENDING_ACTION'; payload: string }
  | { type: 'SET_SYNC_STATUS'; payload: { isSyncing: boolean; lastSyncTime?: Date } };

interface GlobalContextType {
  state: GlobalState;
  // Auth methods
  login: (driver: Driver, token: string) => void;
  logout: () => void;
  updateDriver: (driver: Driver) => void;
  // App methods
  setLocation: (location: Location) => void;
  setCurrentRide: (ride: Ride | null) => void;
  addTrip: (trip: Trip) => void;
  updateEarnings: (earnings: number) => void;
  setAvailableDrivers: (drivers: Driver[]) => void;
  toggleSidebar: (open?: boolean) => void;
  setMapRegion: (region: { latitude: number; longitude: number; latitudeDelta: number; longitudeDelta: number }) => void;
  addPendingBooking: (booking: Ride) => void;
  removePendingBooking: (bookingId: string) => void;
  setNavigationVisibility: (visible: boolean) => void;
  setOnlineStatus: (isOnline: boolean) => void;
  setQueueStatus: (status: 'available' | 'limited_queue' | 'service_mode' | 'completing') => void;
  addQueuedBooking: (booking: Ride) => void;
  removeQueuedBooking: (bookingId: string) => void;
  setQueuedBookings: (bookings: Ride[]) => void;
  setRideStatus: (status: 'heading_to_pickup' | 'arrived_at_pickup' | 'passenger_onboard' | 'arrived_at_destination' | null) => void;
  shouldShowBookings: () => boolean;
  canAcceptNewBooking: () => boolean;
  getMaxQueueSize: () => number;
  startNextQueuedRide: () => Ride | null;
  setAvailableRides: (rides: Ride[]) => void;
  removeAvailableRide: (rideId: string) => void;
  generateMockRides: () => void;
  // Unified booking management
  getAllBookings: () => Ride[];
  getBookingsByStatus: (status: 'pending' | 'accepted' | 'ongoing' | 'completed' | 'cancelled' | 'rejected') => Ride[];
  getBookingsByType: (bookingStatus: 'new' | 'urgent' | 'scheduled' | 'popular' | 'premium') => Ride[];
  getActiveBookings: () => Ride[];
  getScheduledBookings: () => Ride[];
  acceptBooking: (rideId: string) => boolean;
  rejectBooking: (rideId: string) => void;
  completeBooking: (rideId: string) => void;
  // API State Management methods
  setLoading: (section: keyof ApiLoadingState, loading: boolean) => void;
  setError: (section: keyof ApiErrorState, error: string | null) => void;
  clearErrors: () => void;
  setConnectionState: (state: Partial<ConnectionState>) => void;
  isDataStale: (section: keyof CacheState, maxAge?: number) => boolean;
  addPendingAction: (actionType: string, payload: any) => void;
  clearPendingActions: () => void;
  setSyncStatus: (isSyncing: boolean, lastSyncTime?: Date) => void;
}

const initialState: GlobalState = {
  // Auth state
  isAuthenticated: false,
  driver: null,
  token: null,
  
  // App state
  currentRide: null,
  location: null,
  trips: [],
  totalEarnings: 0,
  pendingBookings: [],
  weeklyEarnings: 0,
  todayTrips: 0,
  availableDrivers: [],
  sidebarOpen: false,
  navigationVisible: true,
  isOnline: false,
  queueStatus: 'available',
  queuedBookings: [],
  maxQueueSize: 3,
  rideStatus: null,
  availableRides: [],
  mockDataGenerated: false,
  mapRegion: {
    latitude: 37.7749,
    longitude: -122.4194,
    latitudeDelta: 0.0922,
    longitudeDelta: 0.0421,
  },
  
  // API State Management
  loading: {
    auth: false,
    rides: false,
    trips: false,
    driver: false,
    earnings: false,
    bookings: false,
    navigation: false,
    communication: false,
  },
  errors: {
    auth: null,
    rides: null,
    trips: null,
    driver: null,
    earnings: null,
    bookings: null,
    navigation: null,
    communication: null,
    network: null,
  },
  connection: {
    isOnline: true,
    isApiConnected: false,
    isWebSocketConnected: false,
    lastConnectionCheck: null,
    retryCount: 0,
  },
  cache: {
    rides: null,
    trips: null,
    earnings: null,
    driver: null,
  },
  sync: {
    pendingActions: [],
    lastSyncTime: null,
    isSyncing: false,
  },
};

const GlobalContext = createContext<GlobalContextType | undefined>(undefined);

const globalReducer = (state: GlobalState, action: GlobalAction): GlobalState => {
  switch (action.type) {
    // Auth actions
    case 'LOGIN':
      return {
        ...state,
        isAuthenticated: true,
        driver: action.payload.driver,
        token: action.payload.token,
      };
    case 'LOGOUT':
      return {
        ...initialState,
        isAuthenticated: false,
        driver: null,
        token: null,
      };
    case 'UPDATE_DRIVER':
      return {
        ...state,
        driver: action.payload,
      };
      
    // App actions
    case 'SET_LOCATION':
      return {
        ...state,
        location: action.payload,
      };
    case 'SET_CURRENT_RIDE':
      return {
        ...state,
        currentRide: action.payload,
      };
    case 'ADD_TRIP':
      return {
        ...state,
        trips: [action.payload, ...state.trips],
        totalEarnings: state.totalEarnings + action.payload.earnings,
      };
    case 'UPDATE_EARNINGS':
      return {
        ...state,
        totalEarnings: action.payload,
      };
    case 'SET_AVAILABLE_DRIVERS':
      return {
        ...state,
        availableDrivers: action.payload,
      };
    case 'TOGGLE_SIDEBAR':
      return {
        ...state,
        sidebarOpen: action.payload !== undefined ? action.payload : !state.sidebarOpen,
      };
    case 'SET_MAP_REGION':
      return {
        ...state,
        mapRegion: action.payload,
      };
    case 'ADD_PENDING_BOOKING':
      return {
        ...state,
        pendingBookings: [...state.pendingBookings, action.payload],
      };
    case 'REMOVE_PENDING_BOOKING':
      return {
        ...state,
        pendingBookings: state.pendingBookings.filter(booking => booking.id !== action.payload),
      };
    case 'SET_NAVIGATION_VISIBILITY':
      return {
        ...state,
        navigationVisible: action.payload,
      };
    case 'SET_ONLINE_STATUS':
      return {
        ...state,
        isOnline: action.payload,
      };
    case 'SET_QUEUE_STATUS':
      return {
        ...state,
        queueStatus: action.payload,
      };
    case 'ADD_QUEUED_BOOKING':
      return {
        ...state,
        queuedBookings: [...state.queuedBookings, action.payload],
      };
    case 'REMOVE_QUEUED_BOOKING':
      return {
        ...state,
        queuedBookings: state.queuedBookings.filter(booking => booking.id !== action.payload),
      };
    case 'SET_QUEUED_BOOKINGS':
      return {
        ...state,
        queuedBookings: action.payload,
      };
    case 'SET_MAX_QUEUE_SIZE':
      return {
        ...state,
        maxQueueSize: action.payload,
      };
    case 'SET_RIDE_STATUS':
      return {
        ...state,
        rideStatus: action.payload,
      };
    case 'SET_AVAILABLE_RIDES':
      return {
        ...state,
        availableRides: action.payload,
      };
    case 'REMOVE_AVAILABLE_RIDE':
      return {
        ...state,
        availableRides: state.availableRides.filter(ride => ride.id !== action.payload),
      };
    case 'SET_MOCK_DATA_GENERATED':
      return {
        ...state,
        mockDataGenerated: action.payload,
      };
    
    // API State Management actions
    case 'SET_LOADING':
      return {
        ...state,
        loading: {
          ...state.loading,
          [action.payload.section]: action.payload.loading,
        },
      };
    case 'SET_ERROR':
      return {
        ...state,
        errors: {
          ...state.errors,
          [action.payload.section]: action.payload.error,
        },
      };
    case 'SET_CONNECTION_STATE':
      return {
        ...state,
        connection: {
          ...state.connection,
          ...action.payload,
        },
      };
    case 'SET_CACHE':
      return {
        ...state,
        cache: {
          ...state.cache,
          [action.payload.section]: {
            data: action.payload.data,
            timestamp: Date.now(),
          },
        },
      };
    case 'CLEAR_CACHE':
      if (action.payload) {
        return {
          ...state,
          cache: {
            ...state.cache,
            [action.payload]: null,
          },
        };
      } else {
        return {
          ...state,
          cache: {
            rides: null,
            trips: null,
            earnings: null,
            driver: null,
          },
        };
      }
    case 'ADD_PENDING_ACTION':
      return {
        ...state,
        sync: {
          ...state.sync,
          pendingActions: [
            ...state.sync.pendingActions,
            {
              id: `action-${Date.now()}-${Math.random()}`,
              type: action.payload.type,
              payload: action.payload.payload,
              timestamp: new Date(),
              retryCount: 0,
            },
          ],
        },
      };
    case 'REMOVE_PENDING_ACTION':
      return {
        ...state,
        sync: {
          ...state.sync,
          pendingActions: state.sync.pendingActions.filter(
            action => action.id !== action.payload
          ),
        },
      };
    case 'SET_SYNC_STATUS':
      return {
        ...state,
        sync: {
          ...state.sync,
          isSyncing: action.payload.isSyncing,
          lastSyncTime: action.payload.lastSyncTime || state.sync.lastSyncTime,
        },
      };
    default:
      return state;
  }
};

export const GlobalStateProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [state, dispatch] = useReducer(globalReducer, initialState);

  // Auth methods
  const login = (driver: Driver, token: string) => {
    dispatch({ type: 'LOGIN', payload: { driver, token } });
  };

  const logout = () => {
    dispatch({ type: 'LOGOUT' });
  };

  const updateDriver = (driver: Driver) => {
    dispatch({ type: 'UPDATE_DRIVER', payload: driver });
  };

  // App methods
  const setLocation = (location: Location) => {
    dispatch({ type: 'SET_LOCATION', payload: location });
  };

  const setCurrentRide = (ride: Ride | null) => {
    dispatch({ type: 'SET_CURRENT_RIDE', payload: ride });
  };

  const addTrip = (trip: Trip) => {
    dispatch({ type: 'ADD_TRIP', payload: trip });
  };

  const updateEarnings = (earnings: number) => {
    dispatch({ type: 'UPDATE_EARNINGS', payload: earnings });
  };

  const setAvailableDrivers = (drivers: Driver[]) => {
    dispatch({ type: 'SET_AVAILABLE_DRIVERS', payload: drivers });
  };

  const toggleSidebar = (open?: boolean) => {
    dispatch({ type: 'TOGGLE_SIDEBAR', payload: open });
  };

  const setMapRegion = (region: { latitude: number; longitude: number; latitudeDelta: number; longitudeDelta: number }) => {
    dispatch({ type: 'SET_MAP_REGION', payload: region });
  };

  const addPendingBooking = (booking: Ride) => {
    dispatch({ type: 'ADD_PENDING_BOOKING', payload: booking });
  };

  const removePendingBooking = (bookingId: string) => {
    dispatch({ type: 'REMOVE_PENDING_BOOKING', payload: bookingId });
  };

  const setNavigationVisibility = (visible: boolean) => {
    dispatch({ type: 'SET_NAVIGATION_VISIBILITY', payload: visible });
  };

  const setOnlineStatus = (isOnline: boolean) => {
    dispatch({ type: 'SET_ONLINE_STATUS', payload: isOnline });
  };

  const setQueueStatus = (status: 'available' | 'limited_queue' | 'service_mode' | 'completing') => {
    dispatch({ type: 'SET_QUEUE_STATUS', payload: status });
  };

  const addQueuedBooking = (booking: Ride) => {
    dispatch({ type: 'ADD_QUEUED_BOOKING', payload: booking });
  };

  const removeQueuedBooking = (bookingId: string) => {
    dispatch({ type: 'REMOVE_QUEUED_BOOKING', payload: bookingId });
  };

  const setQueuedBookings = (bookings: Ride[]) => {
    dispatch({ type: 'SET_QUEUED_BOOKINGS', payload: bookings });
  };

  const setRideStatus = (status: 'heading_to_pickup' | 'arrived_at_pickup' | 'passenger_onboard' | 'arrived_at_destination' | null) => {
    dispatch({ type: 'SET_RIDE_STATUS', payload: status });
    const newMaxQueueSize = getMaxQueueSizeForStatus(status);
    dispatch({ type: 'SET_MAX_QUEUE_SIZE', payload: newMaxQueueSize });
  };

  const getMaxQueueSizeForStatus = (status: 'heading_to_pickup' | 'arrived_at_pickup' | 'passenger_onboard' | 'arrived_at_destination' | null): number => {
    if (!status) return 3;
    switch (status) {
      case 'heading_to_pickup': return 3;
      case 'arrived_at_pickup': return 2;
      case 'passenger_onboard': return 0;
      case 'arrived_at_destination': return 1;
      default: return 3;
    }
  };

  const shouldShowBookings = (): boolean => {
    if (!state.isOnline) return false;
    if (!state.currentRide) return true;
    
    if (state.rideStatus === 'passenger_onboard' || state.rideStatus === 'arrived_at_destination') {
      return false;
    }
    
    return true;
  };

  const canAcceptNewBooking = (): boolean => {
    if (!state.isOnline) return false;
    if (!state.currentRide) return true;
    
    if (state.rideStatus === 'passenger_onboard' || state.rideStatus === 'arrived_at_destination') {
      return false;
    }
    
    return state.queuedBookings.length < state.maxQueueSize;
  };

  const getMaxQueueSize = (): number => {
    return state.maxQueueSize;
  };

  const startNextQueuedRide = () => {
    if (state.queuedBookings.length > 0) {
      const nextRide = state.queuedBookings[0];
      dispatch({ type: 'SET_CURRENT_RIDE', payload: nextRide });
      dispatch({ type: 'REMOVE_QUEUED_BOOKING', payload: nextRide.id });
      dispatch({ type: 'SET_RIDE_STATUS', payload: 'heading_to_pickup' });
      dispatch({ type: 'SET_QUEUE_STATUS', payload: 'limited_queue' });
      return nextRide;
    } else {
      dispatch({ type: 'SET_QUEUE_STATUS', payload: 'available' });
      return null;
    }
  };

  const setAvailableRides = (rides: Ride[]) => {
    dispatch({ type: 'SET_AVAILABLE_RIDES', payload: rides });
  };

  const removeAvailableRide = (rideId: string) => {
    dispatch({ type: 'REMOVE_AVAILABLE_RIDE', payload: rideId });
  };

  // Unified booking management methods
  const getAllBookings = (): Ride[] => {
    return [
      ...state.availableRides,
      ...state.queuedBookings,
      ...(state.currentRide ? [state.currentRide] : []),
      ...state.pendingBookings
    ];
  };

  const getBookingsByStatus = (status: 'pending' | 'accepted' | 'ongoing' | 'completed' | 'cancelled' | 'rejected'): Ride[] => {
    return getAllBookings().filter(ride => ride.status === status);
  };

  const getBookingsByType = (bookingStatus: 'new' | 'urgent' | 'scheduled' | 'popular' | 'premium'): Ride[] => {
    return getAllBookings().filter(ride => ride.bookingStatus === bookingStatus);
  };

  const getActiveBookings = (): Ride[] => {
    return getAllBookings().filter(ride => 
      ride.status === 'pending' && (ride.bookingStatus === 'urgent' || ride.bookingStatus === 'new')
    );
  };

  const getScheduledBookings = (): Ride[] => {
    return getAllBookings().filter(ride => 
      ride.bookingStatus === 'scheduled' && ride.scheduledTime
    );
  };

  const acceptBooking = (rideId: string): boolean => {
    const ride = getAllBookings().find(r => r.id === rideId);
    if (!ride || !canAcceptNewBooking()) return false;

    if (state.currentRide) {
      addQueuedBooking(ride);
    } else {
      setCurrentRide(ride);
      setRideStatus('heading_to_pickup');
      setQueueStatus('limited_queue');
    }
    
    removeAvailableRide(rideId);
    return true;
  };

  const rejectBooking = (rideId: string): void => {
    const updatedRides = state.availableRides.map(ride => 
      ride.id === rideId 
        ? { ...ride, status: 'rejected' as const, rejectedAt: new Date() }
        : ride
    );
    dispatch({ type: 'SET_AVAILABLE_RIDES', payload: updatedRides });
  };

  const completeBooking = (rideId: string): void => {
    const ride = getAllBookings().find(r => r.id === rideId);
    if (!ride) return;

    const trip = {
      id: `trip-${Date.now()}`,
      rideId: ride.id,
      earnings: ride.fare,
      distance: ride.distance,
      duration: ride.duration,
      completedAt: new Date()
    };

    addTrip(trip);
    
    if (state.currentRide?.id === rideId) {
      setCurrentRide(null);
      setRideStatus(null);
      startNextQueuedRide();
    } else {
      removeQueuedBooking(rideId);
    }
  };
  
  // API State Management methods
  const setLoading = (section: keyof ApiLoadingState, loading: boolean) => {
    dispatch({ type: 'SET_LOADING', payload: { section, loading } });
  };

  const setError = (section: keyof ApiErrorState, error: string | null) => {
    dispatch({ type: 'SET_ERROR', payload: { section, error } });
  };

  const clearErrors = () => {
    const errorKeys: (keyof ApiErrorState)[] = [
      'auth', 'rides', 'trips', 'driver', 'earnings', 'bookings', 'navigation', 'communication', 'network'
    ];
    errorKeys.forEach(key => {
      dispatch({ type: 'SET_ERROR', payload: { section: key, error: null } });
    });
  };

  const setConnectionState = (connectionState: Partial<ConnectionState>) => {
    dispatch({ type: 'SET_CONNECTION_STATE', payload: connectionState });
  };

  const isDataStale = (section: keyof CacheState, maxAge: number = 5 * 60 * 1000): boolean => {
    const cached = state.cache[section];
    if (!cached) return true;
    
    const age = Date.now() - cached.timestamp;
    return age > maxAge;
  };

  const addPendingAction = (actionType: string, payload: any) => {
    dispatch({ type: 'ADD_PENDING_ACTION', payload: { type: actionType, payload } });
  };

  const clearPendingActions = () => {
    state.sync.pendingActions.forEach(action => {
      dispatch({ type: 'REMOVE_PENDING_ACTION', payload: action.id });
    });
  };

  const setSyncStatus = (isSyncing: boolean, lastSyncTime?: Date) => {
    dispatch({ type: 'SET_SYNC_STATUS', payload: { isSyncing, lastSyncTime } });
  };

  const generateMockRides = () => {
    if (state.mockDataGenerated) return;

    // Use the standardized mock data generator from mockData.ts
    const mockRides = generateMockAvailableRides();

    dispatch({ type: 'SET_AVAILABLE_RIDES', payload: mockRides });
    dispatch({ type: 'SET_MOCK_DATA_GENERATED', payload: true });
  };

  return (
    <GlobalContext.Provider value={{ 
      state, 
      login,
      logout,
      updateDriver,
      setLocation, 
      setCurrentRide, 
      addTrip, 
      updateEarnings,
      setAvailableDrivers,
      toggleSidebar,
      setMapRegion,
      addPendingBooking,
      removePendingBooking,
      setNavigationVisibility,
      setOnlineStatus,
      setQueueStatus,
      addQueuedBooking,
      removeQueuedBooking,
      setQueuedBookings,
      setRideStatus,
      shouldShowBookings,
      canAcceptNewBooking,
      getMaxQueueSize,
      startNextQueuedRide,
      setAvailableRides,
      removeAvailableRide,
      generateMockRides,
      // Unified booking management
      getAllBookings,
      getBookingsByStatus,
      getBookingsByType,
      getActiveBookings,
      getScheduledBookings,
      acceptBooking,
      rejectBooking,
      completeBooking,
      // API State Management methods
      setLoading,
      setError,
      clearErrors,
      setConnectionState,
      isDataStale,
      addPendingAction,
      clearPendingActions,
      setSyncStatus,
    }}>
      {children}
    </GlobalContext.Provider>
  );
};

export const useGlobalState = () => {
  const context = useContext(GlobalContext);
  if (context === undefined) {
    throw new Error('useGlobalState must be used within a GlobalStateProvider');
  }
  return context;
};

export const useAuth = () => {
  const { state, login, logout, updateDriver, setLoading, setError } = useGlobalState();
  return {
    state: {
      isAuthenticated: state.isAuthenticated,
      driver: state.driver,
      token: state.token,
      loading: state.loading.auth,
      error: state.errors.auth,
    },
    login,
    logout,
    updateDriver,
    setLoading: (loading: boolean) => setLoading('auth', loading),
    setError: (error: string | null) => setError('auth', error),
  };
};

export const useApp = () => {
  const { state, ...methods } = useGlobalState();
  return {
    state: {
      currentRide: state.currentRide,
      location: state.location,
      trips: state.trips,
      totalEarnings: state.totalEarnings,
      pendingBookings: state.pendingBookings,
      weeklyEarnings: state.weeklyEarnings,
      todayTrips: state.todayTrips,
      availableDrivers: state.availableDrivers,
      sidebarOpen: state.sidebarOpen,
      navigationVisible: state.navigationVisible,
      isOnline: state.isOnline,
      queueStatus: state.queueStatus,
      queuedBookings: state.queuedBookings,
      maxQueueSize: state.maxQueueSize,
      rideStatus: state.rideStatus,
      availableRides: state.availableRides,
      mockDataGenerated: state.mockDataGenerated,
      mapRegion: state.mapRegion,
      // API State
      loading: state.loading,
      errors: state.errors,
      connection: state.connection,
      cache: state.cache,
      sync: state.sync,
    },
    setLocation: methods.setLocation,
    setCurrentRide: methods.setCurrentRide,
    addTrip: methods.addTrip,
    updateEarnings: methods.updateEarnings,
    setAvailableDrivers: methods.setAvailableDrivers,
    toggleSidebar: methods.toggleSidebar,
    setMapRegion: methods.setMapRegion,
    addPendingBooking: methods.addPendingBooking,
    removePendingBooking: methods.removePendingBooking,
    setNavigationVisibility: methods.setNavigationVisibility,
    setOnlineStatus: methods.setOnlineStatus,
    setQueueStatus: methods.setQueueStatus,
    addQueuedBooking: methods.addQueuedBooking,
    removeQueuedBooking: methods.removeQueuedBooking,
    setQueuedBookings: methods.setQueuedBookings,
    setRideStatus: methods.setRideStatus,
    shouldShowBookings: methods.shouldShowBookings,
    canAcceptNewBooking: methods.canAcceptNewBooking,
    getMaxQueueSize: methods.getMaxQueueSize,
    startNextQueuedRide: methods.startNextQueuedRide,
    setAvailableRides: methods.setAvailableRides,
    removeAvailableRide: methods.removeAvailableRide,
    generateMockRides: methods.generateMockRides,
    // Unified booking management
    getAllBookings: methods.getAllBookings,
    getBookingsByStatus: methods.getBookingsByStatus,
    getBookingsByType: methods.getBookingsByType,
    getActiveBookings: methods.getActiveBookings,
    getScheduledBookings: methods.getScheduledBookings,
    acceptBooking: methods.acceptBooking,
    rejectBooking: methods.rejectBooking,
    completeBooking: methods.completeBooking,
    // API State Management
    setLoading: methods.setLoading,
    setError: methods.setError,
    clearErrors: methods.clearErrors,
    setConnectionState: methods.setConnectionState,
    isDataStale: methods.isDataStale,
    addPendingAction: methods.addPendingAction,
    clearPendingActions: methods.clearPendingActions,
    setSyncStatus: methods.setSyncStatus,
  };
};

// New dedicated API state hook
export const useApiState = () => {
  const { state, setLoading, setError, clearErrors, setConnectionState, isDataStale, addPendingAction, clearPendingActions, setSyncStatus } = useGlobalState();
  return {
    loading: state.loading,
    errors: state.errors,
    connection: state.connection,
    cache: state.cache,
    sync: state.sync,
    setLoading,
    setError,
    clearErrors,
    setConnectionState,
    isDataStale,
    addPendingAction,
    clearPendingActions,
    setSyncStatus,
  };
};