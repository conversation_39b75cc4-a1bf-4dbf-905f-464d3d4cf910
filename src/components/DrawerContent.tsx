import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { DrawerContentScrollView, DrawerContentComponentProps } from '@react-navigation/drawer';
import { SafeAreaView } from 'react-native-safe-area-context';
import { MaterialIcons } from '@expo/vector-icons';
import { useAuth, useApp } from '../context/ApiIntegratedGlobalStateContext';

type MaterialIconName = 
  | 'dashboard' 
  | 'map'
  | 'assignment' 
  | 'attach-money' 
  | 'person' 
  | 'directions-car' 
  | 'settings' 
  | 'help' 
  | 'logout'
  | 'close';

interface MenuItem {
  id: string;
  title: string;
  icon: MaterialIconName;
  badge?: number;
  screenName: string;
}

const DrawerContent: React.FC<DrawerContentComponentProps> = (props) => {
  const { state: authState } = useAuth();
  const { state: appState } = useApp();

  // Calculate today's earnings
  const todayEarnings = React.useMemo(() => {
    if (!appState.trips || appState.trips.length === 0) {
      return '0.00';
    }
    
    try {
      const today = new Date();
      const todayTotal = appState.trips
        .filter(trip => {
          if (!trip.completedAt) return false;
          const tripDate = new Date(trip.completedAt);
          return tripDate.toDateString() === today.toDateString();
        })
        .reduce((sum, trip) => sum + trip.earnings, 0);
      
      return todayTotal.toFixed(2);
    } catch (error) {
      console.error('Error calculating earnings:', error);
      return '0.00';
    }
  }, [appState.trips]);

  const mainMenuItems: MenuItem[] = [
    { id: 'dashboard', title: 'Dashboard', icon: 'dashboard', screenName: 'Dashboard' },
    { id: 'map', title: 'Navigation', icon: 'map', screenName: 'Map' },
    { id: 'bookings', title: 'Bookings', icon: 'assignment', badge: appState.pendingBookings?.length || 0, screenName: 'Bookings' },
    { id: 'earnings', title: 'Earnings', icon: 'attach-money', screenName: 'Earnings' },
    { id: 'status', title: 'Driver Status', icon: 'directions-car', screenName: 'DriverStatus' },
  ];

  const additionalMenuItems: MenuItem[] = [
    { id: 'profile', title: 'Profile', icon: 'person', screenName: 'Profile' },
    { id: 'settings', title: 'Settings', icon: 'settings', screenName: 'Settings' },
    { id: 'help', title: 'Help & Support', icon: 'help', screenName: 'Help' },
  ];

  const handleItemPress = (item: MenuItem) => {
    props.navigation.navigate(item.screenName);
    props.navigation.closeDrawer();
  };

  const handleLogout = () => {
    // Handle logout logic here
    props.navigation.closeDrawer();
    // You might want to call a logout function from your auth context
  };

  const renderMenuItem = (item: MenuItem, isActive: boolean) => (
    <TouchableOpacity
      key={item.id}
      style={[styles.menuItem, isActive && styles.activeMenuItem]}
      onPress={() => handleItemPress(item)}
    >
      <View style={styles.menuItemContent}>
        <View style={styles.menuItemLeft}>
          <MaterialIcons 
            name={item.icon} 
            size={20} 
            color={isActive ? '#007AFF' : '#6D6D70'} 
            style={styles.menuItemIcon}
          />
          <Text style={[styles.menuItemText, isActive && styles.activeMenuItemText]}>
            {item.title}
          </Text>
        </View>
        {item.badge && item.badge > 0 ? (
          <View style={styles.badge}>
            <Text style={styles.badgeText}>{String(item.badge)}</Text>
          </View>
        ) : null}
      </View>
    </TouchableOpacity>
  );

  return (
    <SafeAreaView style={styles.container}>
      <DrawerContentScrollView {...props} style={styles.scrollView}>
        {/* Header */}
        <View style={styles.header}>
          <View style={styles.driverInfo}>
            <Text style={styles.driverName}>{authState.driver?.name || 'Driver'}</Text>
            <Text style={styles.driverRating}>Online</Text>
            <View style={styles.statusIndicator}>
              <View style={[styles.statusDot, { backgroundColor: '#34C759' }]} />
              <Text style={styles.statusText}>Available</Text>
            </View>
          </View>
          <TouchableOpacity onPress={() => props.navigation.closeDrawer()} style={styles.closeButton}>
            <MaterialIcons name="close" size={16} color="#6D6D70" />
          </TouchableOpacity>
        </View>

        {/* Main Menu Items */}
        <View style={styles.menu}>
          {mainMenuItems.map((item) => {
            const isActive = props.state.routes[props.state.index]?.name === item.screenName;
            return renderMenuItem(item, isActive);
          })}
          
          <View style={styles.menuSeparator} />
          
          {additionalMenuItems.map((item) => {
            const isActive = props.state.routes[props.state.index]?.name === item.screenName;
            return renderMenuItem(item, isActive);
          })}

          {/* Logout */}
          <TouchableOpacity style={styles.menuItem} onPress={handleLogout}>
            <View style={styles.menuItemContent}>
              <View style={styles.menuItemLeft}>
                <MaterialIcons 
                  name="logout" 
                  size={20} 
                  color="#6D6D70" 
                  style={styles.menuItemIcon}
                />
                <Text style={styles.menuItemText}>Logout</Text>
              </View>
            </View>
          </TouchableOpacity>
        </View>
      </DrawerContentScrollView>

      {/* Footer */}
      <View style={styles.footer}>
        <View style={styles.todayStats}>
          <Text style={styles.footerTitle}>Today's Stats</Text>
          <View style={styles.statsRow}>
            <View style={styles.statItem}>
              <Text style={styles.statValue}>{String(appState.todayTrips || 0)}</Text>
              <Text style={styles.statLabel}>Trips</Text>
            </View>
            <View style={styles.statItem}>
              <Text style={styles.statValue}>£{todayEarnings}</Text>
              <Text style={styles.statLabel}>Earnings</Text>
            </View>
          </View>
        </View>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  scrollView: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E5EA',
  },
  driverInfo: {
    flex: 1,
  },
  driverName: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#1D1D1F',
    marginBottom: 4,
  },
  driverRating: {
    fontSize: 14,
    color: '#34C759',
    fontWeight: '500',
    marginBottom: 8,
  },
  statusIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statusDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginRight: 6,
  },
  statusText: {
    fontSize: 12,
    color: '#6D6D70',
    fontWeight: '500',
  },
  closeButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: '#F2F2F7',
    justifyContent: 'center',
    alignItems: 'center',
  },
  menu: {
    flex: 1,
    paddingVertical: 20,
  },
  menuItem: {
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderRadius: 0,
  },
  activeMenuItem: {
    backgroundColor: '#F0F8FF',
    borderRightWidth: 3,
    borderRightColor: '#007AFF',
  },
  menuItemContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  menuItemLeft: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  menuItemIcon: {
    marginRight: 12,
  },
  menuItemText: {
    fontSize: 16,
    color: '#1D1D1F',
    fontWeight: '500',
  },
  activeMenuItemText: {
    color: '#007AFF',
    fontWeight: '600',
  },
  badge: {
    backgroundColor: '#FF3B30',
    borderRadius: 10,
    minWidth: 20,
    height: 20,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 6,
  },
  badgeText: {
    color: '#FFFFFF',
    fontSize: 12,
    fontWeight: 'bold',
  },
  menuSeparator: {
    height: 1,
    backgroundColor: '#E5E5EA',
    marginHorizontal: 20,
    marginVertical: 8,
  },
  footer: {
    padding: 20,
    borderTopWidth: 1,
    borderTopColor: '#E5E5EA',
  },
  todayStats: {
    alignItems: 'center',
  },
  footerTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: '#1D1D1F',
    marginBottom: 12,
  },
  statsRow: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    width: '100%',
  },
  statItem: {
    alignItems: 'center',
  },
  statValue: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#34C759',
  },
  statLabel: {
    fontSize: 12,
    color: '#6D6D70',
    marginTop: 2,
  },
});

export default DrawerContent;