import React, { useState, useEffect } from 'react';
import { View, StyleSheet, Alert, Text, TouchableOpacity } from 'react-native';
import MapView, { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, PROVIDER_GOOGLE } from 'react-native-maps';
import * as Location from 'expo-location';

interface NavigationProps {
  destination: { latitude: number; longitude: number };
  destinationTitle?: string;
  onNavigationProgress?: (progress: any) => void;
}

interface RouteCoordinates {
  latitude: number;
  longitude: number;
}

export const Navigation: React.FC<NavigationProps> = ({
  destination,
  destinationTitle = "Destination",
  onNavigationProgress
}) => {
  const [currentLocation, setCurrentLocation] = useState<{ latitude: number; longitude: number } | null>(null);
  const [routeCoordinates, setRouteCoordinates] = useState<RouteCoordinates[]>([]);
  const [mapRegion, setMapRegion] = useState({
    latitude: destination.latitude,
    longitude: destination.longitude,
    latitudeDelta: 0.01,
    longitudeDelta: 0.01,
  });
  const [routeInfo, setRouteInfo] = useState<{ distance: string; duration: string } | null>(null);

  // Get current location
  const getCurrentLocation = async () => {
    try {
      const { status } = await Location.requestForegroundPermissionsAsync();
      if (status !== 'granted') {
        Alert.alert('Permission denied', 'Location permission is required for navigation');
        return;
      }

      const location = await Location.getCurrentPositionAsync({
        accuracy: Location.Accuracy.High,
      });

      const coords = {
        latitude: location.coords.latitude,
        longitude: location.coords.longitude,
      };

      setCurrentLocation(coords);
      return coords;
    } catch (error) {
      console.error('Error getting location:', error);
      Alert.alert('Error', 'Could not get your current location');
    }
  };

  // Get directions using Google Directions API
  const getDirections = async (origin: { latitude: number; longitude: number }) => {
    try {
      const apiKey = 'AIzaSyBQyfTOFI2Rn28Gqy2CjeZ8IdxznKhsSQQ';
      const url = `https://maps.googleapis.com/maps/api/directions/json?origin=${origin.latitude},${origin.longitude}&destination=${destination.latitude},${destination.longitude}&key=${apiKey}`;
      
      const response = await fetch(url);
      const data = await response.json();
      
      if (data.routes && data.routes.length > 0) {
        const route = data.routes[0];
        const points = route.overview_polyline.points;
        const decodedPoints = decodePolyline(points);
        setRouteCoordinates(decodedPoints);
        
        // Set route info
        setRouteInfo({
          distance: route.legs[0].distance.text,
          duration: route.legs[0].duration.text,
        });

        // Calculate region to fit both points
        const minLat = Math.min(origin.latitude, destination.latitude);
        const maxLat = Math.max(origin.latitude, destination.latitude);
        const minLng = Math.min(origin.longitude, destination.longitude);
        const maxLng = Math.max(origin.longitude, destination.longitude);
        
        setMapRegion({
          latitude: (minLat + maxLat) / 2,
          longitude: (minLng + maxLng) / 2,
          latitudeDelta: (maxLat - minLat) * 1.5,
          longitudeDelta: (maxLng - minLng) * 1.5,
        });

        // Notify progress if callback provided
        if (onNavigationProgress) {
          onNavigationProgress({
            distance: route.legs[0].distance.text,
            duration: route.legs[0].duration.text,
            route: decodedPoints
          });
        }
      }
    } catch (error) {
      console.error('Error fetching directions:', error);
      Alert.alert('Error', 'Could not get directions');
    }
  };

  // Polyline decoder for Google Maps
  const decodePolyline = (encoded: string): RouteCoordinates[] => {
    const coordinates: RouteCoordinates[] = [];
    let index = 0;
    let lat = 0;
    let lng = 0;

    while (index < encoded.length) {
      let b;
      let shift = 0;
      let result = 0;
      do {
        b = encoded.charCodeAt(index++) - 63;
        result |= (b & 0x1f) << shift;
        shift += 5;
      } while (b >= 0x20);
      const dlat = ((result & 1) !== 0 ? ~(result >> 1) : (result >> 1));
      lat += dlat;

      shift = 0;
      result = 0;
      do {
        b = encoded.charCodeAt(index++) - 63;
        result |= (b & 0x1f) << shift;
        shift += 5;
      } while (b >= 0x20);
      const dlng = ((result & 1) !== 0 ? ~(result >> 1) : (result >> 1));
      lng += dlng;

      coordinates.push({
        latitude: lat / 1e5,
        longitude: lng / 1e5,
      });
    }

    return coordinates;
  };

  // Start navigation
  const startNavigation = async () => {
    const location = await getCurrentLocation();
    if (location) {
      await getDirections(location);
    }
  };

  useEffect(() => {
    startNavigation();
  }, [destination]);

  return (
    <View style={styles.container}>
      {/* Route Info */}
      {routeInfo && (
        <View style={styles.routeInfo}>
          <Text style={styles.routeText}>Distance: {routeInfo.distance}</Text>
          <Text style={styles.routeText}>Duration: {routeInfo.duration}</Text>
        </View>
      )}

      <MapView
        provider={PROVIDER_GOOGLE}
        style={styles.map}
        region={mapRegion}
        showsUserLocation={true}
        showsMyLocationButton={true}
        showsTraffic={true}
        onRegionChangeComplete={setMapRegion}
      >
        {/* Current location marker */}
        {currentLocation && (
          <Marker
            coordinate={currentLocation}
            title="Your Location"
            description="Current position"
            pinColor="blue"
          />
        )}

        {/* Destination marker */}
        <Marker
          coordinate={destination}
          title={destinationTitle}
          description="Destination"
          pinColor="red"
        />

        {/* Route polyline */}
        {routeCoordinates.length > 0 && (
          <Polyline
            coordinates={routeCoordinates}
            strokeColor="#4285F4"
            strokeWidth={5}
            strokePattern={[1]}
          />
        )}
      </MapView>

      {/* Refresh button */}
      <TouchableOpacity style={styles.refreshButton} onPress={startNavigation}>
        <Text style={styles.refreshText}>Refresh Route</Text>
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  map: {
    flex: 1,
  },
  routeInfo: {
    position: 'absolute',
    top: 50,
    left: 20,
    right: 20,
    backgroundColor: 'white',
    padding: 15,
    borderRadius: 10,
    zIndex: 1,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  routeText: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 5,
  },
  refreshButton: {
    position: 'absolute',
    bottom: 30,
    right: 20,
    backgroundColor: '#4285F4',
    padding: 15,
    borderRadius: 25,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  refreshText: {
    color: 'white',
    fontWeight: '600',
  },
});

export default Navigation;