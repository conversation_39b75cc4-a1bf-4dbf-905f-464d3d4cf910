import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet } from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';

// Define valid icon names for MaterialIcons
type MaterialIconName = 
  | 'dashboard' 
  | 'map'
  | 'assignment' 
  | 'attach-money' 
  | 'person' 
  | 'directions-car' 
  | 'settings' 
  | 'help' 
  | 'logout'
  | 'close';

interface MenuItem {
  id: string;
  title: string;
  icon: MaterialIconName;
  badge?: number;
}

interface SidebarMenuItemProps {
  item: MenuItem;
  isActive: boolean;
  onPress: () => void;
}

const SidebarMenuItem: React.FC<SidebarMenuItemProps> = ({ item, isActive, onPress }) => {
  return (
    <TouchableOpacity
      style={[styles.menuItem, isActive && styles.activeMenuItem]}
      onPress={onPress}
    >
      <View style={styles.menuItemContent}>
        <View style={styles.menuItemLeft}>
          <MaterialIcons 
            name={item.icon} 
            size={20} 
            color={isActive ? '#007AFF' : '#6D6D70'} 
            style={styles.menuItemIcon}
          />
          <Text style={[styles.menuItemText, isActive && styles.activeMenuItemText]}>
            {item.title}
          </Text>
        </View>
        {item.badge && item.badge > 0 ? (
          <View style={styles.badge}>
            <Text style={styles.badgeText}>{String(item.badge)}</Text>
          </View>
        ) : null}
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  menuItem: {
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderRadius: 0,
  },
  activeMenuItem: {
    backgroundColor: '#F0F8FF',
    borderRightWidth: 3,
    borderRightColor: '#007AFF',
  },
  menuItemContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  menuItemLeft: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  menuItemIcon: {
    marginRight: 12,
  },
  menuItemText: {
    fontSize: 16,
    color: '#1D1D1F',
    fontWeight: '500',
  },
  activeMenuItemText: {
    color: '#007AFF',
    fontWeight: '600',
  },
  badge: {
    backgroundColor: '#FF3B30',
    borderRadius: 10,
    minWidth: 20,
    height: 20,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 6,
  },
  badgeText: {
    color: '#FFFFFF',
    fontSize: 12,
    fontWeight: 'bold',
  },
});

export default SidebarMenuItem;