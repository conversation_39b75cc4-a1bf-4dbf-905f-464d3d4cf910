import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Modal } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { MaterialIcons } from '@expo/vector-icons';
import { useAuth, useApp } from '../context/ApiIntegratedGlobalStateContext';
import SidebarMenuItem from './SidebarMenuItem.backup';
import { navigateToTab } from '../services/AppNavigationService';

// Define valid icon names for MaterialIcons
type MaterialIconName = 
  | 'dashboard' 
  | 'map'
  | 'assignment' 
  | 'attach-money' 
  | 'person' 
  | 'directions-car' 
  | 'settings' 
  | 'help' 
  | 'logout'
  | 'close';

interface MenuItem {
  id: string;
  title: string;
  icon: MaterialIconName;
  badge?: number;
}

interface SidebarProps {
  visible: boolean;
  onClose: () => void;
  onNavigate: (screen: string) => void;
  currentScreen: string;
}

const Sidebar = React.memo<SidebarProps>(({ visible, onClose, onNavigate, currentScreen }) => {
  const { state: authState } = useAuth();
  const { state: appState } = useApp();

  // Calculate today's earnings - moved before conditional return
  const todayEarnings = React.useMemo(() => {
    if (!appState.trips || appState.trips.length === 0) {
      return '0.00';
    }
    
    try {
      const today = new Date();
      const todayTotal = appState.trips
        .filter(trip => {
          if (!trip.completedAt) return false;
          const tripDate = new Date(trip.completedAt);
          return tripDate.toDateString() === today.toDateString();
        })
        .reduce((sum, trip) => sum + trip.earnings, 0);
      
      return todayTotal.toFixed(2);
    } catch (error) {
      console.error('Error calculating earnings:', error);
      return '0.00';
    }
  }, [appState.trips]);

  const mainMenuItems: MenuItem[] = [
    { id: 'dashboard', title: 'Dashboard', icon: 'dashboard' },
    { id: 'map', title: 'Navigation', icon: 'map' },
    { id: 'bookings', title: 'Bookings', icon: 'assignment', badge: appState.pendingBookings?.length || 0 },
    { id: 'earnings', title: 'Earnings', icon: 'attach-money' },
    { id: 'status', title: 'Driver Status', icon: 'directions-car' },
  ];

  const additionalMenuItems: MenuItem[] = [
    { id: 'profile', title: 'Profile', icon: 'person' },
    { id: 'settings', title: 'Settings', icon: 'settings' },
    { id: 'help', title: 'Help & Support', icon: 'help' },
    { id: 'logout', title: 'Logout', icon: 'logout' },
  ];

  const handleItemPress = (screenId: string) => {
    // First use the local navigation prop
    onNavigate(screenId);
    
    // Close the sidebar
    onClose();
  };

  if (!visible) {
    return null;
  }

  return (
    <Modal
      visible={visible}
      animationType="slide"
      transparent={true}
      onRequestClose={onClose}
    >
      <View style={styles.overlay}>
        <SafeAreaView style={styles.sidebar}>
          <View style={styles.header}>
            <View style={styles.driverInfo}>
              <Text style={styles.driverName}>{authState.driver?.name || 'Driver'}</Text>
              <Text style={styles.driverRating}>Online</Text>
              <View style={styles.statusIndicator}>
                <View style={[styles.statusDot, { backgroundColor: '#34C759' }]} />
                <Text style={styles.statusText}>Available</Text>
              </View>
            </View>
            <TouchableOpacity onPress={onClose} style={styles.closeButton}>
              <MaterialIcons name="close" size={16} color="#6D6D70" />
            </TouchableOpacity>
          </View>

          <View style={styles.menu}>
            {mainMenuItems.map((item) => (
              <SidebarMenuItem
                key={item.id}
                item={item}
                isActive={currentScreen === item.id}
                onPress={() => handleItemPress(item.id)}
              />
            ))}
            
            <View style={styles.menuSeparator} />
            
            {additionalMenuItems.map((item) => (
              <SidebarMenuItem
                key={item.id}
                item={item}
                isActive={currentScreen === item.id}
                onPress={() => handleItemPress(item.id)}
              />
            ))}
          </View>

          <View style={styles.footer}>
            <View style={styles.todayStats}>
              <Text style={styles.footerTitle}>Today's Stats</Text>
              <View style={styles.statsRow}>
                <View style={styles.statItem}>
                  <Text style={styles.statValue}>{String(appState.todayTrips || 0)}</Text>
                  <Text style={styles.statLabel}>Trips</Text>
                </View>
                <View style={styles.statItem}>
                  <Text style={styles.statValue}>
                    {`£${todayEarnings}`}
                  </Text>
                  <Text style={styles.statLabel}>Earnings</Text>
                </View>
              </View>
            </View>
          </View>
        </SafeAreaView>
        <TouchableOpacity 
          style={styles.overlayBackground} 
          onPress={onClose}
          activeOpacity={1}
        />
      </View>
    </Modal>
  );
});

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    flexDirection: 'row',
  },
  overlayBackground: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  sidebar: {
    width: 280,
    backgroundColor: '#FFFFFF',
    borderTopRightRadius: 20,
    borderBottomRightRadius: 20,
    shadowColor: '#000',
    shadowOffset: { width: 2, height: 0 },
    shadowOpacity: 0.1,
    shadowRadius: 10,
    elevation: 5,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E5EA',
  },
  driverInfo: {
    flex: 1,
  },
  driverName: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#1D1D1F',
    marginBottom: 4,
  },
  driverRating: {
    fontSize: 14,
    color: '#34C759',
    fontWeight: '500',
    marginBottom: 8,
  },
  statusIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statusDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginRight: 6,
  },
  statusText: {
    fontSize: 12,
    color: '#6D6D70',
    fontWeight: '500',
  },
  closeButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: '#F2F2F7',
    justifyContent: 'center',
    alignItems: 'center',
  },
  menu: {
    flex: 1,
    paddingVertical: 20,
  },
  // Menu item styles moved to SidebarMenuItem component
  menuSeparator: {
    height: 1,
    backgroundColor: '#E5E5EA',
    marginHorizontal: 20,
    marginVertical: 8,
  },
  footer: {
    padding: 20,
    borderTopWidth: 1,
    borderTopColor: '#E5E5EA',
  },
  todayStats: {
    alignItems: 'center',
  },
  footerTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: '#1D1D1F',
    marginBottom: 12,
  },
  statsRow: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    width: '100%',
  },
  statItem: {
    alignItems: 'center',
  },
  statValue: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#34C759',
  },
  statLabel: {
    fontSize: 12,
    color: '#6D6D70',
    marginTop: 2,
  },
});

export default Sidebar;