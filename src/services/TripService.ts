import { apiService, ApiResponse } from './ApiService';
import { API_CONFIG } from '../config/api.config';
import { Trip, Ride, Location } from '../types';
import { mockData } from '../data/mockData';

// Trip service types
export interface TripHistoryQuery {
  driverId: string;
  startDate?: Date;
  endDate?: Date;
  status?: 'completed' | 'cancelled';
  limit?: number;
  offset?: number;
  sortBy?: 'date' | 'earnings' | 'distance' | 'duration';
  sortOrder?: 'asc' | 'desc';
}

export interface TripHistoryResponse {
  trips: ExtendedTrip[];
  total: number;
  hasMore: boolean;
  nextOffset?: number;
  summary: {
    totalTrips: number;
    totalEarnings: number;
    totalDistance: number;
    totalDuration: number;
    averageRating: number;
  };
}

export interface ExtendedTrip extends Trip {
  ride: Ride;
  startTime: Date;
  endTime: Date;
  route?: {
    polyline: string;
    waypoints: Location[];
    totalDistance: number;
    actualDistance: number;
  };
  rating?: {
    score: number;
    feedback?: string;
  };
  payment: {
    method: 'cash' | 'card' | 'wallet';
    status: 'pending' | 'completed' | 'failed';
    baseFare: number;
    tips: number;
    surge: number;
    fees: number;
    total: number;
  };
  issues?: {
    type: string;
    description: string;
    reportedAt: Date;
    resolved: boolean;
  }[];
}

export interface CompleteRideRequest {
  rideId: string;
  driverId: string;
  endLocation: Location;
  actualDistance?: number;
  actualDuration?: number;
  route?: {
    polyline: string;
    waypoints: Location[];
  };
  notes?: string;
  imageUrls?: string[];
}

export interface EarningsQuery {
  driverId: string;
  period: 'today' | 'week' | 'month' | 'year' | 'custom';
  startDate?: Date;
  endDate?: Date;
  groupBy?: 'day' | 'week' | 'month';
}

export interface EarningsResponse {
  period: string;
  totalEarnings: number;
  breakdown: {
    baseFares: number;
    tips: number;
    bonuses: number;
    surge: number;
    fees: number;
    deductions: number;
  };
  tripCount: number;
  onlineHours: number;
  metrics: {
    earningsPerTrip: number;
    earningsPerHour: number;
    averageDistance: number;
    averageDuration: number;
    averageRating: number;
  };
  chartData?: {
    date: string;
    earnings: number;
    trips: number;
    hours: number;
  }[];
}

export interface EarningsAnalytics {
  trends: {
    daily: { date: string; earnings: number; trips: number }[];
    weekly: { week: string; earnings: number; trips: number }[];
    monthly: { month: string; earnings: number; trips: number }[];
  };
  comparisons: {
    previousPeriod: {
      earnings: number;
      change: number;
      percentage: number;
    };
    averageDriver: {
      earnings: number;
      ranking: number;
      percentile: number;
    };
  };
  projections: {
    dailyTarget: number;
    weeklyTarget: number;
    monthlyTarget: number;
    expectedMonthly: number;
  };
}

export interface TripFilters {
  dateRange: {
    start: Date;
    end: Date;
  };
  earningsRange: {
    min: number;
    max: number;
  };
  distanceRange: {
    min: number;
    max: number;
  };
  durationRange: {
    min: number;
    max: number;
  };
  transferTypes: ('arrival' | 'departure')[];
  airports: string[];
  vehicleTypes: string[];
  paymentMethods: ('cash' | 'card' | 'wallet')[];
  ratingRange: {
    min: number;
    max: number;
  };
}

class TripService {
  private static instance: TripService;

  public static getInstance(): TripService {
    if (!TripService.instance) {
      TripService.instance = new TripService();
    }
    return TripService.instance;
  }

  /**
   * Complete a ride and create a trip record
   */
  async completeRide(request: CompleteRideRequest): Promise<ExtendedTrip> {
    // Force mock mode for trip service (only auth uses real API)
    if (API_CONFIG.FORCE_MOCK_NON_AUTH || API_CONFIG.FORCE_MOCK_MODE || API_CONFIG.DISABLE_API_CALLS) {
      console.log('🎭 Mock: Completing ride', request.rideId);
      // Generate a mock completed trip
      const mockTrip: ExtendedTrip = {
        id: `trip-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
        rideId: request.rideId,
        earnings: Math.round((15 + Math.random() * 35) * 100) / 100,
        distance: request.actualDistance || Math.round((3 + Math.random() * 15) * 10) / 10,
        duration: request.actualDuration || Math.round(20 + Math.random() * 40),
        completedAt: new Date(),
        ride: {
          id: request.rideId,
          passengerId: `passenger-${Math.floor(Math.random() * 100)}`,
          passengerName: ['Emma Johnson', 'Michael Chen', 'Sarah Williams'][Math.floor(Math.random() * 3)],
          passengerPhone: '******-0000',
          pickupLocation: request.endLocation,
          dropoffLocation: request.endLocation,
          status: 'completed' as const,
          bookingStatus: 'new' as const,
          fare: Math.round((15 + Math.random() * 35) * 100) / 100,
          distance: request.actualDistance || Math.round((3 + Math.random() * 15) * 10) / 10,
          duration: request.actualDuration || Math.round(20 + Math.random() * 40),
          paymentMethod: 'card' as const,
          createdAt: new Date(),
          priority: 'medium' as const,
          transferType: 'arrival' as const,
          vehicleType: 'saloon' as const,
          passengerCount: 1,
          hasMeetAndGreet: false,
          airport: 'San Francisco International (SFO)',
        },
        startTime: new Date(Date.now() - (request.actualDuration || 30) * 60 * 1000),
        endTime: new Date(),
        payment: {
          method: 'card' as const,
          status: 'completed' as const,
          baseFare: Math.round((15 + Math.random() * 35) * 0.8 * 100) / 100,
          tips: Math.round((15 + Math.random() * 35) * 0.15 * 100) / 100,
          surge: 0,
          fees: Math.round((15 + Math.random() * 35) * 0.05 * 100) / 100,
          total: Math.round((15 + Math.random() * 35) * 100) / 100,
        },
      } as any;
      
      // Simulate network delay
      await new Promise(resolve => setTimeout(resolve, 800));
      return mockTrip;
    }

    try {
      const response: ApiResponse<{ trip: ExtendedTrip }> = await apiService.post(
        API_CONFIG.ENDPOINTS.TRIPS.COMPLETE,
        {
          ...request,
          completedAt: new Date(),
        }
      );

      if (response.success && response.data) {
        // Clear relevant caches
        apiService.clearCache('trip-history');
        apiService.clearCache('earnings');
        return response.data.trip;
      } else {
        throw new Error(response.message || 'Failed to complete trip');
      }
    } catch (error: any) {
      throw new Error(error.message || 'Network error completing trip');
    }
  }

  /**
   * Get trip history
   */
  async getTripHistory(query: TripHistoryQuery): Promise<TripHistoryResponse> {
    // Force mock mode for trip service (only auth uses real API)
    if (API_CONFIG.FORCE_MOCK_NON_AUTH || API_CONFIG.FORCE_MOCK_MODE || API_CONFIG.DISABLE_API_CALLS) {
      console.log('🎭 Using mock trip history');
      const trips = mockData.generateTrips();
      return {
        trips: trips.map(trip => ({
          id: trip.id,
          rideId: trip.rideId,
          ride: {
            id: trip.rideId,
            passengerId: `passenger-${Math.floor(Math.random() * 100)}`,
            passengerName: ['Emma Johnson', 'Michael Chen', 'Sarah Williams'][Math.floor(Math.random() * 3)],
            passengerPhone: '******-0000',
            pickupLocation: {
              latitude: 37.7749 + (Math.random() - 0.5) * 0.1,
              longitude: -122.4194 + (Math.random() - 0.5) * 0.1,
              address: 'Pickup Location, San Francisco, CA',
            },
            dropoffLocation: {
              latitude: 37.7749 + (Math.random() - 0.5) * 0.1,
              longitude: -122.4194 + (Math.random() - 0.5) * 0.1,
              address: 'Dropoff Location, San Francisco, CA',
            },
            status: 'completed' as const,
            bookingStatus: 'new' as const,
            fare: trip.earnings,
            distance: trip.distance,
            duration: trip.duration,
            paymentMethod: 'card' as const,
            createdAt: trip.completedAt,
            priority: 'medium' as const,
            transferType: 'arrival' as const,
            vehicleType: 'saloon' as const,
            passengerCount: 1,
            hasMeetAndGreet: false,
            airport: 'San Francisco International (SFO)',
          },
          startTime: trip.completedAt,
          endTime: new Date(trip.completedAt.getTime() + trip.duration * 60 * 1000),
          route: undefined,
          rating: { score: 4.8 },
          payment: {
            method: 'card' as const,
            status: 'completed' as const,
            baseFare: trip.earnings * 0.8,
            tips: trip.earnings * 0.15,
            surge: 0,
            fees: trip.earnings * 0.05,
            total: trip.earnings,
          },
          issues: undefined,
        } as any)),
        total: trips.length,
        hasMore: false,
        summary: {
          totalTrips: trips.length,
          totalEarnings: trips.reduce((sum, t) => sum + t.earnings, 0),
          totalDistance: trips.reduce((sum, t) => sum + t.distance, 0),
          totalDuration: trips.reduce((sum, t) => sum + t.duration, 0),
          averageRating: 4.8,
        },
      };
    }

    try {
      const response: ApiResponse<TripHistoryResponse> = await apiService.get(
        API_CONFIG.ENDPOINTS.TRIPS.HISTORY,
        {
          params: query,
          cacheKey: `trip-history-${query.driverId}-${JSON.stringify(query)}`,
          cacheDuration: API_CONFIG.CACHE_DURATION.MEDIUM,
        }
      );

      if (response.success && response.data) {
        return response.data;
      } else {
        throw new Error(response.message || 'Failed to fetch trip history');
      }
    } catch (error: any) {
      // Fallback to empty trip history for development
      if (API_CONFIG.isDevelopment) {
        console.warn('API get trip history failed, using empty mock data for development');
        return {
          trips: [],
          total: 0,
          hasMore: false,
          summary: {
            totalTrips: 0,
            totalEarnings: 0,
            totalDistance: 0,
            totalDuration: 0,
            averageRating: 0,
          },
        };
      }
      throw new Error(error.message || 'Network error fetching trip history');
    }
  }

  /**
   * Get earnings data
   */
  async getEarnings(query: EarningsQuery): Promise<EarningsResponse> {
    // Force mock mode for trip service (only auth uses real API)
    if (API_CONFIG.FORCE_MOCK_NON_AUTH || API_CONFIG.FORCE_MOCK_MODE || API_CONFIG.DISABLE_API_CALLS) {
      console.log('🎭 Using mock earnings data');
      return this.getMockEarnings(query);
    }

    try {
      const response: ApiResponse<EarningsResponse> = await apiService.get(
        API_CONFIG.ENDPOINTS.TRIPS.EARNINGS(query.period),
        {
          params: query,
          cacheKey: `earnings-${query.driverId}-${query.period}`,
          cacheDuration: API_CONFIG.CACHE_DURATION.MEDIUM,
        }
      );

      if (response.success && response.data) {
        return response.data;
      } else {
        throw new Error(response.message || 'Failed to fetch earnings');
      }
    } catch (error: any) {
      // Fallback to mock earnings data for development
      if (API_CONFIG.isDevelopment) {
        console.warn('API get earnings failed, using mock earnings data for development');
        return this.getMockEarnings(query);
      }
      throw new Error(error.message || 'Network error fetching earnings');
    }
  }

  private getMockEarnings(query: EarningsQuery): EarningsResponse {
    const mockEarnings = {
      today: 180.50,
      week: 850.75,
      month: 3200.25,
      year: 38500.00
    };

    const earnings = mockEarnings[query.period] || mockEarnings.today;
    
    return {
      period: query.period,
      totalEarnings: earnings,
      breakdown: {
        baseFares: earnings * 0.7,
        tips: earnings * 0.15,
        bonuses: earnings * 0.05,
        surge: earnings * 0.08,
        fees: earnings * -0.03,
        deductions: earnings * -0.05,
      },
      tripCount: Math.floor(earnings / 25),
      onlineHours: Math.floor(earnings / 20),
      metrics: {
        earningsPerTrip: earnings / Math.floor(earnings / 25),
        earningsPerHour: earnings / Math.floor(earnings / 20),
        averageDistance: 8.5,
        averageDuration: 22,
        averageRating: 4.7,
      },
      chartData: [],
    };
  }

  /**
   * Get earnings analytics
   */
  async getEarningsAnalytics(driverId: string, period: string = 'month'): Promise<EarningsAnalytics> {
    try {
      const response: ApiResponse<EarningsAnalytics> = await apiService.get(
        API_CONFIG.ENDPOINTS.TRIPS.ANALYTICS(period),
        {
          params: { driverId },
          cacheKey: `earnings-analytics-${driverId}-${period}`,
          cacheDuration: API_CONFIG.CACHE_DURATION.LONG,
        }
      );

      if (response.success && response.data) {
        return response.data;
      } else {
        throw new Error(response.message || 'Failed to fetch earnings analytics');
      }
    } catch (error: any) {
      throw new Error(error.message || 'Network error fetching earnings analytics');
    }
  }

  /**
   * Get trip details by ID
   */
  async getTripDetails(tripId: string): Promise<ExtendedTrip> {
    try {
      const response: ApiResponse<{ trip: ExtendedTrip }> = await apiService.get(
        `/trips/${tripId}`,
        {
          cacheKey: `trip-details-${tripId}`,
          cacheDuration: API_CONFIG.CACHE_DURATION.LONG,
        }
      );

      if (response.success && response.data) {
        return response.data.trip;
      } else {
        throw new Error(response.message || 'Trip not found');
      }
    } catch (error: any) {
      throw new Error(error.message || 'Network error fetching trip details');
    }
  }

  /**
   * Export trip data (for tax/accounting purposes)
   */
  async exportTripData(
    driverId: string,
    format: 'csv' | 'pdf' | 'xlsx',
    dateRange: { start: Date; end: Date },
    filters?: Partial<TripFilters>
  ): Promise<{ downloadUrl: string; expires: Date }> {
    try {
      const response: ApiResponse<{ downloadUrl: string; expires: Date }> = await apiService.post(
        '/trips/export',
        {
          driverId,
          format,
          dateRange,
          filters,
        }
      );

      if (response.success && response.data) {
        return response.data;
      } else {
        throw new Error(response.message || 'Failed to export trip data');
      }
    } catch (error: any) {
      throw new Error(error.message || 'Network error exporting trip data');
    }
  }

  /**
   * Get trip statistics
   */
  async getTripStatistics(driverId: string, period: 'week' | 'month' | 'year' = 'month'): Promise<{
    totalTrips: number;
    completedTrips: number;
    cancelledTrips: number;
    totalEarnings: number;
    totalDistance: number;
    totalDuration: number;
    averageRating: number;
    topRoutes: Array<{
      from: string;
      to: string;
      count: number;
      averageEarnings: number;
    }>;
    busyHours: Array<{
      hour: number;
      tripCount: number;
      averageEarnings: number;
    }>;
    vehicleTypeStats: Array<{
      type: string;
      count: number;
      earnings: number;
    }>;
  }> {
    try {
      const response: ApiResponse<any> = await apiService.get(
        `/trips/statistics`,
        {
          params: { driverId, period },
          cacheKey: `trip-statistics-${driverId}-${period}`,
          cacheDuration: API_CONFIG.CACHE_DURATION.LONG,
        }
      );

      if (response.success && response.data) {
        return response.data;
      } else {
        throw new Error(response.message || 'Failed to fetch trip statistics');
      }
    } catch (error: any) {
      throw new Error(error.message || 'Network error fetching trip statistics');
    }
  }

  /**
   * Submit trip rating and feedback
   */
  async rateTripPassenger(
    tripId: string,
    rating: number,
    feedback?: string,
    tags?: string[]
  ): Promise<void> {
    try {
      const response: ApiResponse<{ message: string }> = await apiService.post(
        `/trips/${tripId}/rate-passenger`,
        {
          rating,
          feedback,
          tags,
          timestamp: new Date(),
        }
      );

      if (!response.success) {
        throw new Error(response.message || 'Failed to submit rating');
      }

      // Clear trip cache to refresh data
      apiService.clearCache(`trip-details-${tripId}`);
    } catch (error: any) {
      throw new Error(error.message || 'Network error submitting rating');
    }
  }

  /**
   * Report trip issue
   */
  async reportTripIssue(
    tripId: string,
    issue: {
      type: 'payment' | 'route' | 'passenger' | 'vehicle' | 'other';
      description: string;
      severity: 'low' | 'medium' | 'high';
      imageUrls?: string[];
      metadata?: Record<string, any>;
    }
  ): Promise<{ ticketId: string }> {
    try {
      const response: ApiResponse<{ ticketId: string }> = await apiService.post(
        `/trips/${tripId}/report-issue`,
        {
          ...issue,
          timestamp: new Date(),
        }
      );

      if (response.success && response.data) {
        return response.data;
      } else {
        throw new Error(response.message || 'Failed to report issue');
      }
    } catch (error: any) {
      throw new Error(error.message || 'Network error reporting issue');
    }
  }

  /**
   * Search trips with advanced filters
   */
  async searchTrips(
    driverId: string,
    filters: Partial<TripFilters>,
    options: {
      limit?: number;
      offset?: number;
      sortBy?: 'date' | 'earnings' | 'distance' | 'duration' | 'rating';
      sortOrder?: 'asc' | 'desc';
    } = {}
  ): Promise<TripHistoryResponse> {
    try {
      const response: ApiResponse<TripHistoryResponse> = await apiService.post(
        '/trips/search',
        {
          driverId,
          filters,
          ...options,
        }
      );

      if (response.success && response.data) {
        return response.data;
      } else {
        throw new Error(response.message || 'Failed to search trips');
      }
    } catch (error: any) {
      throw new Error(error.message || 'Network error searching trips');
    }
  }

  /**
   * Get earnings summary for dashboard
   */
  async getEarningsSummary(driverId: string): Promise<{
    today: number;
    week: number;
    month: number;
    year: number;
    todayTrips: number;
    weekTrips: number;
    monthTrips: number;
    yearTrips: number;
    todayHours: number;
    weekHours: number;
    monthHours: number;
    yearHours: number;
  }> {
    // Force mock mode for trip service (only auth uses real API)
    if (API_CONFIG.FORCE_MOCK_NON_AUTH || API_CONFIG.FORCE_MOCK_MODE || API_CONFIG.DISABLE_API_CALLS) {
      console.log('🎭 Using mock earnings summary');
      return mockData.generateEarningsSummary();
    }

    try {
      const response: ApiResponse<any> = await apiService.get(
        '/trips/earnings-summary',
        {
          params: { driverId },
          cacheKey: `earnings-summary-${driverId}`,
          cacheDuration: API_CONFIG.CACHE_DURATION.SHORT,
        }
      );

      if (response.success && response.data) {
        return response.data;
      } else {
        throw new Error(response.message || 'Failed to fetch earnings summary');
      }
    } catch (error: any) {
      // Fallback to mock earnings summary for development
      if (API_CONFIG.isDevelopment) {
        console.warn('API get earnings summary failed, using mock data for development');
        return {
          today: 180.50,
          week: 850.75,
          month: 3200.25,
          year: 38500.00,
          todayTrips: 8,
          weekTrips: 35,
          monthTrips: 142,
          yearTrips: 1680,
          todayHours: 9,
          weekHours: 42,
          monthHours: 168,
          yearHours: 2016,
        };
      }
      throw new Error(error.message || 'Network error fetching earnings summary');
    }
  }

  /**
   * Calculate distance between two locations
   */
  static calculateDistance(from: Location, to: Location): number {
    const R = 6371; // Earth's radius in km
    const dLat = (to.latitude - from.latitude) * Math.PI / 180;
    const dLon = (to.longitude - from.longitude) * Math.PI / 180;
    const a = 
      Math.sin(dLat/2) * Math.sin(dLat/2) +
      Math.cos(from.latitude * Math.PI / 180) * Math.cos(to.latitude * Math.PI / 180) * 
      Math.sin(dLon/2) * Math.sin(dLon/2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
    return R * c;
  }

  /**
   * Format earnings for display
   */
  static formatEarnings(amount: number, currency: string = '£'): string {
    return `${currency}${amount.toFixed(2)}`;
  }

  /**
   * Format duration for display
   */
  static formatDuration(minutes: number): string {
    if (minutes < 60) {
      return `${minutes}m`;
    }
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    return mins > 0 ? `${hours}h ${mins}m` : `${hours}h`;
  }

  /**
   * Format distance for display
   */
  static formatDistance(kilometers: number): string {
    if (kilometers < 1) {
      return `${Math.round(kilometers * 1000)}m`;
    }
    return `${kilometers.toFixed(1)}km`;
  }

  /**
   * Get trip efficiency score
   */
  static calculateEfficiencyScore(trip: ExtendedTrip): {
    score: number;
    factors: {
      timeEfficiency: number;
      routeEfficiency: number;
      earningsEfficiency: number;
    };
  } {
    const timeEfficiency = trip.ride.duration > 0 ? 
      Math.min(100, (trip.ride.duration / trip.duration) * 100) : 100;
    
    const routeEfficiency = trip.route && trip.route.actualDistance > 0 ? 
      Math.min(100, (trip.ride.distance / trip.route.actualDistance) * 100) : 100;
    
    const expectedEarnings = trip.ride.fare;
    const actualEarnings = trip.earnings;
    const earningsEfficiency = expectedEarnings > 0 ? 
      Math.min(100, (actualEarnings / expectedEarnings) * 100) : 100;

    const score = (timeEfficiency + routeEfficiency + earningsEfficiency) / 3;

    return {
      score: Math.round(score),
      factors: {
        timeEfficiency: Math.round(timeEfficiency),
        routeEfficiency: Math.round(routeEfficiency),
        earningsEfficiency: Math.round(earningsEfficiency),
      },
    };
  }
}

// Export singleton instance
export const tripService = TripService.getInstance();
export default TripService;