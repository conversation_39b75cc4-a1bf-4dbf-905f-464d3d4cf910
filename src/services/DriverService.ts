import { apiService, ApiResponse } from './ApiService';
import { API_CONFIG } from '../config/api.config';
import { Driver, Location, DriverStats } from '../types';
import { mockData } from '../data/mockData';

// Driver service types
export interface UpdateDriverProfileRequest {
  name?: string;
  phone?: string;
  email?: string;
  vehicleType?: string;
  licensePlate?: string;
  profilePhoto?: string;
}

export interface UpdateDriverStatusRequest {
  isOnline: boolean;
  location?: Location;
  reason?: 'break' | 'shift_start' | 'shift_end' | 'maintenance' | 'personal';
  estimatedOfflineMinutes?: number;
}

export interface UpdateDriverLocationRequest {
  location: Location;
  heading?: number; // degrees, 0-360
  speed?: number; // km/h
  accuracy?: number; // meters
  timestamp?: Date;
}

export interface DriverPerformanceResponse {
  stats: DriverStats;
  trends: {
    period: 'daily' | 'weekly' | 'monthly';
    data: {
      date: string;
      trips: number;
      earnings: number;
      onlineHours: number;
      acceptanceRate: number;
    }[];
  };
  rankings: {
    overall: number;
    region: number;
    totalDrivers: number;
    percentile: number;
  };
}

export interface DriverDocumentRequest {
  type: 'driving_license' | 'insurance' | 'vehicle_registration' | 'background_check' | 'profile_photo';
  documentUrl: string;
  expiryDate?: Date;
  metadata?: Record<string, any>;
}

export interface DriverDocument {
  id: string;
  type: 'driving_license' | 'insurance' | 'vehicle_registration' | 'background_check' | 'profile_photo';
  status: 'pending' | 'approved' | 'rejected' | 'expired';
  documentUrl: string;
  uploadedAt: Date;
  approvedAt?: Date;
  expiryDate?: Date;
  rejectionReason?: string;
  metadata?: Record<string, any>;
}

export interface DriverEarningsBreakdown {
  period: 'today' | 'week' | 'month' | 'year';
  totalEarnings: number;
  baseFares: number;
  tips: number;
  bonuses: number;
  surge: number;
  deductions: number;
  netEarnings: number;
  tripCount: number;
  averagePerTrip: number;
  onlineHours: number;
  earningsPerHour: number;
}

export interface DriverZone {
  id: string;
  name: string;
  type: 'airport' | 'city_center' | 'residential' | 'business' | 'entertainment';
  coordinates: Location[];
  demandLevel: 'low' | 'medium' | 'high' | 'surge';
  surgeMultiplier?: number;
  estimatedWaitTime: number;
  activeDrivers: number;
  queuePosition?: number;
}

export interface DriverShiftData {
  id: string;
  startTime: Date;
  endTime?: Date;
  status: 'active' | 'break' | 'ended';
  totalOnlineMinutes: number;
  totalBreakMinutes: number;
  totalTrips: number;
  totalEarnings: number;
  startLocation: Location;
  endLocation?: Location;
}

class DriverService {
  private static instance: DriverService;
  private locationUpdateInterval: NodeJS.Timeout | null = null;
  private statusUpdateCallbacks: Set<(status: boolean) => void> = new Set();

  public static getInstance(): DriverService {
    if (!DriverService.instance) {
      DriverService.instance = new DriverService();
    }
    return DriverService.instance;
  }

  /**
   * Get driver profile
   */
  async getProfile(driverId: string): Promise<Driver> {
    // Force mock mode for driver service (only auth uses real API)
    if (API_CONFIG.FORCE_MOCK_NON_AUTH || API_CONFIG.FORCE_MOCK_MODE || API_CONFIG.DISABLE_API_CALLS) {
      console.log('🎭 Using mock driver profile');
      return mockData.generateDriver(driverId);
    }

    try {
      const response: ApiResponse<{ driver: Driver }> = await apiService.get(
        API_CONFIG.ENDPOINTS.DRIVER.PROFILE,
        {
          params: { driverId },
          cacheKey: `driver-profile-${driverId}`,
          cacheDuration: API_CONFIG.CACHE_DURATION.MEDIUM,
        }
      );

      if (response.success && response.data) {
        return response.data.driver;
      } else {
        throw new Error(response.message || 'Failed to fetch driver profile');
      }
    } catch (error: any) {
      // Fallback to mock driver profile for development
      if (API_CONFIG.isDevelopment) {
        console.warn('API get driver profile failed, using mock profile for development');
        return {
          id: driverId,
          name: 'John Driver',
          email: '<EMAIL>',
          phone: '******-0123',
          isOnline: true,
          location: {
            latitude: 37.7749,
            longitude: -122.4194,
            address: 'San Francisco, CA',
          },
          vehicleType: 'sedan',
          licensePlate: 'ABC123',
          profilePhoto: '',
          rating: 4.8,
          totalTrips: 1250,
          joinDate: new Date('2023-01-15'),
        };
      }
      throw new Error(error.message || 'Network error fetching driver profile');
    }
  }

  /**
   * Update driver profile
   */
  async updateProfile(driverId: string, updates: UpdateDriverProfileRequest): Promise<Driver> {
    try {
      const response: ApiResponse<{ driver: Driver }> = await apiService.put(
        API_CONFIG.ENDPOINTS.DRIVER.PROFILE,
        { driverId, ...updates }
      );

      if (response.success && response.data) {
        // Clear cached profile
        apiService.clearCache(`driver-profile-${driverId}`);
        return response.data.driver;
      } else {
        throw new Error(response.message || 'Failed to update driver profile');
      }
    } catch (error: any) {
      throw new Error(error.message || 'Network error updating driver profile');
    }
  }

  /**
   * Update driver online/offline status
   */
  async updateStatus(driverId: string, request: UpdateDriverStatusRequest): Promise<void> {
    // Force mock mode for driver service (only auth uses real API)
    if (API_CONFIG.FORCE_MOCK_NON_AUTH || API_CONFIG.FORCE_MOCK_MODE || API_CONFIG.DISABLE_API_CALLS) {
      console.log('🎭 Mock: Updating driver status to', request.isOnline ? 'online' : 'offline');
      // Simulate network delay
      await new Promise(resolve => setTimeout(resolve, 300));
      
      // Notify status change listeners
      this.notifyStatusUpdate(request.isOnline);
      return;
    }

    try {
      const response: ApiResponse<{ message: string; timestamp: Date }> = await apiService.put(
        API_CONFIG.ENDPOINTS.DRIVER.STATUS,
        { driverId, ...request }
      );

      if (response.success) {
        // Notify status change listeners
        this.notifyStatusUpdate(request.isOnline);
        
        // Clear relevant caches
        apiService.clearCache(`driver-profile-${driverId}`);
      } else {
        throw new Error(response.message || 'Failed to update driver status');
      }
    } catch (error: any) {
      throw new Error(error.message || 'Network error updating driver status');
    }
  }

  /**
   * Update driver location
   */
  async updateLocation(driverId: string, request: UpdateDriverLocationRequest): Promise<void> {
    // Force mock mode for driver service (only auth uses real API)
    if (API_CONFIG.FORCE_MOCK_NON_AUTH || API_CONFIG.FORCE_MOCK_MODE || API_CONFIG.DISABLE_API_CALLS) {
      // Mock location update - just return success silently
      return;
    }

    try {
      const response: ApiResponse<{ message: string }> = await apiService.put(
        API_CONFIG.ENDPOINTS.DRIVER.LOCATION,
        {
          driverId,
          ...request,
          timestamp: request.timestamp || new Date(),
        }
      );

      if (!response.success) {
        throw new Error(response.message || 'Failed to update driver location');
      }
    } catch (error: any) {
      // Location updates should not throw - log and continue
      console.warn('Failed to update driver location:', error.message);
    }
  }

  /**
   * Start continuous location updates
   */
  startLocationUpdates(
    driverId: string,
    getCurrentLocation: () => Promise<Location | null>,
    intervalMs: number = API_CONFIG.LOCATION.UPDATE_INTERVAL
  ): void {
    if (this.locationUpdateInterval) {
      this.stopLocationUpdates();
    }

    const updateLocation = async () => {
      try {
        const location = await getCurrentLocation();
        if (location) {
          await this.updateLocation(driverId, { location });
        }
      } catch (error) {
        console.warn('Location update failed:', error);
      }
    };

    // Initial update
    updateLocation();

    // Set up interval
    this.locationUpdateInterval = setInterval(updateLocation, intervalMs);
  }

  /**
   * Stop continuous location updates
   */
  stopLocationUpdates(): void {
    if (this.locationUpdateInterval) {
      clearInterval(this.locationUpdateInterval);
      this.locationUpdateInterval = null;
    }
  }

  /**
   * Get driver performance stats
   */
  async getPerformance(driverId: string, period: 'week' | 'month' | 'year' = 'week'): Promise<DriverPerformanceResponse> {
    try {
      const response: ApiResponse<DriverPerformanceResponse> = await apiService.get(
        API_CONFIG.ENDPOINTS.DRIVER.PERFORMANCE,
        {
          params: { driverId, period },
          cacheKey: `driver-performance-${driverId}-${period}`,
          cacheDuration: API_CONFIG.CACHE_DURATION.MEDIUM,
        }
      );

      if (response.success && response.data) {
        return response.data;
      } else {
        throw new Error(response.message || 'Failed to fetch driver performance');
      }
    } catch (error: any) {
      throw new Error(error.message || 'Network error fetching driver performance');
    }
  }

  /**
   * Get driver stats
   */
  async getStats(driverId: string): Promise<DriverStats> {
    // Force mock mode for driver service (only auth uses real API)
    if (API_CONFIG.FORCE_MOCK_NON_AUTH || API_CONFIG.FORCE_MOCK_MODE || API_CONFIG.DISABLE_API_CALLS) {
      console.log('🎭 Using mock driver stats');
      return mockData.generateDriverStats();
    }

    try {
      const response: ApiResponse<DriverStats> = await apiService.get(
        API_CONFIG.ENDPOINTS.DRIVER.STATS,
        {
          params: { driverId },
          cacheKey: `driver-stats-${driverId}`,
          cacheDuration: API_CONFIG.CACHE_DURATION.MEDIUM,
        }
      );

      if (response.success && response.data) {
        return response.data;
      } else {
        throw new Error(response.message || 'Failed to fetch driver stats');
      }
    } catch (error: any) {
      // Fallback to mock driver stats for development
      if (API_CONFIG.isDevelopment) {
        console.warn('API get driver stats failed, using mock stats for development');
        return {
          totalTrips: 1250,
          totalEarnings: 38500.00,
          acceptanceRate: 92.5,
          cancellationRate: 2.1,
          onlineHours: 2016,
          averageRating: 4.8,
        };
      }
      throw new Error(error.message || 'Network error fetching driver stats');
    }
  }

  /**
   * Upload driver profile photo
   */
  async uploadProfilePhoto(driverId: string, photoFile: File | Blob): Promise<string> {
    try {
      const response: ApiResponse<{ photoUrl: string }> = await apiService.uploadFile(
        API_CONFIG.ENDPOINTS.DRIVER.PHOTO,
        photoFile,
        'photo',
        (progress) => {
          // Could emit progress events here
          console.log(`Upload progress: ${progress}%`);
        }
      );

      if (response.success && response.data) {
        // Clear cached profile
        apiService.clearCache(`driver-profile-${driverId}`);
        return response.data.photoUrl;
      } else {
        throw new Error(response.message || 'Failed to upload profile photo');
      }
    } catch (error: any) {
      throw new Error(error.message || 'Network error uploading profile photo');
    }
  }

  /**
   * Get driver documents
   */
  async getDocuments(driverId: string): Promise<DriverDocument[]> {
    try {
      const response: ApiResponse<{ documents: DriverDocument[] }> = await apiService.get(
        API_CONFIG.ENDPOINTS.DRIVER.DOCUMENTS,
        {
          params: { driverId },
          cacheKey: `driver-documents-${driverId}`,
          cacheDuration: API_CONFIG.CACHE_DURATION.LONG,
        }
      );

      if (response.success && response.data) {
        return response.data.documents;
      } else {
        throw new Error(response.message || 'Failed to fetch driver documents');
      }
    } catch (error: any) {
      throw new Error(error.message || 'Network error fetching driver documents');
    }
  }

  /**
   * Upload driver document
   */
  async uploadDocument(driverId: string, document: DriverDocumentRequest): Promise<DriverDocument> {
    try {
      const response: ApiResponse<{ document: DriverDocument }> = await apiService.post(
        API_CONFIG.ENDPOINTS.DRIVER.DOCUMENTS,
        { driverId, ...document }
      );

      if (response.success && response.data) {
        // Clear documents cache
        apiService.clearCache(`driver-documents-${driverId}`);
        return response.data.document;
      } else {
        throw new Error(response.message || 'Failed to upload document');
      }
    } catch (error: any) {
      throw new Error(error.message || 'Network error uploading document');
    }
  }

  /**
   * Get earnings breakdown
   */
  async getEarningsBreakdown(driverId: string, period: 'today' | 'week' | 'month' | 'year'): Promise<DriverEarningsBreakdown> {
    try {
      const response: ApiResponse<DriverEarningsBreakdown> = await apiService.get(
        `/driver/earnings/${period}`,
        {
          params: { driverId },
          cacheKey: `driver-earnings-${driverId}-${period}`,
          cacheDuration: API_CONFIG.CACHE_DURATION.MEDIUM,
        }
      );

      if (response.success && response.data) {
        return response.data;
      } else {
        throw new Error(response.message || 'Failed to fetch earnings breakdown');
      }
    } catch (error: any) {
      throw new Error(error.message || 'Network error fetching earnings breakdown');
    }
  }

  /**
   * Get available driver zones
   */
  async getAvailableZones(location: Location): Promise<DriverZone[]> {
    try {
      const response: ApiResponse<{ zones: DriverZone[] }> = await apiService.get(
        '/driver/zones',
        {
          params: {
            latitude: location.latitude,
            longitude: location.longitude,
          },
          cacheKey: `driver-zones-${location.latitude}-${location.longitude}`,
          cacheDuration: API_CONFIG.CACHE_DURATION.SHORT,
        }
      );

      if (response.success && response.data) {
        return response.data.zones;
      } else {
        throw new Error(response.message || 'Failed to fetch driver zones');
      }
    } catch (error: any) {
      throw new Error(error.message || 'Network error fetching driver zones');
    }
  }

  /**
   * Join a driver zone queue
   */
  async joinZoneQueue(driverId: string, zoneId: string, location: Location): Promise<{ queuePosition: number; estimatedWaitTime: number }> {
    try {
      const response: ApiResponse<{ queuePosition: number; estimatedWaitTime: number }> = await apiService.post(
        `/driver/zones/${zoneId}/join`,
        {
          driverId,
          location,
          timestamp: new Date(),
        }
      );

      if (response.success && response.data) {
        return response.data;
      } else {
        throw new Error(response.message || 'Failed to join zone queue');
      }
    } catch (error: any) {
      throw new Error(error.message || 'Network error joining zone queue');
    }
  }

  /**
   * Leave a driver zone queue
   */
  async leaveZoneQueue(driverId: string, zoneId: string): Promise<void> {
    try {
      const response: ApiResponse<{ message: string }> = await apiService.post(
        `/driver/zones/${zoneId}/leave`,
        { driverId }
      );

      if (!response.success) {
        throw new Error(response.message || 'Failed to leave zone queue');
      }
    } catch (error: any) {
      throw new Error(error.message || 'Network error leaving zone queue');
    }
  }

  /**
   * Start a new driver shift
   */
  async startShift(driverId: string, location: Location): Promise<DriverShiftData> {
    try {
      const response: ApiResponse<{ shift: DriverShiftData }> = await apiService.post(
        '/driver/shift/start',
        {
          driverId,
          startLocation: location,
          startTime: new Date(),
        }
      );

      if (response.success && response.data) {
        return response.data.shift;
      } else {
        throw new Error(response.message || 'Failed to start shift');
      }
    } catch (error: any) {
      throw new Error(error.message || 'Network error starting shift');
    }
  }

  /**
   * End driver shift
   */
  async endShift(driverId: string, location: Location): Promise<DriverShiftData> {
    try {
      const response: ApiResponse<{ shift: DriverShiftData }> = await apiService.post(
        '/driver/shift/end',
        {
          driverId,
          endLocation: location,
          endTime: new Date(),
        }
      );

      if (response.success && response.data) {
        return response.data.shift;
      } else {
        throw new Error(response.message || 'Failed to end shift');
      }
    } catch (error: any) {
      throw new Error(error.message || 'Network error ending shift');
    }
  }

  /**
   * Take a break during shift
   */
  async takeBreak(driverId: string, estimatedMinutes?: number): Promise<void> {
    try {
      const response: ApiResponse<{ message: string }> = await apiService.post(
        '/driver/shift/break',
        {
          driverId,
          estimatedMinutes,
          timestamp: new Date(),
        }
      );

      if (!response.success) {
        throw new Error(response.message || 'Failed to take break');
      }
    } catch (error: any) {
      throw new Error(error.message || 'Network error taking break');
    }
  }

  /**
   * Resume from break
   */
  async resumeFromBreak(driverId: string): Promise<void> {
    try {
      const response: ApiResponse<{ message: string }> = await apiService.post(
        '/driver/shift/resume',
        {
          driverId,
          timestamp: new Date(),
        }
      );

      if (!response.success) {
        throw new Error(response.message || 'Failed to resume from break');
      }
    } catch (error: any) {
      throw new Error(error.message || 'Network error resuming from break');
    }
  }

  /**
   * Subscribe to driver status updates
   */
  subscribeToStatusUpdates(callback: (isOnline: boolean) => void): () => void {
    this.statusUpdateCallbacks.add(callback);

    // Return unsubscribe function
    return () => {
      this.statusUpdateCallbacks.delete(callback);
    };
  }

  /**
   * Notify status update subscribers
   */
  private notifyStatusUpdate(isOnline: boolean): void {
    this.statusUpdateCallbacks.forEach(callback => {
      try {
        callback(isOnline);
      } catch (error) {
        console.error('Error in status update callback:', error);
      }
    });
  }

  /**
   * Validate driver profile data
   */
  static validateProfile(profile: Partial<Driver>): { valid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (profile.name && profile.name.length < 2) {
      errors.push('Name must be at least 2 characters long');
    }

    if (profile.phone && !/^\+?[\d\s\-\(\)]{10,}$/.test(profile.phone)) {
      errors.push('Please enter a valid phone number');
    }

    if (profile.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(profile.email)) {
      errors.push('Please enter a valid email address');
    }

    if (profile.licensePlate && profile.licensePlate.length < 3) {
      errors.push('License plate must be at least 3 characters long');
    }

    return {
      valid: errors.length === 0,
      errors,
    };
  }

  /**
   * Format driver stats for display
   */
  static formatStats(stats: DriverStats): {
    acceptanceRate: string;
    cancellationRate: string;
    onlineHours: string;
    totalEarnings: string;
    averageRating: string;
  } {
    return {
      acceptanceRate: `${stats.acceptanceRate.toFixed(1)}%`,
      cancellationRate: `${stats.cancellationRate.toFixed(1)}%`,
      onlineHours: `${stats.onlineHours.toFixed(1)}h`,
      totalEarnings: `$${stats.totalEarnings.toFixed(2)}`,
      averageRating: '4.8', // TODO: Add to DriverStats interface
    };
  }
}

// Export singleton instance
export const driverService = DriverService.getInstance();
export default DriverService;