import { apiService, ApiResponse } from './ApiService';
import { API_CONFIG } from '../config/api.config';
import { Ride, Location, Driver } from '../types';
import { mockData } from '../data/mockData';

// Ride service types
export interface AvailableRidesQuery {
  driverId: string;
  latitude: number;
  longitude: number;
  radius?: number; // in kilometers
  vehicleTypes?: string[];
  maxFare?: number;
  minFare?: number;
  limit?: number;
  offset?: number;
}

export interface AvailableRidesResponse {
  rides: Ride[];
  total: number;
  hasMore: boolean;
  nextOffset?: number;
}

export interface AcceptRideRequest {
  rideId: string;
  driverId: string;
  estimatedArrivalTime?: number;
  currentLocation: Location;
}

export interface AcceptRideResponse {
  success: boolean;
  ride: Ride;
  passengerContact?: {
    phone: string;
    allowCall: boolean;
    allowSms: boolean;
  };
}

export interface RejectRideRequest {
  rideId: string;
  driverId: string;
  reason?: 'too_far' | 'busy' | 'vehicle_issue' | 'other';
  comment?: string;
}

export interface UpdateRideStatusRequest {
  rideId: string;
  status: 'heading_to_pickup' | 'arrived_at_pickup' | 'passenger_onboard' | 'arrived_at_destination' | 'completed';
  location: Location;
  timestamp?: Date;
  notes?: string;
  imageUrls?: string[];
}

export interface RideETARequest {
  rideId: string;
  currentLocation: Location;
  includeTraffic?: boolean;
}

export interface RideETAResponse {
  estimatedArrival: number; // seconds
  distance: number; // meters
  routePolyline?: string;
  trafficDelaySeconds?: number;
}

export interface NearbyDriversQuery {
  latitude: number;
  longitude: number;
  radius: number;
  vehicleType?: string;
  limit?: number;
}

export interface NearbyDriversResponse {
  drivers: (Driver & {
    distance: number;
    eta: number;
    heading: number;
  })[];
  total: number;
}

export interface RidePreferences {
  maxRadius: number;
  minFare: number;
  preferredVehicleTypes: string[];
  autoAccept: boolean;
  autoAcceptFareThreshold?: number;
  avoidAirports: boolean;
  avoidLongDistance: boolean;
  maxDurationMinutes?: number;
}

class RideService {
  private static instance: RideService;
  private activePollingInterval: NodeJS.Timeout | null = null;
  private rideUpdateCallbacks: Map<string, (ride: Ride) => void> = new Map();

  public static getInstance(): RideService {
    if (!RideService.instance) {
      RideService.instance = new RideService();
    }
    return RideService.instance;
  }

  /**
   * Get available rides for driver
   */
  async getAvailableRides(query: AvailableRidesQuery): Promise<AvailableRidesResponse> {
    // Force mock mode for rides service (only auth uses real API)
    if (API_CONFIG.FORCE_MOCK_NON_AUTH || API_CONFIG.FORCE_MOCK_MODE || API_CONFIG.DISABLE_API_CALLS) {
      console.log('🎭 Using mock rides data');
      return this.getMockAvailableRides(query);
    }

    try {
      const response: ApiResponse<AvailableRidesResponse> = await apiService.get(
        API_CONFIG.ENDPOINTS.RIDES.AVAILABLE,
        {
          params: query,
          cacheKey: `available-rides-${query.driverId}`,
          cacheDuration: API_CONFIG.CACHE_DURATION.SHORT,
        }
      );

      if (response.success && response.data) {
        return response.data;
      } else {
        throw new Error(response.message || 'Failed to fetch available rides');
      }
    } catch (error: any) {
      // Always use mock data for non-auth services
      console.warn('Using mock data for rides service');
      return this.getMockAvailableRides(query);
    }
  }

  /**
   * Mock available rides for development/testing
   */
  private async getMockAvailableRides(query: AvailableRidesQuery): Promise<AvailableRidesResponse> {
    // Simulate network delay
    await new Promise(resolve => setTimeout(resolve, 500));

    // Use our comprehensive mock data generator
    const mockRides = mockData.generateAvailableRides();
    
    console.log(`✅ Generated ${mockRides.length} mock available rides`);
    
    // Original mock data kept for reference:
    const originalMockRides: Ride[] = [
      {
        id: 'mock-ride-1',
        passengerId: 'passenger-001',
        passengerName: 'Emma Thompson',
        passengerPhone: '+44 7700 123456',
        pickupLocation: {
          latitude: 51.4700,
          longitude: -0.4543,
          address: 'Heathrow Airport Terminal 2, Arrivals'
        },
        dropoffLocation: {
          latitude: 51.5074,
          longitude: -0.1278,
          address: 'The Ritz London, 150 Piccadilly, Mayfair'
        },
        status: 'pending',
        fare: 65.00,
        distance: 24.1,
        duration: 45,
        createdAt: new Date(),
        transferType: 'arrival',
        flightNumber: 'BA456',
        airline: 'British Airways',
        terminal: 'Terminal 2',
        flightTime: new Date(),
        flightStatus: 'landed',
        vehicleType: 'executive',
        passengerCount: 1,
        hasMeetAndGreet: true,
        airport: 'Heathrow (LHR)',
        bookingStatus: 'premium',
        paymentMethod: 'card',
        priority: 'high',
      },
      {
        id: 'mock-ride-2',
        passengerId: 'passenger-002',
        passengerName: 'James Rodriguez',
        passengerPhone: '+44 7789 234567',
        pickupLocation: {
          latitude: 51.5200,
          longitude: -0.1276,
          address: 'King\'s Cross St. Pancras International, London'
        },
        dropoffLocation: {
          latitude: 51.4700,
          longitude: -0.4543,
          address: 'Heathrow Airport Terminal 5, Departures'
        },
        status: 'pending',
        fare: 58.00,
        distance: 28.5,
        duration: 50,
        createdAt: new Date(Date.now() - 300000),
        transferType: 'departure',
        flightNumber: 'VS401',
        airline: 'Virgin Atlantic',
        terminal: 'Terminal 5',
        flightTime: new Date(Date.now() + 7200000),
        flightStatus: 'on_time',
        vehicleType: 'saloon',
        passengerCount: 2,
        hasMeetAndGreet: false,
        airport: 'Heathrow (LHR)',
        bookingStatus: 'urgent',
        paymentMethod: 'cash',
        priority: 'high',
        specialRequests: 'Child car seat required'
      },
      {
        id: 'mock-ride-3',
        passengerId: 'passenger-003',
        passengerName: 'Sarah Johnson',
        passengerPhone: '+44 7654 987321',
        pickupLocation: {
          latitude: 51.4700,
          longitude: -0.4543,
          address: 'Heathrow Airport Terminal 3, Arrivals'
        },
        dropoffLocation: {
          latitude: 51.4816,
          longitude: -0.0077,
          address: 'Canary Wharf, London E14'
        },
        status: 'pending',
        fare: 42.00,
        distance: 32.8,
        duration: 55,
        createdAt: new Date(Date.now() - 600000),
        transferType: 'arrival',
        flightNumber: 'EK7',
        airline: 'Emirates',
        terminal: 'Terminal 3',
        flightTime: new Date(Date.now() - 1800000),
        flightStatus: 'delayed',
        vehicleType: 'people_carrier',
        passengerCount: 4,
        hasMeetAndGreet: true,
        airport: 'Heathrow (LHR)',
        bookingStatus: 'scheduled',
        paymentMethod: 'card',
        priority: 'medium',
        specialRequests: 'Business travelers - please wait in arrivals with name board'
      }
    ];

    return {
      rides: mockRides, // Using our generated mock data
      total: mockRides.length,
      hasMore: false,
    };
  }

  /**
   * Accept a ride request
   */
  async acceptRide(request: AcceptRideRequest): Promise<AcceptRideResponse> {
    // Force mock mode for rides service (only auth uses real API)
    if (API_CONFIG.FORCE_MOCK_NON_AUTH || API_CONFIG.FORCE_MOCK_MODE || API_CONFIG.DISABLE_API_CALLS) {
      console.log('🎭 Mock: Accepting ride', request.rideId);
      return this.mockAcceptRide(request);
    }

    try {
      const response: ApiResponse<AcceptRideResponse> = await apiService.post(
        API_CONFIG.ENDPOINTS.RIDES.ACCEPT(request.rideId),
        {
          driverId: request.driverId,
          estimatedArrivalTime: request.estimatedArrivalTime,
          currentLocation: request.currentLocation,
        }
      );

      if (response.success && response.data) {
        // Clear available rides cache as it's now stale
        apiService.clearCache('available-rides');
        return response.data;
      } else {
        throw new Error(response.message || 'Failed to accept ride');
      }
    } catch (error: any) {
      // Fallback to mock acceptance for development
      if (API_CONFIG.isDevelopment) {
        console.warn('API accept ride failed, using mock response for development');
        return this.mockAcceptRide(request);
      }
      throw new Error(error.message || 'Network error accepting ride');
    }
  }

  /**
   * Mock ride acceptance for development/testing
   */
  private async mockAcceptRide(request: AcceptRideRequest): Promise<AcceptRideResponse> {
    // Simulate network delay
    await new Promise(resolve => setTimeout(resolve, 500));

    // Create a mock accepted ride (should be the same ride with updated status)
    const mockRide: Ride = {
      id: request.rideId,
      passengerId: 'passenger-mock',
      passengerName: 'Mock Passenger',
      passengerPhone: '+****************',
      pickupLocation: request.currentLocation,
      dropoffLocation: {
        latitude: 37.7849,
        longitude: -122.4094,
        address: 'Mock Destination, San Francisco, CA'
      },
      status: 'accepted',
      fare: 35.50,
      distance: 12.5,
      duration: 25,
      createdAt: new Date(),
      transferType: 'departure',
      flightNumber: 'UA123',
      airline: 'United Airlines',
      terminal: 'Terminal 1',
      flightTime: new Date(Date.now() + 3600000),
      flightStatus: 'on_time',
      vehicleType: 'saloon',
      passengerCount: 1,
      hasMeetAndGreet: false,
      airport: 'San Francisco (SFO)',
      bookingStatus: 'new',
      paymentMethod: 'card',
      priority: 'medium',
    };

    return {
      success: true,
      ride: mockRide,
      passengerContact: {
        phone: '+****************',
        allowCall: true,
        allowSms: true,
      }
    };
  }

  /**
   * Reject a ride request
   */
  async rejectRide(request: RejectRideRequest): Promise<void> {
    // Force mock mode for rides service (only auth uses real API)
    if (API_CONFIG.FORCE_MOCK_NON_AUTH || API_CONFIG.FORCE_MOCK_MODE || API_CONFIG.DISABLE_API_CALLS) {
      console.log('🎭 Mock: Rejecting ride', request.rideId);
      await new Promise(resolve => setTimeout(resolve, 500)); // Simulate delay
      return; // Successfully rejected
    }

    try {
      const response: ApiResponse<{ message: string }> = await apiService.post(
        API_CONFIG.ENDPOINTS.RIDES.REJECT(request.rideId),
        {
          driverId: request.driverId,
          reason: request.reason,
          comment: request.comment,
        }
      );

      if (!response.success) {
        throw new Error(response.message || 'Failed to reject ride');
      }

      // Clear available rides cache
      apiService.clearCache('available-rides');
    } catch (error: any) {
      throw new Error(error.message || 'Network error rejecting ride');
    }
  }

  /**
   * Update ride status
   */
  async updateRideStatus(request: UpdateRideStatusRequest): Promise<Ride> {
    try {
      const response: ApiResponse<{ ride: Ride }> = await apiService.put(
        API_CONFIG.ENDPOINTS.RIDES.STATUS(request.rideId),
        {
          status: request.status,
          location: request.location,
          timestamp: request.timestamp || new Date(),
          notes: request.notes,
          imageUrls: request.imageUrls,
        }
      );

      if (response.success && response.data) {
        // Notify listeners of ride update
        this.notifyRideUpdate(response.data.ride);
        return response.data.ride;
      } else {
        throw new Error(response.message || 'Failed to update ride status');
      }
    } catch (error: any) {
      throw new Error(error.message || 'Network error updating ride status');
    }
  }

  /**
   * Get estimated time of arrival for a ride
   */
  async getRideETA(request: RideETARequest): Promise<RideETAResponse> {
    try {
      const response: ApiResponse<RideETAResponse> = await apiService.post(
        API_CONFIG.ENDPOINTS.RIDES.ETA(request.rideId),
        {
          currentLocation: request.currentLocation,
          includeTraffic: request.includeTraffic ?? true,
        }
      );

      if (response.success && response.data) {
        return response.data;
      } else {
        throw new Error(response.message || 'Failed to get ETA');
      }
    } catch (error: any) {
      throw new Error(error.message || 'Network error fetching ETA');
    }
  }

  /**
   * Get nearby drivers (for passenger view or admin)
   */
  async getNearbyDrivers(query: NearbyDriversQuery): Promise<NearbyDriversResponse> {
    try {
      const response: ApiResponse<NearbyDriversResponse> = await apiService.get(
        '/rides/nearby-drivers',
        {
          params: query,
          cacheKey: `nearby-drivers-${query.latitude}-${query.longitude}`,
          cacheDuration: API_CONFIG.CACHE_DURATION.SHORT,
        }
      );

      if (response.success && response.data) {
        return response.data;
      } else {
        throw new Error(response.message || 'Failed to fetch nearby drivers');
      }
    } catch (error: any) {
      throw new Error(error.message || 'Network error fetching nearby drivers');
    }
  }

  /**
   * Get ride details by ID
   */
  async getRideDetails(rideId: string): Promise<Ride> {
    try {
      const response: ApiResponse<{ ride: Ride }> = await apiService.get(
        `/rides/${rideId}`,
        {
          cacheKey: `ride-${rideId}`,
          cacheDuration: API_CONFIG.CACHE_DURATION.SHORT,
        }
      );

      if (response.success && response.data) {
        return response.data.ride;
      } else {
        throw new Error(response.message || 'Ride not found');
      }
    } catch (error: any) {
      throw new Error(error.message || 'Network error fetching ride details');
    }
  }

  /**
   * Start polling for available rides
   */
  startPollingForRides(
    driverId: string,
    location: Location,
    onRidesUpdate: (rides: Ride[]) => void,
    intervalMs: number = 10000
  ): void {
    if (this.activePollingInterval) {
      this.stopPollingForRides();
    }

    const poll = async () => {
      try {
        const response = await this.getAvailableRides({
          driverId,
          latitude: location.latitude,
          longitude: location.longitude,
        });
        onRidesUpdate(response.rides);
      } catch (error) {
        console.error('Error polling for rides:', error);
      }
    };

    // Initial poll
    poll();

    // Set up interval
    this.activePollingInterval = setInterval(poll, intervalMs);
  }

  /**
   * Stop polling for available rides
   */
  stopPollingForRides(): void {
    if (this.activePollingInterval) {
      clearInterval(this.activePollingInterval);
      this.activePollingInterval = null;
    }
  }

  /**
   * Subscribe to ride updates
   */
  subscribeToRideUpdates(rideId: string, callback: (ride: Ride) => void): () => void {
    this.rideUpdateCallbacks.set(rideId, callback);

    // Return unsubscribe function
    return () => {
      this.rideUpdateCallbacks.delete(rideId);
    };
  }

  /**
   * Notify subscribers of ride updates
   */
  private notifyRideUpdate(ride: Ride): void {
    const callback = this.rideUpdateCallbacks.get(ride.id);
    if (callback) {
      callback(ride);
    }
  }

  /**
   * Get driver's ride preferences
   */
  async getRidePreferences(driverId: string): Promise<RidePreferences> {
    try {
      const response: ApiResponse<RidePreferences> = await apiService.get(
        `/rides/preferences/${driverId}`,
        {
          cacheKey: `ride-preferences-${driverId}`,
          cacheDuration: API_CONFIG.CACHE_DURATION.MEDIUM,
        }
      );

      if (response.success && response.data) {
        return response.data;
      } else {
        // Return default preferences if not found
        return this.getDefaultPreferences();
      }
    } catch (error) {
      return this.getDefaultPreferences();
    }
  }

  /**
   * Update driver's ride preferences
   */
  async updateRidePreferences(driverId: string, preferences: Partial<RidePreferences>): Promise<RidePreferences> {
    try {
      const response: ApiResponse<RidePreferences> = await apiService.put(
        `/rides/preferences/${driverId}`,
        preferences
      );

      if (response.success && response.data) {
        // Clear cache
        apiService.clearCache(`ride-preferences-${driverId}`);
        return response.data;
      } else {
        throw new Error(response.message || 'Failed to update preferences');
      }
    } catch (error: any) {
      throw new Error(error.message || 'Network error updating preferences');
    }
  }

  /**
   * Cancel an active ride (emergency use)
   */
  async cancelRide(rideId: string, reason: string, driverId: string): Promise<void> {
    try {
      const response: ApiResponse<{ message: string }> = await apiService.post(
        `/rides/${rideId}/cancel`,
        {
          reason,
          driverId,
          timestamp: new Date(),
        }
      );

      if (!response.success) {
        throw new Error(response.message || 'Failed to cancel ride');
      }
    } catch (error: any) {
      throw new Error(error.message || 'Network error canceling ride');
    }
  }

  /**
   * Report an issue with a ride
   */
  async reportRideIssue(
    rideId: string,
    issue: {
      type: 'passenger_no_show' | 'wrong_location' | 'safety_concern' | 'payment_issue' | 'other';
      description: string;
      imageUrls?: string[];
    }
  ): Promise<void> {
    try {
      const response: ApiResponse<{ ticketId: string }> = await apiService.post(
        `/rides/${rideId}/report-issue`,
        {
          ...issue,
          timestamp: new Date(),
        }
      );

      if (!response.success) {
        throw new Error(response.message || 'Failed to report issue');
      }
    } catch (error: any) {
      throw new Error(error.message || 'Network error reporting issue');
    }
  }

  /**
   * Get default ride preferences
   */
  private getDefaultPreferences(): RidePreferences {
    return {
      maxRadius: 10, // 10km
      minFare: 5.00,
      preferredVehicleTypes: ['saloon', 'estate'],
      autoAccept: false,
      avoidAirports: false,
      avoidLongDistance: false,
    };
  }

  /**
   * Calculate distance between two locations (Haversine formula)
   */
  static calculateDistance(loc1: Location, loc2: Location): number {
    const R = 6371; // Earth's radius in km
    const dLat = (loc2.latitude - loc1.latitude) * Math.PI / 180;
    const dLon = (loc2.longitude - loc1.longitude) * Math.PI / 180;
    const a = 
      Math.sin(dLat/2) * Math.sin(dLat/2) +
      Math.cos(loc1.latitude * Math.PI / 180) * Math.cos(loc2.latitude * Math.PI / 180) * 
      Math.sin(dLon/2) * Math.sin(dLon/2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
    return R * c;
  }

  /**
   * Filter rides based on preferences
   */
  static filterRidesByPreferences(rides: Ride[], preferences: RidePreferences, driverLocation: Location): Ride[] {
    return rides.filter(ride => {
      // Check minimum fare
      if (ride.fare < preferences.minFare) {
        return false;
      }

      // Check vehicle type preference
      if (preferences.preferredVehicleTypes.length > 0 && 
          !preferences.preferredVehicleTypes.includes(ride.vehicleType)) {
        return false;
      }

      // Check distance
      const distance = RideService.calculateDistance(driverLocation, ride.pickupLocation);
      if (distance > preferences.maxRadius) {
        return false;
      }

      // Check airport preference
      if (preferences.avoidAirports && ride.airport) {
        return false;
      }

      // Check duration preference
      if (preferences.maxDurationMinutes && ride.duration > preferences.maxDurationMinutes) {
        return false;
      }

      return true;
    });
  }
}

// Export singleton instance
export const rideService = RideService.getInstance();
export default RideService;